import React, { useState, useEffect } from 'react'
import {
  Card, Button, Table, Tag, Space, Modal, Form, Select,
  InputNumber, message, Row, Col, Statistic, Tabs,
  Descriptions, Tooltip, Alert, List, Progress, Input,
  Checkbox, Divider
} from 'antd'
import {
  <PERSON><PERSON><PERSON>cleOutlined, <PERSON>Outlined, <PERSON>Outlined,
  <PERSON>Outlined, <PERSON>Outlined, <PERSON><PERSON>Outlined,
  <PERSON>TextOutlined, ReloadOutlined, <PERSON><PERSON><PERSON><PERSON>utlined,
  <PERSON><PERSON><PERSON>Outlined, <PERSON><PERSON><PERSON>Outlined, <PERSON>Outlined,
  ClockCircleOutlined, DollarOutlined
} from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import axios from 'axios'

const { TabPane } = Tabs
const { TextArea } = Input

interface RouteOptimizationData {
  id: number
  project_id: number
  optimization_type: string
  total_routes: number
  total_distance?: number
  total_time?: number
  total_cost?: number
  optimization_score?: number
  optimized_at: string
}

interface TransportTask {
  task_id?: string
  origin: string
  destination: string
  cargo_volume: number
  cargo_weight: number
  priority: number
  time_window?: number[]
  vehicle_requirements?: string[]
}

interface Vehicle {
  vehicle_id?: string
  vehicle_type: string
  capacity: number
  max_speed: number
  fuel_consumption: number
  operating_cost: number
  weight: number
  dimensions: number[]
}

interface PathResult {
  success: boolean
  path?: string[]
  total_distance?: number
  total_cost?: number
  algorithm_used?: string
  computation_time?: number
  path_quality?: any
  message?: string
}

const RouteOptimization: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [optimizationList, setOptimizationList] = useState<RouteOptimizationData[]>([])
  const [selectedOptimization, setSelectedOptimization] = useState<RouteOptimizationData | null>(null)
  const [pathResult, setPathResult] = useState<PathResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [optimizing, setOptimizing] = useState(false)
  const [optimizationModalVisible, setOptimizationModalVisible] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [pathModalVisible, setPathModalVisible] = useState(false)
  const [networkAnalysisVisible, setNetworkAnalysisVisible] = useState(false)
  const [networkAnalysis, setNetworkAnalysis] = useState<any>(null)
  const [form] = Form.useForm()
  const [pathForm] = Form.useForm()

  // 获取路线优化列表
  const fetchOptimizationList = async () => {
    if (!projectId) return

    setLoading(true)
    try {
      const response = await axios.get(`/api/v1/routes/${projectId}/optimizations`)
      setOptimizationList(response.data)
    } catch (error) {
      message.error('获取路线优化列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchOptimizationList()
  }, [projectId])

  // 开始路线优化
  const handleStartOptimization = async () => {
    try {
      const values = await form.validateFields()
      setOptimizing(true)

      const optimizationParams = {
        tasks: values.tasks || [],
        vehicles: values.vehicles || [],
        objectives: values.objectives || ['minimize_distance', 'minimize_time'],
        optimization_type: values.optimization_type
      }

      const response = await axios.post(
        `/api/v1/routes/${projectId}/optimize`,
        optimizationParams
      )

      message.success('路线优化完成')
      setOptimizationModalVisible(false)
      form.resetFields()
      fetchOptimizationList()
    } catch (error) {
      message.error('路线优化失败')
    } finally {
      setOptimizing(false)
    }
  }

  // 查看优化详情
  const handleViewDetail = async (optimization: RouteOptimizationData) => {
    setSelectedOptimization(optimization)
    setDetailModalVisible(true)
  }

  // 路径查找
  const handlePathFinding = async () => {
    try {
      const values = await pathForm.validateFields()

      const pathParams = {
        start_point: {
          x: values.start_x,
          y: values.start_y,
          z: values.start_z || 0
        },
        end_point: {
          x: values.end_x,
          y: values.end_y,
          z: values.end_z || 0
        },
        algorithm: values.algorithm || 'a_star'
      }

      const response = await axios.post(
        `/api/v1/routes/${projectId}/find-path`,
        pathParams
      )

      setPathResult(response.data)
      if (response.data.success) {
        message.success('路径查找完成')
      } else {
        message.warning(response.data.message || '未找到可行路径')
      }
    } catch (error) {
      message.error('路径查找失败')
    }
  }

  // 网络分析
  const handleNetworkAnalysis = async () => {
    try {
      const response = await axios.get(`/api/v1/routes/${projectId}/network-analysis`)
      setNetworkAnalysis(response.data)
      setNetworkAnalysisVisible(true)
    } catch (error) {
      message.error('网络分析失败')
    }
  }

  // 获取优化类型文本
  const getOptimizationTypeText = (type: string) => {
    const typeMap = {
      transport_routes: '运输路线优化',
      comprehensive: '综合优化',
      cost_optimization: '成本优化',
      time_optimization: '时间优化'
    }
    return typeMap[type as keyof typeof typeMap] || type
  }

  // 优化表格列定义
  const optimizationColumns = [
    {
      title: '优化ID',
      dataIndex: 'id',
      key: 'id',
      render: (id: number) => (
        <Space>
          <RouteOutlined />
          <span>#{id}</span>
        </Space>
      )
    },
    {
      title: '优化类型',
      dataIndex: 'optimization_type',
      key: 'optimization_type',
      render: (type: string) => getOptimizationTypeText(type)
    },
    {
      title: '路线数量',
      dataIndex: 'total_routes',
      key: 'total_routes',
      render: (count: number) => `${count}条`
    },
    {
      title: '总距离',
      dataIndex: 'total_distance',
      key: 'total_distance',
      render: (distance: number) => distance ? `${distance.toFixed(1)}km` : '-'
    },
    {
      title: '总时间',
      dataIndex: 'total_time',
      key: 'total_time',
      render: (time: number) => time ? `${time.toFixed(1)}h` : '-'
    },
    {
      title: '总成本',
      dataIndex: 'total_cost',
      key: 'total_cost',
      render: (cost: number) => cost ? `¥${cost.toFixed(0)}` : '-'
    },
    {
      title: '优化评分',
      dataIndex: 'optimization_score',
      key: 'optimization_score',
      render: (score: number) => score ? (
        <Space>
          <Progress
            type="circle"
            size={40}
            percent={Math.round(score * 100)}
            status={score > 0.8 ? 'success' : score > 0.6 ? 'normal' : 'exception'}
          />
          <span>{(score * 100).toFixed(1)}%</span>
        </Space>
      ) : '-'
    },
    {
      title: '优化时间',
      dataIndex: 'optimized_at',
      key: 'optimized_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: RouteOptimizationData) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          运输路线优化
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          智能运输路线规划，多目标优化算法，提高运输效率和降低成本
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="优化次数"
              value={optimizationList.length}
              prefix={<RouteOutlined />}
              suffix="次"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总路线数"
              value={optimizationList.reduce((sum, opt) => sum + opt.total_routes, 0)}
              prefix={<LineChartOutlined />}
              suffix="条"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总距离"
              value={optimizationList.reduce((sum, opt) => sum + (opt.total_distance || 0), 0)}
              prefix={<BarChartOutlined />}
              suffix="km"
              precision={1}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均评分"
              value={optimizationList.length > 0
                ? optimizationList.reduce((sum, opt) => sum + (opt.optimization_score || 0), 0) / optimizationList.length * 100
                : 0}
              prefix={<TrophyOutlined />}
              suffix="%"
              precision={1}
              valueStyle={{
                color: optimizationList.length > 0 &&
                       optimizationList.reduce((sum, opt) => sum + (opt.optimization_score || 0), 0) / optimizationList.length > 0.8
                       ? '#52c41a' : '#faad14'
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card
        title="路线优化记录"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchOptimizationList}
            >
              刷新
            </Button>
            <Button
              icon={<EnvironmentOutlined />}
              onClick={() => setPathModalVisible(true)}
            >
              路径查找
            </Button>
            <Button
              icon={<BarChartOutlined />}
              onClick={handleNetworkAnalysis}
            >
              网络分析
            </Button>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => setOptimizationModalVisible(true)}
              loading={optimizing}
            >
              开始优化
            </Button>
          </Space>
        }
      >
        <Table
          columns={optimizationColumns}
          dataSource={optimizationList}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 路线优化参数模态框 */}
      <Modal
        title="路线优化设置"
        open={optimizationModalVisible}
        onOk={handleStartOptimization}
        onCancel={() => {
          setOptimizationModalVisible(false)
          form.resetFields()
        }}
        confirmLoading={optimizing}
        okText="开始优化"
        cancelText="取消"
        width={800}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="optimization_type"
            label="优化类型"
            initialValue="comprehensive"
          >
            <Select>
              <Select.Option value="comprehensive">综合优化</Select.Option>
              <Select.Option value="transport_routes">运输路线优化</Select.Option>
              <Select.Option value="cost_optimization">成本优化</Select.Option>
              <Select.Option value="time_optimization">时间优化</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="objectives"
            label="优化目标"
            initialValue={['minimize_distance', 'minimize_time']}
          >
            <Select mode="multiple" placeholder="选择优化目标">
              <Select.Option value="minimize_distance">最小化距离</Select.Option>
              <Select.Option value="minimize_time">最小化时间</Select.Option>
              <Select.Option value="minimize_cost">最小化成本</Select.Option>
              <Select.Option value="minimize_fuel">最小化燃料消耗</Select.Option>
              <Select.Option value="maximize_safety">最大化安全性</Select.Option>
              <Select.Option value="minimize_environmental_impact">最小化环境影响</Select.Option>
            </Select>
          </Form.Item>

          <Divider>运输任务</Divider>

          <Form.List name="tasks">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card key={key} size="small" style={{ marginBottom: '8px' }}>
                    <Row gutter={16}>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          name={[name, 'origin']}
                          label="起点"
                          rules={[{ required: true, message: '请输入起点' }]}
                        >
                          <Input placeholder="起点位置" />
                        </Form.Item>
                      </Col>
                      <Col span={8}>
                        <Form.Item
                          {...restField}
                          name={[name, 'destination']}
                          label="终点"
                          rules={[{ required: true, message: '请输入终点' }]}
                        >
                          <Input placeholder="终点位置" />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item
                          {...restField}
                          name={[name, 'cargo_volume']}
                          label="货物体积(m³)"
                          rules={[{ required: true, message: '请输入货物体积' }]}
                        >
                          <InputNumber min={0} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={4}>
                        <Form.Item
                          {...restField}
                          name={[name, 'priority']}
                          label="优先级"
                          initialValue={3}
                        >
                          <Select>
                            <Select.Option value={1}>低</Select.Option>
                            <Select.Option value={2}>较低</Select.Option>
                            <Select.Option value={3}>中等</Select.Option>
                            <Select.Option value={4}>较高</Select.Option>
                            <Select.Option value={5}>高</Select.Option>
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>
                    <Button type="link" onClick={() => remove(name)} danger>
                      删除任务
                    </Button>
                  </Card>
                ))}
                <Button type="dashed" onClick={() => add()} block>
                  添加运输任务
                </Button>
              </>
            )}
          </Form.List>

          <Divider>车辆配置</Divider>

          <Form.List name="vehicles">
            {(fields, { add, remove }) => (
              <>
                {fields.map(({ key, name, ...restField }) => (
                  <Card key={key} size="small" style={{ marginBottom: '8px' }}>
                    <Row gutter={16}>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'vehicle_type']}
                          label="车辆类型"
                          initialValue="dump_truck"
                        >
                          <Select>
                            <Select.Option value="dump_truck">自卸卡车</Select.Option>
                            <Select.Option value="excavator">挖掘机</Select.Option>
                            <Select.Option value="bulldozer">推土机</Select.Option>
                            <Select.Option value="grader">平地机</Select.Option>
                            <Select.Option value="water_truck">洒水车</Select.Option>
                          </Select>
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'capacity']}
                          label="载重(吨)"
                          initialValue={50}
                        >
                          <InputNumber min={0} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'max_speed']}
                          label="最大速度(km/h)"
                          initialValue={30}
                        >
                          <InputNumber min={0} max={80} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                      <Col span={6}>
                        <Form.Item
                          {...restField}
                          name={[name, 'fuel_consumption']}
                          label="油耗(L/100km)"
                          initialValue={25}
                        >
                          <InputNumber min={0} style={{ width: '100%' }} />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Button type="link" onClick={() => remove(name)} danger>
                      删除车辆
                    </Button>
                  </Card>
                ))}
                <Button type="dashed" onClick={() => add()} block>
                  添加车辆
                </Button>
              </>
            )}
          </Form.List>
        </Form>
      </Modal>

      {/* 优化详情模态框 */}
      <Modal
        title={`路线优化详情 - #${selectedOptimization?.id}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedOptimization && (
          <Descriptions bordered size="small">
            <Descriptions.Item label="优化类型" span={2}>
              {getOptimizationTypeText(selectedOptimization.optimization_type)}
            </Descriptions.Item>
            <Descriptions.Item label="路线数量">
              {selectedOptimization.total_routes}条
            </Descriptions.Item>
            <Descriptions.Item label="总距离">
              {selectedOptimization.total_distance ? `${selectedOptimization.total_distance.toFixed(1)}km` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="总时间">
              {selectedOptimization.total_time ? `${selectedOptimization.total_time.toFixed(1)}h` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="总成本">
              {selectedOptimization.total_cost ? `¥${selectedOptimization.total_cost.toFixed(0)}` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="优化评分">
              {selectedOptimization.optimization_score ? `${(selectedOptimization.optimization_score * 100).toFixed(1)}%` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="优化时间" span={2}>
              {new Date(selectedOptimization.optimized_at).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* 路径查找模态框 */}
      <Modal
        title="路径查找"
        open={pathModalVisible}
        onOk={handlePathFinding}
        onCancel={() => setPathModalVisible(false)}
        okText="查找路径"
        cancelText="取消"
        width={600}
      >
        <Form form={pathForm} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="start_x"
                label="起点X坐标"
                rules={[{ required: true, message: '请输入起点X坐标' }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 1000" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="start_y"
                label="起点Y坐标"
                rules={[{ required: true, message: '请输入起点Y坐标' }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 2000" />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="end_x"
                label="终点X坐标"
                rules={[{ required: true, message: '请输入终点X坐标' }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 3000" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="end_y"
                label="终点Y坐标"
                rules={[{ required: true, message: '请输入终点Y坐标' }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 4000" />
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="algorithm"
            label="查找算法"
            initialValue="a_star"
          >
            <Select>
              <Select.Option value="dijkstra">Dijkstra算法</Select.Option>
              <Select.Option value="a_star">A*算法</Select.Option>
              <Select.Option value="floyd_warshall">Floyd-Warshall算法</Select.Option>
            </Select>
          </Form.Item>
        </Form>

        {pathResult && (
          <div style={{ marginTop: '16px' }}>
            <Alert
              message={pathResult.success ? "路径查找成功" : "路径查找失败"}
              description={
                pathResult.success ? (
                  <Space direction="vertical" size="small">
                    <span>总距离: {pathResult.total_distance?.toFixed(1)}m</span>
                    <span>总成本: {pathResult.total_cost?.toFixed(1)}</span>
                    <span>算法: {pathResult.algorithm_used}</span>
                    <span>计算时间: {pathResult.computation_time?.toFixed(3)}s</span>
                    <span>路径节点: {pathResult.path?.length}个</span>
                  </Space>
                ) : (
                  pathResult.message
                )
              }
              type={pathResult.success ? "success" : "error"}
              showIcon
            />
          </div>
        )}
      </Modal>

      {/* 网络分析模态框 */}
      <Modal
        title="路线网络分析"
        open={networkAnalysisVisible}
        onCancel={() => setNetworkAnalysisVisible(false)}
        footer={null}
        width={800}
      >
        {networkAnalysis && (
          <Tabs defaultActiveKey="statistics">
            <TabPane tab="网络统计" key="statistics">
              <Descriptions bordered size="small">
                <Descriptions.Item label="节点数量">
                  {networkAnalysis.network_statistics?.node_count || 0}个
                </Descriptions.Item>
                <Descriptions.Item label="边数量">
                  {networkAnalysis.network_statistics?.edge_count || 0}条
                </Descriptions.Item>
                <Descriptions.Item label="连通分量">
                  {networkAnalysis.network_statistics?.connected_components || 0}个
                </Descriptions.Item>
                <Descriptions.Item label="平均度数">
                  {networkAnalysis.network_statistics?.average_degree?.toFixed(2) || 0}
                </Descriptions.Item>
                <Descriptions.Item label="网络密度">
                  {(networkAnalysis.network_statistics?.network_density * 100)?.toFixed(2) || 0}%
                </Descriptions.Item>
                <Descriptions.Item label="分析时间" span={2}>
                  {new Date(networkAnalysis.analysis_time).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            </TabPane>

            <TabPane tab="连通性分析" key="connectivity">
              <Descriptions bordered size="small">
                <Descriptions.Item label="连通分量数">
                  {networkAnalysis.connectivity_analysis?.connected_components || 0}
                </Descriptions.Item>
                <Descriptions.Item label="最大分量大小">
                  {networkAnalysis.connectivity_analysis?.largest_component_size || 0}
                </Descriptions.Item>
                <Descriptions.Item label="连通率">
                  {(networkAnalysis.connectivity_analysis?.connectivity_ratio * 100)?.toFixed(1) || 0}%
                </Descriptions.Item>
              </Descriptions>
            </TabPane>

            <TabPane tab="瓶颈分析" key="bottlenecks">
              <div>
                <p>瓶颈路段数量: {networkAnalysis.bottleneck_analysis?.bottleneck_count || 0}</p>
                {networkAnalysis.bottleneck_analysis?.bottlenecks && (
                  <List
                    dataSource={networkAnalysis.bottleneck_analysis.bottlenecks}
                    renderItem={(bottleneck: any) => (
                      <List.Item>
                        <List.Item.Meta
                          title={`节点: ${bottleneck.node_id}`}
                          description={`类型: ${bottleneck.type}, 严重程度: ${bottleneck.severity}`}
                        />
                      </List.Item>
                    )}
                  />
                )}
              </div>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    </div>
  )
}

export default RouteOptimization
