#!/bin/bash

# 露天矿山道路设计软件部署脚本
# 用于生产环境的自动化部署

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查必要的工具
check_requirements() {
    log_info "检查部署环境..."
    
    # 检查Docker
    if ! command -v docker &> /dev/null; then
        log_error "Docker未安装，请先安装Docker"
        exit 1
    fi
    
    # 检查Docker Compose
    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose未安装，请先安装Docker Compose"
        exit 1
    fi
    
    # 检查Git
    if ! command -v git &> /dev/null; then
        log_error "Git未安装，请先安装Git"
        exit 1
    fi
    
    log_success "环境检查通过"
}

# 检查环境变量
check_env_vars() {
    log_info "检查环境变量..."
    
    required_vars=(
        "POSTGRES_PASSWORD"
        "REDIS_PASSWORD"
        "SECRET_KEY"
        "DOMAIN"
        "SSL_EMAIL"
        "GRAFANA_USER"
        "GRAFANA_PASSWORD"
        "GRAFANA_SECRET_KEY"
    )
    
    missing_vars=()
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var}" ]]; then
            missing_vars+=("$var")
        fi
    done
    
    if [[ ${#missing_vars[@]} -gt 0 ]]; then
        log_error "缺少必要的环境变量:"
        for var in "${missing_vars[@]}"; do
            echo "  - $var"
        done
        log_error "请设置这些环境变量后重新运行部署脚本"
        exit 1
    fi
    
    log_success "环境变量检查通过"
}

# 创建必要的目录
create_directories() {
    log_info "创建必要的目录..."
    
    directories=(
        "nginx/logs"
        "ssl"
        "monitoring/grafana/provisioning"
        "monitoring/grafana/dashboards"
        "database/init"
        "backups"
    )
    
    for dir in "${directories[@]}"; do
        mkdir -p "$dir"
        log_info "创建目录: $dir"
    done
    
    log_success "目录创建完成"
}

# 生成SSL证书
generate_ssl_cert() {
    log_info "生成SSL证书..."
    
    if [[ ! -f "ssl/fullchain.pem" ]] || [[ ! -f "ssl/privkey.pem" ]]; then
        log_info "使用Let's Encrypt生成SSL证书..."
        
        # 临时启动nginx以验证域名
        docker-compose -f docker-compose.prod.yml up -d nginx
        
        # 生成证书
        docker-compose -f docker-compose.prod.yml run --rm certbot
        
        # 重启nginx以加载证书
        docker-compose -f docker-compose.prod.yml restart nginx
        
        log_success "SSL证书生成完成"
    else
        log_info "SSL证书已存在，跳过生成"
    fi
}

# 初始化数据库
init_database() {
    log_info "初始化数据库..."
    
    # 等待数据库启动
    log_info "等待数据库启动..."
    sleep 30
    
    # 运行数据库迁移
    docker-compose -f docker-compose.prod.yml exec app python -m alembic upgrade head
    
    log_success "数据库初始化完成"
}

# 部署应用
deploy_application() {
    log_info "开始部署应用..."
    
    # 拉取最新代码
    log_info "拉取最新代码..."
    git pull origin main
    
    # 构建镜像
    log_info "构建Docker镜像..."
    docker-compose -f docker-compose.prod.yml build --no-cache
    
    # 启动服务
    log_info "启动服务..."
    docker-compose -f docker-compose.prod.yml up -d
    
    # 等待服务启动
    log_info "等待服务启动..."
    sleep 60
    
    # 检查服务状态
    check_services_health
    
    log_success "应用部署完成"
}

# 检查服务健康状态
check_services_health() {
    log_info "检查服务健康状态..."
    
    services=(
        "postgres"
        "redis"
        "app"
        "nginx"
    )
    
    for service in "${services[@]}"; do
        if docker-compose -f docker-compose.prod.yml ps "$service" | grep -q "Up"; then
            log_success "$service 服务运行正常"
        else
            log_error "$service 服务启动失败"
            docker-compose -f docker-compose.prod.yml logs "$service"
            exit 1
        fi
    done
    
    # 检查应用健康端点
    log_info "检查应用健康端点..."
    if curl -f "https://${DOMAIN}/health" > /dev/null 2>&1; then
        log_success "应用健康检查通过"
    else
        log_warning "应用健康检查失败，请检查日志"
    fi
}

# 设置监控
setup_monitoring() {
    log_info "设置监控服务..."
    
    # 创建Grafana配置
    cat > monitoring/grafana/provisioning/datasources/prometheus.yml << EOF
apiVersion: 1

datasources:
  - name: Prometheus
    type: prometheus
    access: proxy
    url: http://prometheus:9090
    isDefault: true
EOF

    # 创建基本的监控面板
    cat > monitoring/grafana/dashboards/system.json << 'EOF'
{
  "dashboard": {
    "title": "系统监控",
    "panels": [
      {
        "title": "CPU使用率",
        "type": "graph",
        "targets": [
          {
            "expr": "100 - (avg by (instance) (rate(node_cpu_seconds_total{mode=\"idle\"}[5m])) * 100)"
          }
        ]
      }
    ]
  }
}
EOF
    
    log_success "监控服务设置完成"
}

# 设置备份
setup_backup() {
    log_info "设置备份服务..."
    
    # 创建备份脚本
    cat > scripts/backup.sh << 'EOF'
#!/bin/bash

# 数据库备份脚本
BACKUP_DIR="/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/mining_road_db_$DATE.sql"

# 创建备份
pg_dump -h postgres -U postgres -d mining_road_design > "$BACKUP_FILE"

# 压缩备份文件
gzip "$BACKUP_FILE"

# 删除30天前的备份
find "$BACKUP_DIR" -name "*.sql.gz" -mtime +30 -delete

echo "备份完成: ${BACKUP_FILE}.gz"
EOF
    
    chmod +x scripts/backup.sh
    
    log_success "备份服务设置完成"
}

# 性能优化
optimize_performance() {
    log_info "应用性能优化..."
    
    # 设置系统参数
    echo "vm.swappiness=10" >> /etc/sysctl.conf
    echo "net.core.somaxconn=65535" >> /etc/sysctl.conf
    echo "net.ipv4.tcp_max_syn_backlog=65535" >> /etc/sysctl.conf
    
    # 应用系统参数
    sysctl -p
    
    # 设置Docker日志轮转
    cat > /etc/docker/daemon.json << EOF
{
  "log-driver": "json-file",
  "log-opts": {
    "max-size": "10m",
    "max-file": "3"
  }
}
EOF
    
    # 重启Docker服务
    systemctl restart docker
    
    log_success "性能优化完成"
}

# 安全加固
security_hardening() {
    log_info "安全加固..."
    
    # 创建nginx认证文件
    if [[ ! -f "nginx/.htpasswd" ]]; then
        htpasswd -cb nginx/.htpasswd "${GRAFANA_USER}" "${GRAFANA_PASSWORD}"
    fi
    
    # 设置文件权限
    chmod 600 .env
    chmod 600 nginx/.htpasswd
    
    # 设置防火墙规则
    ufw --force enable
    ufw default deny incoming
    ufw default allow outgoing
    ufw allow ssh
    ufw allow 80/tcp
    ufw allow 443/tcp
    
    log_success "安全加固完成"
}

# 主部署流程
main() {
    log_info "开始部署露天矿山道路设计软件..."
    
    # 检查环境
    check_requirements
    check_env_vars
    
    # 创建目录
    create_directories
    
    # 设置监控和备份
    setup_monitoring
    setup_backup
    
    # 部署应用
    deploy_application
    
    # 初始化数据库
    init_database
    
    # 生成SSL证书
    generate_ssl_cert
    
    # 性能优化
    optimize_performance
    
    # 安全加固
    security_hardening
    
    log_success "部署完成！"
    log_info "应用访问地址: https://${DOMAIN}"
    log_info "监控面板: https://${DOMAIN}/grafana"
    log_info "Prometheus: https://${DOMAIN}/prometheus"
    
    # 显示服务状态
    echo ""
    log_info "服务状态:"
    docker-compose -f docker-compose.prod.yml ps
}

# 运行主流程
main "$@"
