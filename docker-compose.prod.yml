version: '3.8'

services:
  # 数据库服务
  postgres:
    image: postgis/postgis:15-3.3
    container_name: mining_road_db_prod
    environment:
      POSTGRES_DB: mining_road_design
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./database/init:/docker-entrypoint-initdb.d
    networks:
      - mining_road_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'

  # Redis缓存服务
  redis:
    image: redis:7-alpine
    container_name: mining_road_redis_prod
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
    networks:
      - mining_road_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 10s
      timeout: 3s
      retries: 5
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # 主应用服务
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: mining_road_app_prod
    environment:
      # 数据库配置
      DATABASE_URL: postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/mining_road_design
      
      # Redis配置
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379/0
      
      # 应用配置
      SECRET_KEY: ${SECRET_KEY}
      DEBUG: false
      ENVIRONMENT: production
      
      # 文件存储配置
      UPLOAD_DIR: /app/uploads
      MAX_UPLOAD_SIZE: 104857600  # 100MB
      
      # 日志配置
      LOG_LEVEL: INFO
      LOG_FILE: /app/logs/app.log
      
      # 外部服务配置
      CESIUM_ION_ACCESS_TOKEN: ${CESIUM_ION_ACCESS_TOKEN}
      
      # 性能配置
      WORKERS: 4
      MAX_CONNECTIONS: 100
      
      # 安全配置
      ALLOWED_HOSTS: ${ALLOWED_HOSTS}
      CORS_ORIGINS: ${CORS_ORIGINS}
      
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - app_temp:/app/temp
    networks:
      - mining_road_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
      replicas: 2

  # Nginx反向代理和负载均衡
  nginx:
    image: nginx:alpine
    container_name: mining_road_nginx_prod
    volumes:
      - ./nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - app_uploads:/var/www/uploads:ro
      - ./ssl:/etc/nginx/ssl:ro
      - ./nginx/logs:/var/log/nginx
    ports:
      - "80:80"
      - "443:443"
    networks:
      - mining_road_network
    depends_on:
      - app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'

  # 监控服务 - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: mining_road_prometheus_prod
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'
      - '--web.external-url=https://${DOMAIN}/prometheus'
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - mining_road_network
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # 监控服务 - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: mining_road_grafana_prod
    environment:
      GF_SECURITY_ADMIN_USER: ${GRAFANA_USER}
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_SERVER_ROOT_URL: https://${DOMAIN}/grafana
      GF_SERVER_SERVE_FROM_SUB_PATH: true
      GF_SECURITY_SECRET_KEY: ${GRAFANA_SECRET_KEY}
    volumes:
      - grafana_data:/var/lib/grafana
      - ./monitoring/grafana/provisioning:/etc/grafana/provisioning
      - ./monitoring/grafana/dashboards:/var/lib/grafana/dashboards
    networks:
      - mining_road_network
    depends_on:
      - prometheus
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  # 日志收集服务
  filebeat:
    image: docker.elastic.co/beats/filebeat:8.8.0
    container_name: mining_road_filebeat_prod
    user: root
    volumes:
      - ./monitoring/filebeat.yml:/usr/share/filebeat/filebeat.yml:ro
      - app_logs:/var/log/app:ro
      - ./nginx/logs:/var/log/nginx:ro
      - /var/lib/docker/containers:/var/lib/docker/containers:ro
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - mining_road_network
    depends_on:
      - app
    restart: unless-stopped
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.2'

  # 备份服务
  backup:
    image: postgres:15-alpine
    container_name: mining_road_backup_prod
    environment:
      PGPASSWORD: ${POSTGRES_PASSWORD}
      BACKUP_SCHEDULE: "0 2 * * *"  # 每天凌晨2点备份
      BACKUP_RETENTION_DAYS: 30
      S3_BUCKET: ${BACKUP_S3_BUCKET}
      AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
      AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
    volumes:
      - ./scripts/backup.sh:/backup.sh:ro
      - backup_data:/backups
    networks:
      - mining_road_network
    depends_on:
      - postgres
    restart: unless-stopped
    command: ["sh", "-c", "crond -f"]
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.2'

  # SSL证书自动更新
  certbot:
    image: certbot/certbot
    container_name: mining_road_certbot_prod
    volumes:
      - ./ssl:/etc/letsencrypt
      - ./nginx/webroot:/var/www/certbot
    command: certonly --webroot --webroot-path=/var/www/certbot --email ${SSL_EMAIL} --agree-tos --no-eff-email -d ${DOMAIN}
    depends_on:
      - nginx
    restart: "no"

# 网络配置
networks:
  mining_road_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

# 数据卷配置
volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local
  app_temp:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  backup_data:
    driver: local
