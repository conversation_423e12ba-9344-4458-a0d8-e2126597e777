"""
系统监控服务
"""
import psutil
import time
import asyncio
from typing import Dict, List, Optional, Any
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
import logging

logger = logging.getLogger(__name__)


class MonitoringService:
    """系统监控服务类"""
    
    def __init__(self):
        self.metrics_history = []
        self.alert_thresholds = {
            'cpu_usage': 80.0,
            'memory_usage': 85.0,
            'disk_usage': 90.0,
            'response_time': 2.0,
            'error_rate': 5.0
        }
        self.performance_counters = {
            'api_requests': 0,
            'api_errors': 0,
            'database_queries': 0,
            'file_uploads': 0,
            'calculations_performed': 0
        }
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统指标"""
        try:
            # CPU使用率
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            cpu_freq = psutil.cpu_freq()
            
            # 内存使用情况
            memory = psutil.virtual_memory()
            swap = psutil.swap_memory()
            
            # 磁盘使用情况
            disk_usage = psutil.disk_usage('/')
            disk_io = psutil.disk_io_counters()
            
            # 网络使用情况
            network_io = psutil.net_io_counters()
            
            # 进程信息
            process = psutil.Process()
            process_memory = process.memory_info()
            process_cpu = process.cpu_percent()
            
            metrics = {
                'timestamp': datetime.utcnow().isoformat(),
                'cpu': {
                    'usage_percent': cpu_percent,
                    'count': cpu_count,
                    'frequency': cpu_freq.current if cpu_freq else None,
                    'process_usage': process_cpu
                },
                'memory': {
                    'total': memory.total,
                    'available': memory.available,
                    'used': memory.used,
                    'usage_percent': memory.percent,
                    'process_rss': process_memory.rss,
                    'process_vms': process_memory.vms
                },
                'swap': {
                    'total': swap.total,
                    'used': swap.used,
                    'usage_percent': swap.percent
                },
                'disk': {
                    'total': disk_usage.total,
                    'used': disk_usage.used,
                    'free': disk_usage.free,
                    'usage_percent': (disk_usage.used / disk_usage.total) * 100,
                    'read_bytes': disk_io.read_bytes if disk_io else 0,
                    'write_bytes': disk_io.write_bytes if disk_io else 0
                },
                'network': {
                    'bytes_sent': network_io.bytes_sent,
                    'bytes_recv': network_io.bytes_recv,
                    'packets_sent': network_io.packets_sent,
                    'packets_recv': network_io.packets_recv
                }
            }
            
            # 保存到历史记录
            self.metrics_history.append(metrics)
            
            # 只保留最近1小时的数据
            cutoff_time = datetime.utcnow() - timedelta(hours=1)
            self.metrics_history = [
                m for m in self.metrics_history 
                if datetime.fromisoformat(m['timestamp']) > cutoff_time
            ]
            
            return metrics
            
        except Exception as e:
            logger.error(f"获取系统指标失败: {str(e)}")
            return {}
    
    def get_application_metrics(self) -> Dict[str, Any]:
        """获取应用程序指标"""
        try:
            # 计算错误率
            total_requests = self.performance_counters['api_requests']
            total_errors = self.performance_counters['api_errors']
            error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0
            
            # 计算平均响应时间（简化实现）
            avg_response_time = 0.5  # 这里应该从实际的响应时间记录中计算
            
            return {
                'timestamp': datetime.utcnow().isoformat(),
                'performance': {
                    'api_requests_total': total_requests,
                    'api_errors_total': total_errors,
                    'error_rate_percent': error_rate,
                    'average_response_time': avg_response_time,
                    'database_queries_total': self.performance_counters['database_queries'],
                    'file_uploads_total': self.performance_counters['file_uploads'],
                    'calculations_performed': self.performance_counters['calculations_performed']
                },
                'health_status': self._calculate_health_status()
            }
            
        except Exception as e:
            logger.error(f"获取应用程序指标失败: {str(e)}")
            return {}
    
    def _calculate_health_status(self) -> str:
        """计算系统健康状态"""
        try:
            system_metrics = self.get_system_metrics()
            
            # 检查各项指标
            cpu_ok = system_metrics.get('cpu', {}).get('usage_percent', 0) < self.alert_thresholds['cpu_usage']
            memory_ok = system_metrics.get('memory', {}).get('usage_percent', 0) < self.alert_thresholds['memory_usage']
            disk_ok = system_metrics.get('disk', {}).get('usage_percent', 0) < self.alert_thresholds['disk_usage']
            
            # 计算错误率
            total_requests = self.performance_counters['api_requests']
            total_errors = self.performance_counters['api_errors']
            error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0
            error_ok = error_rate < self.alert_thresholds['error_rate']
            
            if all([cpu_ok, memory_ok, disk_ok, error_ok]):
                return 'healthy'
            elif any([not cpu_ok, not memory_ok]):
                return 'warning'
            else:
                return 'critical'
                
        except Exception as e:
            logger.error(f"计算健康状态失败: {str(e)}")
            return 'unknown'
    
    def get_performance_summary(self, hours: int = 24) -> Dict[str, Any]:
        """获取性能摘要"""
        try:
            cutoff_time = datetime.utcnow() - timedelta(hours=hours)
            
            # 过滤指定时间范围内的数据
            recent_metrics = [
                m for m in self.metrics_history 
                if datetime.fromisoformat(m['timestamp']) > cutoff_time
            ]
            
            if not recent_metrics:
                return {
                    'period_hours': hours,
                    'data_points': 0,
                    'message': '没有足够的历史数据'
                }
            
            # 计算统计信息
            cpu_values = [m['cpu']['usage_percent'] for m in recent_metrics if 'cpu' in m]
            memory_values = [m['memory']['usage_percent'] for m in recent_metrics if 'memory' in m]
            disk_values = [m['disk']['usage_percent'] for m in recent_metrics if 'disk' in m]
            
            summary = {
                'period_hours': hours,
                'data_points': len(recent_metrics),
                'cpu': {
                    'average': sum(cpu_values) / len(cpu_values) if cpu_values else 0,
                    'max': max(cpu_values) if cpu_values else 0,
                    'min': min(cpu_values) if cpu_values else 0
                },
                'memory': {
                    'average': sum(memory_values) / len(memory_values) if memory_values else 0,
                    'max': max(memory_values) if memory_values else 0,
                    'min': min(memory_values) if memory_values else 0
                },
                'disk': {
                    'average': sum(disk_values) / len(disk_values) if disk_values else 0,
                    'max': max(disk_values) if disk_values else 0,
                    'min': min(disk_values) if disk_values else 0
                },
                'performance_counters': self.performance_counters.copy()
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取性能摘要失败: {str(e)}")
            return {}
    
    def check_alerts(self) -> List[Dict[str, Any]]:
        """检查告警"""
        try:
            alerts = []
            current_metrics = self.get_system_metrics()
            
            # CPU使用率告警
            cpu_usage = current_metrics.get('cpu', {}).get('usage_percent', 0)
            if cpu_usage > self.alert_thresholds['cpu_usage']:
                alerts.append({
                    'type': 'cpu_high',
                    'severity': 'warning' if cpu_usage < 90 else 'critical',
                    'message': f'CPU使用率过高: {cpu_usage:.1f}%',
                    'value': cpu_usage,
                    'threshold': self.alert_thresholds['cpu_usage'],
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            # 内存使用率告警
            memory_usage = current_metrics.get('memory', {}).get('usage_percent', 0)
            if memory_usage > self.alert_thresholds['memory_usage']:
                alerts.append({
                    'type': 'memory_high',
                    'severity': 'warning' if memory_usage < 95 else 'critical',
                    'message': f'内存使用率过高: {memory_usage:.1f}%',
                    'value': memory_usage,
                    'threshold': self.alert_thresholds['memory_usage'],
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            # 磁盘使用率告警
            disk_usage = current_metrics.get('disk', {}).get('usage_percent', 0)
            if disk_usage > self.alert_thresholds['disk_usage']:
                alerts.append({
                    'type': 'disk_high',
                    'severity': 'warning' if disk_usage < 95 else 'critical',
                    'message': f'磁盘使用率过高: {disk_usage:.1f}%',
                    'value': disk_usage,
                    'threshold': self.alert_thresholds['disk_usage'],
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            # 错误率告警
            total_requests = self.performance_counters['api_requests']
            total_errors = self.performance_counters['api_errors']
            error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0
            
            if error_rate > self.alert_thresholds['error_rate']:
                alerts.append({
                    'type': 'error_rate_high',
                    'severity': 'warning' if error_rate < 10 else 'critical',
                    'message': f'API错误率过高: {error_rate:.1f}%',
                    'value': error_rate,
                    'threshold': self.alert_thresholds['error_rate'],
                    'timestamp': datetime.utcnow().isoformat()
                })
            
            return alerts
            
        except Exception as e:
            logger.error(f"检查告警失败: {str(e)}")
            return []
    
    def increment_counter(self, counter_name: str, value: int = 1) -> None:
        """增加性能计数器"""
        if counter_name in self.performance_counters:
            self.performance_counters[counter_name] += value
    
    def record_api_request(self, success: bool = True) -> None:
        """记录API请求"""
        self.increment_counter('api_requests')
        if not success:
            self.increment_counter('api_errors')
    
    def record_database_query(self) -> None:
        """记录数据库查询"""
        self.increment_counter('database_queries')
    
    def record_file_upload(self) -> None:
        """记录文件上传"""
        self.increment_counter('file_uploads')
    
    def record_calculation(self) -> None:
        """记录计算操作"""
        self.increment_counter('calculations_performed')
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            import platform
            import sys
            
            return {
                'system': {
                    'platform': platform.platform(),
                    'system': platform.system(),
                    'release': platform.release(),
                    'version': platform.version(),
                    'machine': platform.machine(),
                    'processor': platform.processor()
                },
                'python': {
                    'version': sys.version,
                    'executable': sys.executable,
                    'platform': sys.platform
                },
                'process': {
                    'pid': psutil.Process().pid,
                    'create_time': datetime.fromtimestamp(psutil.Process().create_time()).isoformat(),
                    'num_threads': psutil.Process().num_threads()
                }
            }
            
        except Exception as e:
            logger.error(f"获取系统信息失败: {str(e)}")
            return {}
    
    def get_database_metrics(self, db: Session) -> Dict[str, Any]:
        """获取数据库指标"""
        try:
            # 这里可以添加数据库相关的监控指标
            # 例如连接数、查询性能、表大小等
            
            return {
                'connection_pool': {
                    'active_connections': 'N/A',
                    'idle_connections': 'N/A',
                    'total_connections': 'N/A'
                },
                'query_performance': {
                    'average_query_time': 'N/A',
                    'slow_queries': 'N/A'
                },
                'storage': {
                    'database_size': 'N/A',
                    'table_count': 'N/A'
                }
            }
            
        except Exception as e:
            logger.error(f"获取数据库指标失败: {str(e)}")
            return {}
    
    def generate_health_report(self, db: Session = None) -> Dict[str, Any]:
        """生成健康报告"""
        try:
            system_metrics = self.get_system_metrics()
            app_metrics = self.get_application_metrics()
            performance_summary = self.get_performance_summary()
            alerts = self.check_alerts()
            system_info = self.get_system_info()
            
            database_metrics = {}
            if db:
                database_metrics = self.get_database_metrics(db)
            
            health_report = {
                'report_time': datetime.utcnow().isoformat(),
                'overall_status': self._calculate_health_status(),
                'system_metrics': system_metrics,
                'application_metrics': app_metrics,
                'performance_summary': performance_summary,
                'database_metrics': database_metrics,
                'system_info': system_info,
                'active_alerts': alerts,
                'alert_count': len(alerts),
                'recommendations': self._generate_recommendations(system_metrics, alerts)
            }
            
            return health_report
            
        except Exception as e:
            logger.error(f"生成健康报告失败: {str(e)}")
            return {}
    
    def _generate_recommendations(self, 
                                system_metrics: Dict[str, Any],
                                alerts: List[Dict[str, Any]]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        try:
            # 基于系统指标生成建议
            cpu_usage = system_metrics.get('cpu', {}).get('usage_percent', 0)
            memory_usage = system_metrics.get('memory', {}).get('usage_percent', 0)
            disk_usage = system_metrics.get('disk', {}).get('usage_percent', 0)
            
            if cpu_usage > 70:
                recommendations.append("CPU使用率较高，建议优化计算密集型操作或增加CPU资源")
            
            if memory_usage > 80:
                recommendations.append("内存使用率较高，建议优化内存使用或增加内存容量")
            
            if disk_usage > 85:
                recommendations.append("磁盘空间不足，建议清理临时文件或扩展存储空间")
            
            # 基于告警生成建议
            for alert in alerts:
                if alert['type'] == 'error_rate_high':
                    recommendations.append("API错误率过高，建议检查应用程序日志和错误处理")
            
            # 性能优化建议
            total_requests = self.performance_counters['api_requests']
            if total_requests > 10000:
                recommendations.append("API请求量较大，建议考虑启用缓存和负载均衡")
            
            return recommendations
            
        except Exception as e:
            logger.error(f"生成优化建议失败: {str(e)}")
            return []
