import React from 'react'
import { Card, Button, Table, Tag, Space } from 'antd'
import { PlusOutlined, EditOutlined, DeleteOutlined, EyeOutlined } from '@ant-design/icons'

const ProjectManagement: React.FC = () => {
  const columns = [
    { title: '项目名称', dataIndex: 'name', key: 'name' },
    { title: '状态', dataIndex: 'status', key: 'status', render: (status: string) => <Tag color="blue">{status}</Tag> },
    { title: '创建时间', dataIndex: 'createTime', key: 'createTime' },
    {
      title: '操作',
      key: 'action',
      render: () => (
        <Space>
          <Button icon={<EyeOutlined />} size="small">查看</Button>
          <Button icon={<EditOutlined />} size="small">编辑</Button>
          <Button icon={<DeleteOutlined />} size="small" danger>删除</Button>
        </Space>
      ),
    },
  ]

  const data = [
    { key: '1', name: '东山露天矿道路设计', status: '进行中', createTime: '2024-01-15' },
    { key: '2', name: '西部矿区运输路线优化', status: '已完成', createTime: '2024-01-14' },
  ]

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '16px' }}>
        <h1>项目管理</h1>
        <Button type="primary" icon={<PlusOutlined />}>新建项目</Button>
      </div>
      <Card>
        <Table columns={columns} dataSource={data} />
      </Card>
    </div>
  )
}

export default ProjectManagement
