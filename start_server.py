#!/usr/bin/env python3
"""
直接启动HTTP服务器
"""
import json
import os
import sys
import time
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from pathlib import Path

class MiningRoadHandler(BaseHTTPRequestHandler):
    """露天矿山道路设计软件HTTP处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        path = urllib.parse.urlparse(self.path).path
        
        # 处理静态文件
        if path == '/' or path == '/index.html':
            self.serve_html_file()
            return
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
        
        # 路由处理
        if path == '/health':
            response = {
                "status": "healthy",
                "version": "1.0.0",
                "timestamp": time.time(),
                "message": "露天矿山道路设计软件运行正常"
            }
        elif path == '/api/v1/info':
            response = {
                "name": "露天矿山道路设计软件API",
                "version": "1.0.0",
                "description": "专业的露天矿山采矿工程道路设计软件API"
            }
        elif path == '/api/v1/projects':
            response = {
                "projects": [
                    {
                        "id": 1,
                        "name": "示例项目1",
                        "description": "露天矿山道路设计示例项目",
                        "status": "active"
                    }
                ],
                "total": 1
            }
        elif path == '/api/v1/roads':
            response = {
                "roads": [
                    {
                        "id": 1,
                        "name": "主运输道路",
                        "length": 1500.0,
                        "width": 8.0,
                        "status": "designed"
                    }
                ],
                "total": 1
            }
        elif path == '/api/v1/monitoring/status':
            response = {
                "system_status": {
                    "cpu_usage": 25.5,
                    "memory_usage": 45.2,
                    "disk_usage": 60.8
                },
                "service_status": {
                    "api_server": "running",
                    "database": "simulated"
                }
            }
        else:
            response = {
                "error": "Not Found",
                "message": "请求的资源不存在"
            }
        
        json_str = json.dumps(response, ensure_ascii=False, indent=2)
        self.wfile.write(json_str.encode('utf-8'))
    
    def do_POST(self):
        """处理POST请求"""
        path = urllib.parse.urlparse(self.path).path
        
        # 读取请求体
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8')) if post_data else {}
        except json.JSONDecodeError:
            data = {}
        
        # 设置CORS头
        self.send_response(200)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
        
        if path == '/api/v1/projects':
            response = {
                "id": 2,
                "name": data.get("name", "新项目"),
                "description": data.get("description", ""),
                "created_at": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
                "status": "active",
                "message": "项目创建成功"
            }
        elif path == '/api/v1/roads/design':
            response = {
                "message": "道路设计完成",
                "road_id": 2,
                "design_result": {
                    "length": data.get("length", 1000.0),
                    "width": data.get("width", 6.0),
                    "safety_score": 85.5
                }
            }
        else:
            response = {
                "error": "Not Found",
                "message": "请求的资源不存在"
            }
        
        json_str = json.dumps(response, ensure_ascii=False, indent=2)
        self.wfile.write(json_str.encode('utf-8'))
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
        self.end_headers()
    
    def serve_html_file(self):
        """服务HTML文件"""
        try:
            with open('index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_response(404)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.end_headers()
            
            error_html = """
            <!DOCTYPE html>
            <html>
            <head><title>露天矿山道路设计软件</title></head>
            <body>
                <h1>🏔️ 露天矿山道路设计软件</h1>
                <p>服务器正在运行，但未找到index.html文件</p>
                <h2>API端点测试:</h2>
                <ul>
                    <li><a href="/health">健康检查</a></li>
                    <li><a href="/api/v1/info">API信息</a></li>
                    <li><a href="/api/v1/projects">项目列表</a></li>
                    <li><a href="/api/v1/roads">道路列表</a></li>
                    <li><a href="/api/v1/monitoring/status">系统监控</a></li>
                </ul>
            </body>
            </html>
            """
            self.wfile.write(error_html.encode('utf-8'))
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {self.address_string()} - {format % args}")

def main():
    """主函数"""
    print("=" * 60)
    print("🏔️  露天矿山道路设计软件")
    print("   专业的露天矿山采矿工程道路设计软件")
    print("   版本: 1.0.0")
    print("=" * 60)
    
    # 创建必要的目录
    for directory in ['logs', 'uploads', 'data']:
        Path(directory).mkdir(exist_ok=True)
    
    # 启动服务器
    server_address = ('localhost', 8000)
    
    try:
        httpd = HTTPServer(server_address, MiningRoadHandler)
        print(f"\n🚀 服务器启动成功!")
        print(f"🌐 Web界面: http://localhost:8000")
        print(f"🔍 健康检查: http://localhost:8000/health")
        print(f"📚 API信息: http://localhost:8000/api/v1/info")
        print(f"\n按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务器...")
        httpd.shutdown()
        print("✅ 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
