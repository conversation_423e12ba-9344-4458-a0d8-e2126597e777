# 露天矿山道路设计软件

## 项目简介

这是一款专业的露天矿山采矿工程道路设计软件，采用现代Web技术栈开发，提供完整的道路设计、分析和优化解决方案。

## 主要功能

- 🛣️ **道路选线功能** - 基于露天矿山道路设计标准的智能选线
- ⚠️ **道路冲突检测** - 自动检测道路与地形、设施的冲突
- 🔒 **安全检测功能** - 视距分析、坡度检查、排水系统检测
- 📊 **道路剖切功能** - 纵断面、横断面分析和工程量统计
- 🚛 **运输路线优化** - 多目标优化算法，考虑成本、时间、安全性
- 🌍 **三维地形地质数据** - 支持多种地质数据格式导入和可视化
- 📐 **AutoCAD集成** - DWG/DXF文件导入导出，保持设计数据完整性
- 🎨 **现代简约界面** - 黑色、黄色、灰色、白色主题的专业UI设计

## 技术栈

### 后端
- **Python 3.9+** - 主要开发语言
- **FastAPI** - 高性能Web框架
- **SQLAlchemy** - ORM数据库操作
- **PostgreSQL** - 主数据库
- **Redis** - 缓存和会话管理
- **Celery** - 异步任务处理

### 前端
- **React 18** - 用户界面框架
- **Vite** - 构建工具
- **TypeScript** - 类型安全
- **Cesium** - 三维GIS可视化
- **Ant Design** - UI组件库
- **Three.js** - 3D图形渲染

### 数据处理
- **GDAL** - 地理空间数据处理
- **NumPy/SciPy** - 科学计算
- **Shapely** - 几何操作
- **Rasterio** - 栅格数据处理
- **ezdxf** - AutoCAD文件处理

## 项目结构

```
openpit-road-design/
├── backend/                 # 后端服务
│   ├── app/                # 应用主目录
│   │   ├── api/           # API路由
│   │   ├── core/          # 核心配置
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   └── utils/         # 工具函数
│   ├── tests/             # 测试文件
│   └── requirements.txt   # Python依赖
├── frontend/               # 前端应用
│   ├── src/               # 源代码
│   │   ├── components/    # React组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── utils/         # 工具函数
│   │   └── styles/        # 样式文件
│   ├── public/            # 静态资源
│   └── package.json       # 前端依赖
├── algorithms/             # 核心算法库
│   ├── road_design/       # 道路设计算法
│   ├── conflict_detection/ # 冲突检测算法
│   ├── safety_analysis/   # 安全分析算法
│   └── optimization/      # 优化算法
├── data/                  # 数据文件
│   ├── terrain/           # 地形数据
│   ├── geology/           # 地质数据
│   └── standards/         # 设计标准
├── docs/                  # 文档
├── scripts/               # 部署脚本
└── docker/                # Docker配置
```

## 快速开始

### 环境要求

- Python 3.9+
- Node.js 16+
- PostgreSQL 13+
- Redis 6+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd openpit-road-design
```

2. 设置Python虚拟环境
```bash
python -m venv venv
source venv/bin/activate  # Windows: venv\Scripts\activate
```

3. 安装后端依赖
```bash
cd backend
pip install -r requirements.txt
```

4. 安装前端依赖
```bash
cd frontend
npm install
```

5. 配置数据库
```bash
# 创建数据库
createdb openpit_road_design

# 运行迁移
cd backend
alembic upgrade head
```

6. 启动服务
```bash
# 启动后端服务
cd backend
uvicorn app.main:app --reload

# 启动前端服务
cd frontend
npm run dev
```

## 开发指南

### 代码规范

- Python代码遵循PEP 8规范
- JavaScript/TypeScript代码使用ESLint和Prettier
- 提交信息遵循Conventional Commits规范

### 测试

```bash
# 后端测试
cd backend
pytest

# 前端测试
cd frontend
npm test
```

## 许可证

本项目采用MIT许可证，详见LICENSE文件。

## 贡献

欢迎提交Issue和Pull Request来改进项目。

## 联系方式

如有问题或建议，请通过以下方式联系：
- 邮箱: [<EMAIL>]
- 项目主页: [project-url]
