"""
项目管理API端点
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.core.database import get_db

router = APIRouter()


@router.get("/")
async def get_projects(db: Session = Depends(get_db)):
    """获取项目列表"""
    return {"message": "获取项目列表"}


@router.post("/")
async def create_project(db: Session = Depends(get_db)):
    """创建新项目"""
    return {"message": "创建项目成功"}


@router.get("/{project_id}")
async def get_project(project_id: int, db: Session = Depends(get_db)):
    """获取项目详情"""
    return {"message": f"获取项目{project_id}详情"}


@router.put("/{project_id}")
async def update_project(project_id: int, db: Session = Depends(get_db)):
    """更新项目信息"""
    return {"message": f"更新项目{project_id}成功"}


@router.delete("/{project_id}")
async def delete_project(project_id: int, db: Session = Depends(get_db)):
    """删除项目"""
    return {"message": f"删除项目{project_id}成功"}
