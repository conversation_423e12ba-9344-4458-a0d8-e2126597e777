"""
CAD图纸生成器
"""
import os
import math
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

try:
    import ezdxf
    from ezdxf import units
    from ezdxf.math import Vec3
    EZDXF_AVAILABLE = True
except ImportError:
    EZDXF_AVAILABLE = False
    logger.warning("ezdxf库未安装，图纸生成功能将受限")


@dataclass
class DrawingTemplate:
    """图纸模板"""
    name: str
    paper_size: str  # A0, A1, A2, A3, A4
    scale: float
    units: str
    title_block: bool = True
    layers: List[Dict] = None


@dataclass
class RoadDrawingData:
    """道路图纸数据"""
    roads: List[Dict]
    points: List[Dict]
    annotations: List[Dict]
    contours: List[Dict] = None
    cross_sections: List[Dict] = None


class DrawingGenerator:
    """CAD图纸生成器"""
    
    def __init__(self):
        self.paper_sizes = {
            'A0': (1189, 841),
            'A1': (841, 594),
            'A2': (594, 420),
            'A3': (420, 297),
            'A4': (297, 210)
        }
        
        self.standard_layers = {
            'ROAD_CENTERLINE': {'color': 1, 'linetype': 'CONTINUOUS', 'lineweight': 0.5},
            'ROAD_EDGE': {'color': 2, 'linetype': 'CONTINUOUS', 'lineweight': 0.3},
            'CONTOURS': {'color': 3, 'linetype': 'CONTINUOUS', 'lineweight': 0.2},
            'POINTS': {'color': 4, 'linetype': 'CONTINUOUS', 'lineweight': 0.1},
            'TEXT': {'color': 7, 'linetype': 'CONTINUOUS', 'lineweight': 0.1},
            'DIMENSIONS': {'color': 6, 'linetype': 'CONTINUOUS', 'lineweight': 0.1},
            'GRID': {'color': 8, 'linetype': 'DASHED', 'lineweight': 0.1},
            'TITLE_BLOCK': {'color': 7, 'linetype': 'CONTINUOUS', 'lineweight': 0.3}
        }
        
        if not EZDXF_AVAILABLE:
            logger.error("ezdxf库未安装，请安装: pip install ezdxf")
    
    def generate_plan_drawing(self, 
                            road_data: RoadDrawingData,
                            template: DrawingTemplate,
                            output_path: str) -> bool:
        """生成平面图"""
        try:
            if not EZDXF_AVAILABLE:
                raise ImportError("ezdxf库未安装")
            
            logger.info(f"生成平面图: {output_path}")
            
            # 创建新图纸
            doc = ezdxf.new('R2010')
            
            # 设置图纸单位
            self._setup_drawing_units(doc, template.units)
            
            # 创建图层
            self._create_layers(doc)
            
            # 获取模型空间
            msp = doc.modelspace()
            
            # 绘制道路
            self._draw_roads(msp, road_data.roads)
            
            # 绘制点位
            self._draw_points(msp, road_data.points)
            
            # 绘制等高线
            if road_data.contours:
                self._draw_contours(msp, road_data.contours)
            
            # 绘制注释
            self._draw_annotations(msp, road_data.annotations)
            
            # 添加标题栏
            if template.title_block:
                self._add_title_block(msp, template, road_data)
            
            # 添加比例尺和指北针
            self._add_scale_and_north(msp, template.scale)
            
            # 保存文件
            doc.saveas(output_path)
            
            logger.info(f"平面图生成完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成平面图失败: {str(e)}")
            return False
    
    def generate_profile_drawing(self,
                               profile_data: Dict,
                               template: DrawingTemplate,
                               output_path: str) -> bool:
        """生成纵断面图"""
        try:
            if not EZDXF_AVAILABLE:
                raise ImportError("ezdxf库未安装")
            
            logger.info(f"生成纵断面图: {output_path}")
            
            # 创建新图纸
            doc = ezdxf.new('R2010')
            
            # 设置图纸单位
            self._setup_drawing_units(doc, template.units)
            
            # 创建图层
            self._create_layers(doc)
            
            # 获取模型空间
            msp = doc.modelspace()
            
            # 绘制纵断面
            self._draw_longitudinal_profile(msp, profile_data)
            
            # 添加标题栏
            if template.title_block:
                self._add_title_block(msp, template, {'type': 'profile'})
            
            # 保存文件
            doc.saveas(output_path)
            
            logger.info(f"纵断面图生成完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成纵断面图失败: {str(e)}")
            return False
    
    def generate_cross_section_drawing(self,
                                     cross_section_data: Dict,
                                     template: DrawingTemplate,
                                     output_path: str) -> bool:
        """生成横断面图"""
        try:
            if not EZDXF_AVAILABLE:
                raise ImportError("ezdxf库未安装")
            
            logger.info(f"生成横断面图: {output_path}")
            
            # 创建新图纸
            doc = ezdxf.new('R2010')
            
            # 设置图纸单位
            self._setup_drawing_units(doc, template.units)
            
            # 创建图层
            self._create_layers(doc)
            
            # 获取模型空间
            msp = doc.modelspace()
            
            # 绘制横断面
            self._draw_cross_sections(msp, cross_section_data)
            
            # 添加标题栏
            if template.title_block:
                self._add_title_block(msp, template, {'type': 'cross_section'})
            
            # 保存文件
            doc.saveas(output_path)
            
            logger.info(f"横断面图生成完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"生成横断面图失败: {str(e)}")
            return False
    
    def _setup_drawing_units(self, doc, unit_name: str) -> None:
        """设置图纸单位"""
        try:
            unit_map = {
                'mm': units.MM,
                'cm': units.CM,
                'm': units.M,
                'km': units.KM,
                'in': units.INCH,
                'ft': units.FEET
            }
            
            unit = unit_map.get(unit_name.lower(), units.M)
            doc.units = unit
            
            # 设置头部变量
            doc.header['$INSUNITS'] = unit
            
        except Exception as e:
            logger.warning(f"设置图纸单位失败: {str(e)}")
    
    def _create_layers(self, doc) -> None:
        """创建标准图层"""
        try:
            for layer_name, properties in self.standard_layers.items():
                layer = doc.layers.new(layer_name)
                layer.color = properties['color']
                layer.linetype = properties['linetype']
                layer.lineweight = properties['lineweight']
                
        except Exception as e:
            logger.warning(f"创建图层失败: {str(e)}")
    
    def _draw_roads(self, msp, roads: List[Dict]) -> None:
        """绘制道路"""
        try:
            for road in roads:
                coordinates = road.get('coordinates', [])
                if len(coordinates) < 2:
                    continue
                
                # 绘制道路中心线
                points = [Vec3(coord[0], coord[1], coord[2]) for coord in coordinates]
                
                if len(points) == 2:
                    # 直线
                    msp.add_line(points[0], points[1], dxfattribs={'layer': 'ROAD_CENTERLINE'})
                else:
                    # 折线
                    msp.add_lwpolyline(points, dxfattribs={'layer': 'ROAD_CENTERLINE'})
                
                # 绘制道路边线（如果有宽度信息）
                road_width = road.get('width', 6.0)  # 默认6米宽
                if road_width > 0:
                    self._draw_road_edges(msp, points, road_width)
                
                # 添加道路标注
                if road.get('name'):
                    mid_point = self._get_polyline_midpoint(points)
                    msp.add_text(
                        road['name'],
                        dxfattribs={'layer': 'TEXT', 'height': 2.0}
                    ).set_pos(mid_point)
                    
        except Exception as e:
            logger.warning(f"绘制道路失败: {str(e)}")
    
    def _draw_road_edges(self, msp, centerline_points: List[Vec3], width: float) -> None:
        """绘制道路边线"""
        try:
            half_width = width / 2
            
            # 计算左右边线点
            left_points = []
            right_points = []
            
            for i in range(len(centerline_points)):
                if i == 0:
                    # 第一个点，使用下一个点的方向
                    if len(centerline_points) > 1:
                        direction = (centerline_points[1] - centerline_points[0]).normalize()
                    else:
                        direction = Vec3(1, 0, 0)
                elif i == len(centerline_points) - 1:
                    # 最后一个点，使用前一个点的方向
                    direction = (centerline_points[i] - centerline_points[i-1]).normalize()
                else:
                    # 中间点，使用平均方向
                    dir1 = (centerline_points[i] - centerline_points[i-1]).normalize()
                    dir2 = (centerline_points[i+1] - centerline_points[i]).normalize()
                    direction = (dir1 + dir2).normalize()
                
                # 计算垂直方向
                perpendicular = Vec3(-direction.y, direction.x, 0).normalize()
                
                # 计算左右边线点
                center = centerline_points[i]
                left_point = center + perpendicular * half_width
                right_point = center - perpendicular * half_width
                
                left_points.append(left_point)
                right_points.append(right_point)
            
            # 绘制边线
            if len(left_points) > 1:
                msp.add_lwpolyline(left_points, dxfattribs={'layer': 'ROAD_EDGE'})
                msp.add_lwpolyline(right_points, dxfattribs={'layer': 'ROAD_EDGE'})
                
        except Exception as e:
            logger.warning(f"绘制道路边线失败: {str(e)}")
    
    def _draw_points(self, msp, points: List[Dict]) -> None:
        """绘制点位"""
        try:
            for point in points:
                x = point.get('x', 0)
                y = point.get('y', 0)
                z = point.get('z', 0)
                
                # 绘制点
                msp.add_point(Vec3(x, y, z), dxfattribs={'layer': 'POINTS'})
                
                # 添加点号标注
                point_id = point.get('point_id', '')
                if point_id:
                    msp.add_text(
                        point_id,
                        dxfattribs={'layer': 'TEXT', 'height': 1.0}
                    ).set_pos(Vec3(x + 1, y + 1, z))
                    
        except Exception as e:
            logger.warning(f"绘制点位失败: {str(e)}")
    
    def _draw_contours(self, msp, contours: List[Dict]) -> None:
        """绘制等高线"""
        try:
            for contour in contours:
                coordinates = contour.get('coordinates', [])
                elevation = contour.get('elevation', 0)
                
                if len(coordinates) < 2:
                    continue
                
                points = [Vec3(coord[0], coord[1], elevation) for coord in coordinates]
                
                # 绘制等高线
                msp.add_lwpolyline(points, dxfattribs={'layer': 'CONTOURS'})
                
                # 添加高程标注
                if len(points) > 0:
                    mid_point = self._get_polyline_midpoint(points)
                    msp.add_text(
                        f"{elevation:.1f}",
                        dxfattribs={'layer': 'TEXT', 'height': 0.8}
                    ).set_pos(mid_point)
                    
        except Exception as e:
            logger.warning(f"绘制等高线失败: {str(e)}")
    
    def _draw_annotations(self, msp, annotations: List[Dict]) -> None:
        """绘制注释"""
        try:
            for annotation in annotations:
                x = annotation.get('x', 0)
                y = annotation.get('y', 0)
                z = annotation.get('z', 0)
                text = annotation.get('text', '')
                height = annotation.get('height', 2.0)
                rotation = annotation.get('rotation', 0.0)
                
                if text:
                    msp.add_text(
                        text,
                        dxfattribs={
                            'layer': 'TEXT',
                            'height': height,
                            'rotation': math.radians(rotation)
                        }
                    ).set_pos(Vec3(x, y, z))
                    
        except Exception as e:
            logger.warning(f"绘制注释失败: {str(e)}")
    
    def _draw_longitudinal_profile(self, msp, profile_data: Dict) -> None:
        """绘制纵断面"""
        try:
            grade_points = profile_data.get('grade_points', [])
            if not grade_points:
                return
            
            # 绘制地面线
            ground_points = []
            design_points = []
            
            for point in grade_points:
                chainage = point.get('chainage', 0)
                ground_elevation = point.get('ground_elevation', 0)
                design_elevation = point.get('elevation', 0)
                
                ground_points.append(Vec3(chainage, ground_elevation, 0))
                design_points.append(Vec3(chainage, design_elevation, 0))
            
            # 绘制地面线
            if len(ground_points) > 1:
                msp.add_lwpolyline(ground_points, dxfattribs={'layer': 'CONTOURS'})
            
            # 绘制设计线
            if len(design_points) > 1:
                msp.add_lwpolyline(design_points, dxfattribs={'layer': 'ROAD_CENTERLINE'})
            
            # 添加里程标注
            for i, point in enumerate(grade_points):
                if i % 5 == 0:  # 每5个点标注一次
                    chainage = point.get('chainage', 0)
                    elevation = point.get('elevation', 0)
                    
                    msp.add_text(
                        f"K{chainage/1000:.3f}",
                        dxfattribs={'layer': 'TEXT', 'height': 1.0}
                    ).set_pos(Vec3(chainage, elevation - 5, 0))
                    
        except Exception as e:
            logger.warning(f"绘制纵断面失败: {str(e)}")
    
    def _draw_cross_sections(self, msp, cross_section_data: Dict) -> None:
        """绘制横断面"""
        try:
            sections = cross_section_data.get('sections', [])
            
            y_offset = 0
            section_spacing = 50  # 横断面间距
            
            for section in sections:
                chainage = section.get('chainage', 0)
                left_points = section.get('left_points', [])
                right_points = section.get('right_points', [])
                center_elevation = section.get('center_elevation', 0)
                
                # 绘制地面线
                all_points = []
                
                # 左侧点（反向）
                for offset, elevation in reversed(left_points):
                    all_points.append(Vec3(-abs(offset), elevation + y_offset, 0))
                
                # 中心点
                all_points.append(Vec3(0, center_elevation + y_offset, 0))
                
                # 右侧点
                for offset, elevation in right_points:
                    all_points.append(Vec3(abs(offset), elevation + y_offset, 0))
                
                # 绘制横断面线
                if len(all_points) > 1:
                    msp.add_lwpolyline(all_points, dxfattribs={'layer': 'CONTOURS'})
                
                # 添加里程标注
                msp.add_text(
                    f"K{chainage/1000:.3f}",
                    dxfattribs={'layer': 'TEXT', 'height': 2.0}
                ).set_pos(Vec3(-30, center_elevation + y_offset, 0))
                
                y_offset += section_spacing
                
        except Exception as e:
            logger.warning(f"绘制横断面失败: {str(e)}")
    
    def _add_title_block(self, msp, template: DrawingTemplate, data: Dict) -> None:
        """添加标题栏"""
        try:
            # 标题栏位置（右下角）
            title_x = 200
            title_y = 20
            title_width = 180
            title_height = 60
            
            # 绘制标题栏边框
            title_points = [
                Vec3(title_x, title_y, 0),
                Vec3(title_x + title_width, title_y, 0),
                Vec3(title_x + title_width, title_y + title_height, 0),
                Vec3(title_x, title_y + title_height, 0),
                Vec3(title_x, title_y, 0)
            ]
            
            msp.add_lwpolyline(title_points, dxfattribs={'layer': 'TITLE_BLOCK'})
            
            # 添加标题栏内容
            project_name = data.get('project_name', '露天矿山道路设计')
            drawing_type = data.get('type', 'plan')
            
            type_names = {
                'plan': '平面图',
                'profile': '纵断面图',
                'cross_section': '横断面图'
            }
            
            drawing_name = type_names.get(drawing_type, '设计图')
            
            # 项目名称
            msp.add_text(
                project_name,
                dxfattribs={'layer': 'TEXT', 'height': 4.0}
            ).set_pos(Vec3(title_x + 10, title_y + 40, 0))
            
            # 图纸名称
            msp.add_text(
                drawing_name,
                dxfattribs={'layer': 'TEXT', 'height': 3.0}
            ).set_pos(Vec3(title_x + 10, title_y + 30, 0))
            
            # 比例
            msp.add_text(
                f"比例 1:{int(template.scale)}",
                dxfattribs={'layer': 'TEXT', 'height': 2.0}
            ).set_pos(Vec3(title_x + 10, title_y + 20, 0))
            
            # 日期
            from datetime import datetime
            date_str = datetime.now().strftime("%Y-%m-%d")
            msp.add_text(
                f"日期: {date_str}",
                dxfattribs={'layer': 'TEXT', 'height': 2.0}
            ).set_pos(Vec3(title_x + 10, title_y + 10, 0))
            
        except Exception as e:
            logger.warning(f"添加标题栏失败: {str(e)}")
    
    def _add_scale_and_north(self, msp, scale: float) -> None:
        """添加比例尺和指北针"""
        try:
            # 比例尺位置
            scale_x = 20
            scale_y = 20
            scale_length = 100  # 比例尺长度（图纸单位）
            
            # 绘制比例尺
            scale_points = [
                Vec3(scale_x, scale_y, 0),
                Vec3(scale_x + scale_length, scale_y, 0)
            ]
            
            msp.add_line(scale_points[0], scale_points[1], dxfattribs={'layer': 'DIMENSIONS'})
            
            # 比例尺刻度
            for i in range(6):
                tick_x = scale_x + i * scale_length / 5
                tick_start = Vec3(tick_x, scale_y - 2, 0)
                tick_end = Vec3(tick_x, scale_y + 2, 0)
                msp.add_line(tick_start, tick_end, dxfattribs={'layer': 'DIMENSIONS'})
                
                # 标注
                real_distance = i * scale_length * scale / 5 / 1000  # 转换为公里
                msp.add_text(
                    f"{real_distance:.1f}km",
                    dxfattribs={'layer': 'TEXT', 'height': 1.5}
                ).set_pos(Vec3(tick_x, scale_y - 8, 0))
            
            # 指北针
            north_x = scale_x + scale_length + 30
            north_y = scale_y + 10
            arrow_length = 15
            
            # 绘制指北针箭头
            north_points = [
                Vec3(north_x, north_y, 0),
                Vec3(north_x, north_y + arrow_length, 0),
                Vec3(north_x - 3, north_y + arrow_length - 5, 0),
                Vec3(north_x, north_y + arrow_length, 0),
                Vec3(north_x + 3, north_y + arrow_length - 5, 0)
            ]
            
            for i in range(len(north_points) - 1):
                msp.add_line(north_points[i], north_points[i + 1], dxfattribs={'layer': 'DIMENSIONS'})
            
            # 添加"N"标注
            msp.add_text(
                "N",
                dxfattribs={'layer': 'TEXT', 'height': 3.0}
            ).set_pos(Vec3(north_x - 2, north_y + arrow_length + 3, 0))
            
        except Exception as e:
            logger.warning(f"添加比例尺和指北针失败: {str(e)}")
    
    def _get_polyline_midpoint(self, points: List[Vec3]) -> Vec3:
        """获取折线中点"""
        try:
            if not points:
                return Vec3(0, 0, 0)
            
            if len(points) == 1:
                return points[0]
            
            # 计算总长度
            total_length = 0
            lengths = []
            
            for i in range(len(points) - 1):
                length = (points[i + 1] - points[i]).magnitude
                lengths.append(length)
                total_length += length
            
            # 找到中点
            half_length = total_length / 2
            current_length = 0
            
            for i, length in enumerate(lengths):
                if current_length + length >= half_length:
                    # 中点在这一段内
                    ratio = (half_length - current_length) / length
                    return points[i] + (points[i + 1] - points[i]) * ratio
                current_length += length
            
            return points[-1]
            
        except Exception as e:
            logger.warning(f"计算折线中点失败: {str(e)}")
            return Vec3(0, 0, 0)
    
    def get_standard_templates(self) -> List[DrawingTemplate]:
        """获取标准图纸模板"""
        templates = [
            DrawingTemplate(
                name="道路平面图-A1",
                paper_size="A1",
                scale=1000,
                units="m",
                title_block=True
            ),
            DrawingTemplate(
                name="纵断面图-A1",
                paper_size="A1",
                scale=1000,
                units="m",
                title_block=True
            ),
            DrawingTemplate(
                name="横断面图-A2",
                paper_size="A2",
                scale=200,
                units="m",
                title_block=True
            ),
            DrawingTemplate(
                name="详图-A3",
                paper_size="A3",
                scale=100,
                units="m",
                title_block=True
            )
        ]
        
        return templates
