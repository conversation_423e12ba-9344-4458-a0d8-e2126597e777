"""
运输路线优化核心算法
"""
import numpy as np
import math
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import heapq
import logging

logger = logging.getLogger(__name__)


class OptimizationObjective(Enum):
    """优化目标枚举"""
    MINIMIZE_DISTANCE = "minimize_distance"
    MINIMIZE_TIME = "minimize_time"
    MINIMIZE_COST = "minimize_cost"
    MINIMIZE_FUEL = "minimize_fuel"
    MAXIMIZE_SAFETY = "maximize_safety"
    MINIMIZE_ENVIRONMENTAL_IMPACT = "minimize_environmental_impact"


class VehicleType(Enum):
    """车辆类型枚举"""
    DUMP_TRUCK = "dump_truck"
    EXCAVATOR = "excavator"
    BULLDOZER = "bulldozer"
    GRADER = "grader"
    WATER_TRUCK = "water_truck"


@dataclass
class RouteNode:
    """路线节点"""
    node_id: str
    x: float
    y: float
    z: float
    node_type: str  # 'origin', 'destination', 'waypoint', 'junction'
    capacity: Optional[float] = None
    restrictions: Optional[Dict] = None


@dataclass
class RouteEdge:
    """路线边"""
    edge_id: str
    from_node: str
    to_node: str
    distance: float
    travel_time: float
    cost: float
    gradient: float
    road_condition: str  # 'excellent', 'good', 'fair', 'poor'
    width: float
    weight_limit: Optional[float] = None
    speed_limit: Optional[float] = None


@dataclass
class Vehicle:
    """车辆信息"""
    vehicle_id: str
    vehicle_type: VehicleType
    capacity: float
    max_speed: float
    fuel_consumption: float  # L/100km
    operating_cost: float  # 元/小时
    weight: float
    dimensions: Tuple[float, float, float]  # 长、宽、高


@dataclass
class TransportTask:
    """运输任务"""
    task_id: str
    origin: str
    destination: str
    cargo_volume: float
    cargo_weight: float
    priority: int  # 1-5, 5为最高优先级
    time_window: Optional[Tuple[float, float]] = None  # 时间窗口
    vehicle_requirements: Optional[List[VehicleType]] = None


@dataclass
class OptimizedRoute:
    """优化后的路线"""
    route_id: str
    task_id: str
    vehicle_id: str
    path: List[str]  # 节点序列
    total_distance: float
    total_time: float
    total_cost: float
    fuel_consumption: float
    safety_score: float
    environmental_impact: float
    route_efficiency: float


class RouteOptimizer:
    """运输路线优化器主类"""
    
    def __init__(self):
        self.nodes: Dict[str, RouteNode] = {}
        self.edges: Dict[str, RouteEdge] = {}
        self.adjacency_list: Dict[str, List[str]] = {}
        
        # 优化参数
        self.optimization_weights = {
            OptimizationObjective.MINIMIZE_DISTANCE: 0.3,
            OptimizationObjective.MINIMIZE_TIME: 0.3,
            OptimizationObjective.MINIMIZE_COST: 0.2,
            OptimizationObjective.MINIMIZE_FUEL: 0.1,
            OptimizationObjective.MAXIMIZE_SAFETY: 0.1
        }
    
    def build_road_network(self, 
                          nodes: List[RouteNode],
                          edges: List[RouteEdge]) -> None:
        """构建道路网络"""
        try:
            logger.info("构建道路网络")
            
            # 添加节点
            for node in nodes:
                self.nodes[node.node_id] = node
                self.adjacency_list[node.node_id] = []
            
            # 添加边
            for edge in edges:
                self.edges[edge.edge_id] = edge
                
                # 构建邻接表
                if edge.from_node in self.adjacency_list:
                    self.adjacency_list[edge.from_node].append(edge.to_node)
                
                # 如果是双向道路，添加反向边
                if edge.road_condition != 'one_way':
                    if edge.to_node in self.adjacency_list:
                        self.adjacency_list[edge.to_node].append(edge.from_node)
            
            logger.info(f"道路网络构建完成: {len(self.nodes)}个节点, {len(self.edges)}条边")
            
        except Exception as e:
            logger.error(f"构建道路网络失败: {str(e)}")
            raise
    
    def optimize_routes(self, 
                       tasks: List[TransportTask],
                       vehicles: List[Vehicle],
                       objectives: List[OptimizationObjective] = None) -> List[OptimizedRoute]:
        """优化运输路线"""
        try:
            logger.info(f"开始优化 {len(tasks)} 个运输任务")
            
            if objectives is None:
                objectives = [OptimizationObjective.MINIMIZE_DISTANCE, 
                             OptimizationObjective.MINIMIZE_TIME]
            
            optimized_routes = []
            
            for task in tasks:
                # 为每个任务找到最优路线
                best_route = self._optimize_single_task(task, vehicles, objectives)
                if best_route:
                    optimized_routes.append(best_route)
            
            # 全局优化（考虑车辆调度）
            optimized_routes = self._global_optimization(optimized_routes, vehicles)
            
            logger.info(f"路线优化完成，生成 {len(optimized_routes)} 条优化路线")
            return optimized_routes
            
        except Exception as e:
            logger.error(f"路线优化失败: {str(e)}")
            raise
    
    def _optimize_single_task(self, 
                             task: TransportTask,
                             vehicles: List[Vehicle],
                             objectives: List[OptimizationObjective]) -> Optional[OptimizedRoute]:
        """优化单个运输任务"""
        try:
            # 筛选合适的车辆
            suitable_vehicles = self._filter_suitable_vehicles(task, vehicles)
            if not suitable_vehicles:
                logger.warning(f"任务 {task.task_id} 没有合适的车辆")
                return None
            
            best_route = None
            best_score = float('inf')
            
            for vehicle in suitable_vehicles:
                # 使用A*算法找到最短路径
                path = self._find_shortest_path(task.origin, task.destination, vehicle)
                
                if path:
                    # 计算路线指标
                    route_metrics = self._calculate_route_metrics(path, vehicle, task)
                    
                    # 计算综合评分
                    score = self._calculate_objective_score(route_metrics, objectives)
                    
                    if score < best_score:
                        best_score = score
                        best_route = OptimizedRoute(
                            route_id=f"route_{task.task_id}_{vehicle.vehicle_id}",
                            task_id=task.task_id,
                            vehicle_id=vehicle.vehicle_id,
                            path=path,
                            total_distance=route_metrics['distance'],
                            total_time=route_metrics['time'],
                            total_cost=route_metrics['cost'],
                            fuel_consumption=route_metrics['fuel'],
                            safety_score=route_metrics['safety'],
                            environmental_impact=route_metrics['environmental'],
                            route_efficiency=1.0 / score if score > 0 else 0
                        )
            
            return best_route
            
        except Exception as e:
            logger.error(f"单任务优化失败: {str(e)}")
            return None
    
    def _filter_suitable_vehicles(self, 
                                 task: TransportTask,
                                 vehicles: List[Vehicle]) -> List[Vehicle]:
        """筛选合适的车辆"""
        suitable_vehicles = []
        
        for vehicle in vehicles:
            # 检查载重能力
            if vehicle.capacity < task.cargo_volume:
                continue
            
            # 检查车辆类型要求
            if (task.vehicle_requirements and 
                vehicle.vehicle_type not in task.vehicle_requirements):
                continue
            
            suitable_vehicles.append(vehicle)
        
        return suitable_vehicles
    
    def _find_shortest_path(self, 
                           origin: str,
                           destination: str,
                           vehicle: Vehicle) -> Optional[List[str]]:
        """使用A*算法找到最短路径"""
        try:
            if origin not in self.nodes or destination not in self.nodes:
                return None
            
            # A*算法实现
            open_set = [(0, origin, [origin])]
            closed_set = set()
            g_scores = {origin: 0}
            
            while open_set:
                current_f, current_node, path = heapq.heappop(open_set)
                
                if current_node == destination:
                    return path
                
                if current_node in closed_set:
                    continue
                
                closed_set.add(current_node)
                
                # 检查所有邻居节点
                for neighbor in self.adjacency_list.get(current_node, []):
                    if neighbor in closed_set:
                        continue
                    
                    # 找到连接边
                    edge = self._find_edge(current_node, neighbor)
                    if not edge or not self._is_vehicle_compatible(edge, vehicle):
                        continue
                    
                    # 计算g值（实际成本）
                    tentative_g = g_scores[current_node] + self._calculate_edge_cost(edge, vehicle)
                    
                    if neighbor not in g_scores or tentative_g < g_scores[neighbor]:
                        g_scores[neighbor] = tentative_g
                        
                        # 计算h值（启发式估计）
                        h_score = self._heuristic_distance(neighbor, destination)
                        f_score = tentative_g + h_score
                        
                        new_path = path + [neighbor]
                        heapq.heappush(open_set, (f_score, neighbor, new_path))
            
            return None  # 没有找到路径
            
        except Exception as e:
            logger.error(f"路径搜索失败: {str(e)}")
            return None
    
    def _find_edge(self, from_node: str, to_node: str) -> Optional[RouteEdge]:
        """查找连接两个节点的边"""
        for edge in self.edges.values():
            if edge.from_node == from_node and edge.to_node == to_node:
                return edge
            # 检查双向道路
            if (edge.to_node == from_node and edge.from_node == to_node and 
                edge.road_condition != 'one_way'):
                return edge
        return None
    
    def _is_vehicle_compatible(self, edge: RouteEdge, vehicle: Vehicle) -> bool:
        """检查车辆是否能通过该边"""
        # 检查重量限制
        if edge.weight_limit and vehicle.weight > edge.weight_limit:
            return False
        
        # 检查宽度限制
        if edge.width < vehicle.dimensions[1]:  # 车辆宽度
            return False
        
        return True
    
    def _calculate_edge_cost(self, edge: RouteEdge, vehicle: Vehicle) -> float:
        """计算边的通行成本"""
        # 基础距离成本
        distance_cost = edge.distance
        
        # 时间成本
        speed = min(vehicle.max_speed, edge.speed_limit or vehicle.max_speed)
        time_cost = edge.distance / speed
        
        # 燃料成本
        fuel_factor = 1.0
        if edge.gradient > 5:  # 上坡增加油耗
            fuel_factor += edge.gradient / 100
        
        fuel_cost = (edge.distance / 100) * vehicle.fuel_consumption * fuel_factor
        
        # 道路条件影响
        condition_factor = {
            'excellent': 1.0,
            'good': 1.1,
            'fair': 1.3,
            'poor': 1.6
        }.get(edge.road_condition, 1.2)
        
        return distance_cost * condition_factor + time_cost + fuel_cost
    
    def _heuristic_distance(self, node1: str, node2: str) -> float:
        """计算启发式距离（欧几里得距离）"""
        if node1 not in self.nodes or node2 not in self.nodes:
            return 0
        
        n1 = self.nodes[node1]
        n2 = self.nodes[node2]
        
        return math.sqrt((n1.x - n2.x)**2 + (n1.y - n2.y)**2 + (n1.z - n2.z)**2)
    
    def _calculate_route_metrics(self, 
                                path: List[str],
                                vehicle: Vehicle,
                                task: TransportTask) -> Dict[str, float]:
        """计算路线指标"""
        try:
            total_distance = 0
            total_time = 0
            total_fuel = 0
            safety_scores = []
            environmental_impacts = []
            
            for i in range(len(path) - 1):
                edge = self._find_edge(path[i], path[i + 1])
                if edge:
                    total_distance += edge.distance
                    
                    # 计算行驶时间
                    speed = min(vehicle.max_speed, edge.speed_limit or vehicle.max_speed)
                    travel_time = edge.distance / speed
                    total_time += travel_time
                    
                    # 计算燃料消耗
                    fuel_factor = 1.0
                    if edge.gradient > 0:
                        fuel_factor += edge.gradient / 100
                    
                    fuel_consumption = (edge.distance / 100) * vehicle.fuel_consumption * fuel_factor
                    total_fuel += fuel_consumption
                    
                    # 安全评分（基于道路条件和坡度）
                    safety_score = self._calculate_safety_score(edge)
                    safety_scores.append(safety_score)
                    
                    # 环境影响（基于燃料消耗和道路类型）
                    env_impact = fuel_consumption * 2.3  # CO2排放系数
                    environmental_impacts.append(env_impact)
            
            # 计算总成本
            total_cost = (total_time * vehicle.operating_cost + 
                         total_fuel * 7.5)  # 假设燃料价格7.5元/升
            
            return {
                'distance': total_distance,
                'time': total_time,
                'cost': total_cost,
                'fuel': total_fuel,
                'safety': sum(safety_scores) / len(safety_scores) if safety_scores else 0,
                'environmental': sum(environmental_impacts)
            }
            
        except Exception as e:
            logger.error(f"计算路线指标失败: {str(e)}")
            return {}
    
    def _calculate_safety_score(self, edge: RouteEdge) -> float:
        """计算边的安全评分"""
        base_score = 100
        
        # 道路条件影响
        condition_penalty = {
            'excellent': 0,
            'good': 5,
            'fair': 15,
            'poor': 30
        }.get(edge.road_condition, 20)
        
        # 坡度影响
        gradient_penalty = max(0, abs(edge.gradient) - 5) * 2
        
        # 宽度影响
        width_penalty = max(0, 6 - edge.width) * 5
        
        safety_score = base_score - condition_penalty - gradient_penalty - width_penalty
        return max(0, safety_score)
    
    def _calculate_objective_score(self, 
                                  metrics: Dict[str, float],
                                  objectives: List[OptimizationObjective]) -> float:
        """计算多目标优化评分"""
        score = 0
        
        for objective in objectives:
            weight = self.optimization_weights.get(objective, 0.1)
            
            if objective == OptimizationObjective.MINIMIZE_DISTANCE:
                score += weight * metrics.get('distance', 0)
            elif objective == OptimizationObjective.MINIMIZE_TIME:
                score += weight * metrics.get('time', 0)
            elif objective == OptimizationObjective.MINIMIZE_COST:
                score += weight * metrics.get('cost', 0)
            elif objective == OptimizationObjective.MINIMIZE_FUEL:
                score += weight * metrics.get('fuel', 0)
            elif objective == OptimizationObjective.MAXIMIZE_SAFETY:
                score += weight * (100 - metrics.get('safety', 0))
            elif objective == OptimizationObjective.MINIMIZE_ENVIRONMENTAL_IMPACT:
                score += weight * metrics.get('environmental', 0)
        
        return score
    
    def _global_optimization(self, 
                           routes: List[OptimizedRoute],
                           vehicles: List[Vehicle]) -> List[OptimizedRoute]:
        """全局优化（考虑车辆调度冲突）"""
        try:
            # 简化的全局优化：检查车辆时间冲突
            vehicle_schedules = {}
            optimized_routes = []
            
            # 按优先级和效率排序
            routes.sort(key=lambda r: r.route_efficiency, reverse=True)
            
            for route in routes:
                vehicle_id = route.vehicle_id
                
                # 检查车辆是否可用
                if vehicle_id not in vehicle_schedules:
                    vehicle_schedules[vehicle_id] = []
                
                # 简化：假设任务按顺序执行，不考虑时间窗口重叠
                vehicle_schedules[vehicle_id].append(route)
                optimized_routes.append(route)
            
            return optimized_routes
            
        except Exception as e:
            logger.error(f"全局优化失败: {str(e)}")
            return routes
    
    def analyze_route_performance(self, routes: List[OptimizedRoute]) -> Dict[str, Any]:
        """分析路线性能"""
        try:
            if not routes:
                return {}
            
            total_distance = sum(r.total_distance for r in routes)
            total_time = sum(r.total_time for r in routes)
            total_cost = sum(r.total_cost for r in routes)
            total_fuel = sum(r.fuel_consumption for r in routes)
            avg_safety = sum(r.safety_score for r in routes) / len(routes)
            total_environmental = sum(r.environmental_impact for r in routes)
            
            # 效率分析
            avg_efficiency = sum(r.route_efficiency for r in routes) / len(routes)
            
            # 车辆利用率
            used_vehicles = len(set(r.vehicle_id for r in routes))
            
            return {
                'total_routes': len(routes),
                'total_distance': total_distance,
                'total_time': total_time,
                'total_cost': total_cost,
                'total_fuel_consumption': total_fuel,
                'average_safety_score': avg_safety,
                'total_environmental_impact': total_environmental,
                'average_efficiency': avg_efficiency,
                'vehicle_utilization': used_vehicles,
                'cost_per_km': total_cost / total_distance if total_distance > 0 else 0,
                'fuel_efficiency': total_distance / total_fuel if total_fuel > 0 else 0
            }
            
        except Exception as e:
            logger.error(f"路线性能分析失败: {str(e)}")
            return {}
    
    def generate_optimization_report(self, 
                                   routes: List[OptimizedRoute],
                                   tasks: List[TransportTask]) -> Dict[str, Any]:
        """生成优化报告"""
        try:
            performance = self.analyze_route_performance(routes)
            
            # 任务完成率
            completed_tasks = len(routes)
            total_tasks = len(tasks)
            completion_rate = completed_tasks / total_tasks if total_tasks > 0 else 0
            
            # 优化建议
            recommendations = self._generate_optimization_recommendations(routes, performance)
            
            return {
                'optimization_summary': {
                    'total_tasks': total_tasks,
                    'completed_tasks': completed_tasks,
                    'completion_rate': completion_rate * 100,
                    'optimization_time': 'current'
                },
                'performance_metrics': performance,
                'route_details': [
                    {
                        'route_id': r.route_id,
                        'task_id': r.task_id,
                        'vehicle_id': r.vehicle_id,
                        'distance': r.total_distance,
                        'time': r.total_time,
                        'cost': r.total_cost,
                        'efficiency': r.route_efficiency
                    }
                    for r in routes
                ],
                'recommendations': recommendations
            }
            
        except Exception as e:
            logger.error(f"生成优化报告失败: {str(e)}")
            return {}
    
    def _generate_optimization_recommendations(self, 
                                             routes: List[OptimizedRoute],
                                             performance: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []
        
        # 安全建议
        avg_safety = performance.get('average_safety_score', 0)
        if avg_safety < 70:
            recommendations.append("平均安全评分较低，建议优先选择安全性更好的道路")
        
        # 效率建议
        avg_efficiency = performance.get('average_efficiency', 0)
        if avg_efficiency < 0.7:
            recommendations.append("路线效率有待提高，建议重新评估优化目标权重")
        
        # 成本建议
        cost_per_km = performance.get('cost_per_km', 0)
        if cost_per_km > 50:
            recommendations.append("运输成本较高，建议考虑使用更经济的车辆或路线")
        
        # 燃料效率建议
        fuel_efficiency = performance.get('fuel_efficiency', 0)
        if fuel_efficiency < 3:  # km/L
            recommendations.append("燃料效率较低，建议优化车辆选择或避开陡坡路段")
        
        # 车辆利用率建议
        vehicle_utilization = performance.get('vehicle_utilization', 0)
        total_routes = performance.get('total_routes', 0)
        if vehicle_utilization > 0 and total_routes / vehicle_utilization > 3:
            recommendations.append("车辆利用率较高，建议增加车辆数量或优化调度")
        
        return recommendations
