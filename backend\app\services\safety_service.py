"""
安全检测服务
"""
import os
import json
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import asyncio

from app.models.safety_analysis import SafetyAnalysis, SafetyIssue as DBSafetyIssue
from app.models.road import Road
from app.models.project import Project
from app.core.config import settings
from algorithms.safety_analysis.safety_analyzer import (
    SafetyAnalyzer, RoadGeometry, SafetyAnalysisResult, SafetyIssue, SafetyLevel
)
from algorithms.safety_analysis.sight_distance_analyzer import SightDistanceAnalyzer
import logging

logger = logging.getLogger(__name__)


class SafetyService:
    """安全检测服务类"""
    
    def __init__(self):
        self.safety_analyzer = SafetyAnalyzer()
        self.sight_analyzer = SightDistanceAnalyzer()
    
    async def analyze_road_safety(self, 
                                 db: Session,
                                 road_id: int,
                                 analysis_params: Dict = None) -> SafetyAnalysis:
        """分析道路安全性"""
        try:
            # 获取道路信息
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            logger.info(f"开始道路安全分析: {road.name} (ID: {road_id})")
            
            # 构建道路几何数据
            road_geometry = await self._build_road_geometry(db, road)
            
            # 获取地形数据
            terrain_data = await self._get_terrain_data(db, road.project_id)
            
            # 获取交通数据
            traffic_data = analysis_params.get('traffic_data') if analysis_params else None
            
            # 执行安全分析
            analysis_result = await asyncio.get_event_loop().run_in_executor(
                None,
                self.safety_analyzer.analyze_road_safety,
                road_geometry,
                terrain_data,
                traffic_data
            )
            
            # 保存分析结果到数据库
            safety_analysis = await self._save_analysis_result(db, road, analysis_result)
            
            logger.info(f"道路安全分析完成，发现 {analysis_result.total_issues} 个安全问题")
            return safety_analysis
            
        except Exception as e:
            logger.error(f"道路安全分析失败: {str(e)}")
            raise
    
    async def analyze_sight_distance(self, 
                                   db: Session,
                                   road_id: int) -> Dict:
        """分析道路视距"""
        try:
            # 获取道路信息
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            # 构建道路几何数据
            road_geometry = await self._build_road_geometry(db, road)
            
            # 执行视距分析
            sight_results = await asyncio.get_event_loop().run_in_executor(
                None,
                self.sight_analyzer.analyze_sight_distance,
                road_geometry.points,
                road_geometry.chainages,
                road_geometry.gradients,
                road_geometry.design_speed
            )
            
            # 生成视距报告
            sight_report = self.sight_analyzer.generate_sight_distance_report(sight_results)
            
            return {
                'road_id': road_id,
                'analysis_results': [
                    {
                        'chainage': result.chainage,
                        'location': result.location,
                        'stopping_sight_distance': result.stopping_sight_distance,
                        'actual_sight_distance': result.actual_sight_distance,
                        'is_adequate': result.is_adequate,
                        'deficiency': result.deficiency,
                        'recommendations': result.recommendations
                    }
                    for result in sight_results
                ],
                'report': sight_report
            }
            
        except Exception as e:
            logger.error(f"视距分析失败: {str(e)}")
            raise
    
    async def _build_road_geometry(self, db: Session, road: Road) -> RoadGeometry:
        """构建道路几何数据"""
        try:
            # 从道路点位构建几何数据
            from app.models.road import RoadPoint, RoadSegment
            
            points_query = db.query(RoadPoint).join(RoadSegment).filter(
                RoadSegment.road_id == road.id
            ).order_by(RoadPoint.chainage)
            
            road_points = points_query.all()
            
            if not road_points:
                # 如果没有详细点位，使用段数据
                segments = db.query(RoadSegment).filter(RoadSegment.road_id == road.id).all()
                points = []
                chainages = []
                gradients = []
                curvatures = []
                widths = []
                
                for segment in segments:
                    if segment.start_point:
                        points.append(tuple(segment.start_point))
                        chainages.append(segment.start_chainage)
                        gradients.append(segment.gradient or 0.0)
                        curvatures.append(1.0 / segment.radius if segment.radius else 0.0)
                        widths.append(road.road_width)
                    
                    if segment.end_point:
                        points.append(tuple(segment.end_point))
                        chainages.append(segment.end_chainage)
                        gradients.append(segment.gradient or 0.0)
                        curvatures.append(1.0 / segment.radius if segment.radius else 0.0)
                        widths.append(road.road_width)
            else:
                # 使用详细点位数据
                points = [(p.x, p.y, p.z) for p in road_points]
                chainages = [p.chainage for p in road_points]
                gradients = [0.0] * len(road_points)  # TODO: 计算实际坡度
                curvatures = [0.0] * len(road_points)  # TODO: 计算实际曲率
                widths = [p.left_width + p.right_width for p in road_points]
            
            return RoadGeometry(
                points=points,
                chainages=chainages,
                gradients=gradients,
                curvatures=curvatures,
                widths=widths,
                design_speed=road.design_speed
            )
            
        except Exception as e:
            logger.error(f"构建道路几何数据失败: {str(e)}")
            raise
    
    async def _get_terrain_data(self, db: Session, project_id: int) -> Optional[any]:
        """获取地形数据"""
        try:
            from app.models.terrain import TerrainData
            
            terrain_list = db.query(TerrainData).filter(
                TerrainData.project_id == project_id,
                TerrainData.processing_status == 'processed'
            ).first()
            
            if terrain_list:
                # TODO: 加载实际的DEM数据
                return None  # 返回numpy数组
            
            return None
            
        except Exception as e:
            logger.error(f"获取地形数据失败: {str(e)}")
            return None
    
    async def _save_analysis_result(self, 
                                  db: Session, 
                                  road: Road, 
                                  result: SafetyAnalysisResult) -> SafetyAnalysis:
        """保存安全分析结果到数据库"""
        try:
            # 创建安全分析记录
            safety_analysis = SafetyAnalysis(
                project_id=road.project_id,
                road_id=road.id,
                analysis_type='comprehensive',
                overall_safety_score=result.overall_safety_score,
                safety_level=result.safety_level.value,
                total_issues=result.total_issues,
                critical_issues=result.critical_issues,
                warning_issues=result.warning_issues,
                analysis_summary=result.analysis_summary,
                recommendations=result.recommendations,
                analyzed_at=datetime.utcnow()
            )
            
            db.add(safety_analysis)
            db.commit()
            db.refresh(safety_analysis)
            
            # 保存安全问题
            for issue in result.issues:
                db_issue = DBSafetyIssue(
                    analysis_id=safety_analysis.id,
                    issue_type=issue.issue_type.value,
                    severity_level=issue.severity_level.value,
                    title=issue.description,
                    description=issue.description,
                    location=[issue.location[0], issue.location[1], issue.location[2]],
                    chainage=issue.chainage,
                    affected_length=issue.affected_length,
                    measured_values=issue.measured_values,
                    threshold_values=issue.threshold_values,
                    deviation=issue.deviation,
                    risk_score=issue.risk_score,
                    recommendations=issue.recommendations,
                    urgency_level=issue.urgency_level
                )
                
                db.add(db_issue)
            
            db.commit()
            
            return safety_analysis
            
        except Exception as e:
            logger.error(f"保存安全分析结果失败: {str(e)}")
            raise
    
    def get_project_safety_analyses(self, 
                                   db: Session,
                                   project_id: int,
                                   skip: int = 0,
                                   limit: int = 100) -> List[SafetyAnalysis]:
        """获取项目安全分析列表"""
        return db.query(SafetyAnalysis)\
                 .filter(SafetyAnalysis.project_id == project_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_road_safety_analyses(self, 
                               db: Session,
                               road_id: int,
                               skip: int = 0,
                               limit: int = 100) -> List[SafetyAnalysis]:
        """获取道路安全分析列表"""
        return db.query(SafetyAnalysis)\
                 .filter(SafetyAnalysis.road_id == road_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_safety_analysis_detail(self, db: Session, analysis_id: int) -> Optional[SafetyAnalysis]:
        """获取安全分析详情"""
        return db.query(SafetyAnalysis).filter(SafetyAnalysis.id == analysis_id).first()
    
    def get_safety_issues(self, 
                         db: Session,
                         analysis_id: int,
                         skip: int = 0,
                         limit: int = 100) -> List[DBSafetyIssue]:
        """获取安全问题列表"""
        return db.query(DBSafetyIssue)\
                 .filter(DBSafetyIssue.analysis_id == analysis_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_safety_statistics(self, db: Session, project_id: int) -> Dict:
        """获取安全统计信息"""
        try:
            analyses = db.query(SafetyAnalysis).filter(SafetyAnalysis.project_id == project_id).all()
            
            if not analyses:
                return {
                    'total_analyses': 0,
                    'average_safety_score': 0,
                    'total_issues': 0,
                    'critical_issues': 0,
                    'safety_trend': 'stable'
                }
            
            total_analyses = len(analyses)
            avg_safety_score = sum(a.overall_safety_score for a in analyses) / total_analyses
            total_issues = sum(a.total_issues for a in analyses)
            critical_issues = sum(a.critical_issues for a in analyses)
            
            # 计算安全趋势
            if total_analyses >= 2:
                recent_score = analyses[-1].overall_safety_score
                previous_score = analyses[-2].overall_safety_score
                if recent_score > previous_score + 5:
                    safety_trend = 'improving'
                elif recent_score < previous_score - 5:
                    safety_trend = 'declining'
                else:
                    safety_trend = 'stable'
            else:
                safety_trend = 'stable'
            
            # 按安全等级统计
            safety_level_stats = {}
            for analysis in analyses:
                level = analysis.safety_level
                safety_level_stats[level] = safety_level_stats.get(level, 0) + 1
            
            return {
                'total_analyses': total_analyses,
                'average_safety_score': round(avg_safety_score, 1),
                'total_issues': total_issues,
                'critical_issues': critical_issues,
                'safety_trend': safety_trend,
                'safety_level_distribution': safety_level_stats,
                'latest_analysis_date': analyses[-1].analyzed_at.isoformat() if analyses else None
            }
            
        except Exception as e:
            logger.error(f"获取安全统计失败: {str(e)}")
            return {}
    
    def generate_safety_report(self, db: Session, project_id: int) -> Dict:
        """生成安全报告"""
        try:
            analyses = db.query(SafetyAnalysis).filter(SafetyAnalysis.project_id == project_id).all()
            statistics = self.get_safety_statistics(db, project_id)
            
            # 获取所有安全问题
            all_issues = []
            for analysis in analyses:
                issues = db.query(DBSafetyIssue).filter(DBSafetyIssue.analysis_id == analysis.id).all()
                all_issues.extend(issues)
            
            # 生成报告
            report = {
                'project_id': project_id,
                'generated_at': datetime.utcnow().isoformat(),
                'summary': statistics,
                'analyses': [
                    {
                        'id': analysis.id,
                        'road_id': analysis.road_id,
                        'analysis_type': analysis.analysis_type,
                        'safety_score': analysis.overall_safety_score,
                        'safety_level': analysis.safety_level,
                        'total_issues': analysis.total_issues,
                        'analyzed_at': analysis.analyzed_at.isoformat()
                    }
                    for analysis in analyses
                ],
                'critical_issues': [
                    {
                        'issue_type': issue.issue_type,
                        'description': issue.description,
                        'location': issue.location,
                        'risk_score': issue.risk_score
                    }
                    for issue in all_issues if issue.severity_level == 'danger'
                ],
                'recommendations': self._generate_project_recommendations(analyses, all_issues)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成安全报告失败: {str(e)}")
            return {}
    
    def _generate_project_recommendations(self, 
                                        analyses: List[SafetyAnalysis],
                                        issues: List[DBSafetyIssue]) -> List[str]:
        """生成项目级安全建议"""
        recommendations = []
        
        if not analyses:
            return recommendations
        
        # 分析总体安全水平
        avg_score = sum(a.overall_safety_score for a in analyses) / len(analyses)
        if avg_score < 60:
            recommendations.append("项目整体安全水平较低，建议进行全面安全评估和改进")
        elif avg_score < 80:
            recommendations.append("项目安全水平中等，建议重点关注高风险区域")
        
        # 分析问题类型分布
        issue_types = {}
        for issue in issues:
            issue_types[issue.issue_type] = issue_types.get(issue.issue_type, 0) + 1
        
        if issue_types.get('sight_distance', 0) > 5:
            recommendations.append("视距问题较多，建议系统性优化道路线形设计")
        
        if issue_types.get('slope_stability', 0) > 3:
            recommendations.append("边坡稳定性问题需要重点关注，建议进行地质勘察")
        
        if issue_types.get('geometric_hazard', 0) > 4:
            recommendations.append("几何设计问题较多，建议检查设计标准执行情况")
        
        return recommendations
