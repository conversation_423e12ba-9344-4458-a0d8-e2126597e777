<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>露天矿山道路设计软件</title>
    <meta name="description" content="专业的露天矿山采矿工程道路设计软件" />
    <meta name="keywords" content="露天矿山,道路设计,采矿工程,GIS,三维建模" />
    
    <!-- Cesium CSS -->
    <link href="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Widgets/widgets.css" rel="stylesheet">
    
    <!-- 预加载关键资源 -->
    <link rel="preload" href="/fonts/main.woff2" as="font" type="font/woff2" crossorigin>
    
    <!-- 主题色 -->
    <meta name="theme-color" content="#faad14" />
    
    <!-- PWA支持 -->
    <link rel="manifest" href="/manifest.json" />
    
    <!-- 防止FOUC (Flash of Unstyled Content) -->
    <style>
      #root {
        min-height: 100vh;
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
      }
      
      .loading-screen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
      }
      
      .loading-spinner {
        width: 50px;
        height: 50px;
        border: 3px solid #333;
        border-top: 3px solid #faad14;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
      
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .loading-text {
        color: #faad14;
        font-size: 16px;
        margin-top: 20px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
    </style>
  </head>
  <body>
    <div id="root">
      <!-- 加载屏幕 -->
      <div class="loading-screen" id="loading-screen">
        <div style="text-align: center;">
          <div class="loading-spinner"></div>
          <div class="loading-text">正在加载露天矿山道路设计软件...</div>
        </div>
      </div>
    </div>
    
    <!-- Cesium JavaScript -->
    <script src="https://cesium.com/downloads/cesiumjs/releases/1.111/Build/Cesium/Cesium.js"></script>
    
    <!-- 应用入口 -->
    <script type="module" src="/src/main.tsx"></script>
    
    <!-- 移除加载屏幕 -->
    <script>
      window.addEventListener('load', function() {
        setTimeout(function() {
          const loadingScreen = document.getElementById('loading-screen');
          if (loadingScreen) {
            loadingScreen.style.opacity = '0';
            loadingScreen.style.transition = 'opacity 0.5s ease-out';
            setTimeout(function() {
              loadingScreen.remove();
            }, 500);
          }
        }, 1000);
      });
    </script>
  </body>
</html>
