import React, { useState, useEffect } from 'react'
import {
  Card, Button, Table, Form, Input, Select, InputNumber,
  Modal, Space, Tag, Tooltip, message, Row, Col, Statistic,
  Descriptions, Progress, Popconfirm
} from 'antd'
import {
  PlusOutlined, EditOutlined, DeleteOutlined, <PERSON>Outlined,
  <PERSON>Outlined, <PERSON><PERSON><PERSON>Outlined, <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON>Outlined, Check<PERSON>ircleOutlined, ExclamationCircleOutlined
} from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import axios from 'axios'

interface Point3D {
  x: number
  y: number
  z?: number
}

interface RoadData {
  id: number
  name: string
  description?: string
  road_type: string
  design_speed: number
  design_load: number
  road_width: number
  total_length: number
  max_gradient?: number
  min_turning_radius?: number
  design_status: string
  earthwork_cut?: number
  earthwork_fill?: number
  created_at: string
}

interface RoadDesignParams {
  name: string
  description?: string
  road_type: string
  start_point: Point3D
  end_point: Point3D
  control_points?: Point3D[]
  design_speed: number
  design_load: number
  road_width: number
  design_standard_id?: number
}

const RoadDesign: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [roadList, setRoadList] = useState<RoadData[]>([])
  const [loading, setLoading] = useState(false)
  const [designModalVisible, setDesignModalVisible] = useState(false)
  const [profileModalVisible, setProfileModalVisible] = useState(false)
  const [selectedRoad, setSelectedRoad] = useState<RoadData | null>(null)
  const [form] = Form.useForm()

  // 获取道路列表
  const fetchRoadList = async () => {
    if (!projectId) return

    setLoading(true)
    try {
      const response = await axios.get(`/api/v1/roads/${projectId}/roads`)
      setRoadList(response.data)
    } catch (error) {
      message.error('获取道路列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchRoadList()
  }, [projectId])

  // 设计道路
  const handleDesignRoad = async () => {
    try {
      const values = await form.validateFields()

      const designParams: RoadDesignParams = {
        name: values.name,
        description: values.description,
        road_type: values.road_type,
        start_point: {
          x: values.start_x,
          y: values.start_y,
          z: values.start_z || 0
        },
        end_point: {
          x: values.end_x,
          y: values.end_y,
          z: values.end_z || 0
        },
        design_speed: values.design_speed,
        design_load: values.design_load,
        road_width: values.road_width
      }

      await axios.post(`/api/v1/roads/${projectId}/design`, designParams)
      message.success('道路设计成功')
      setDesignModalVisible(false)
      form.resetFields()
      fetchRoadList()
    } catch (error) {
      message.error('道路设计失败')
    }
  }

  // 删除道路
  const handleDeleteRoad = async (roadId: number) => {
    try {
      await axios.delete(`/api/v1/roads/${projectId}/roads/${roadId}`)
      message.success('道路删除成功')
      fetchRoadList()
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 查看道路剖面
  const handleViewProfile = async (road: RoadData) => {
    setSelectedRoad(road)
    setProfileModalVisible(true)
  }

  // 优化道路
  const handleOptimizeRoad = async (roadId: number) => {
    try {
      const optimizationParams = {
        optimization_type: "multi_objective",
        weight_cost: 0.4,
        weight_safety: 0.3,
        weight_environment: 0.3
      }

      await axios.post(`/api/v1/roads/${projectId}/roads/${roadId}/optimize`, optimizationParams)
      message.success('道路优化完成')
      fetchRoadList()
    } catch (error) {
      message.error('道路优化失败')
    }
  }

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      draft: { color: 'default', text: '草图' },
      designed: { color: 'blue', text: '已设计' },
      optimized: { color: 'green', text: '已优化' },
      approved: { color: 'purple', text: '已审批' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 道路类型映射
  const getRoadTypeText = (type: string) => {
    const typeMap = {
      haul_road: '运输道路',
      access_road: '进场道路',
      ramp: '坡道',
      service_road: '服务道路'
    }
    return typeMap[type as keyof typeof typeMap] || type
  }

  // 表格列定义
  const columns = [
    {
      title: '道路名称',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: RoadData) => (
        <Space>
          <CarOutlined />
          <span>{text}</span>
          <Tag>{getRoadTypeText(record.road_type)}</Tag>
        </Space>
      )
    },
    {
      title: '设计参数',
      key: 'design_params',
      render: (record: RoadData) => (
        <Space direction="vertical" size="small">
          <span>速度: {record.design_speed}km/h</span>
          <span>宽度: {record.road_width}m</span>
          <span>载重: {record.design_load}t</span>
        </Space>
      )
    },
    {
      title: '几何参数',
      key: 'geometry',
      render: (record: RoadData) => (
        <Space direction="vertical" size="small">
          <span>长度: {record.total_length.toFixed(1)}m</span>
          {record.max_gradient && <span>最大坡度: {record.max_gradient.toFixed(1)}%</span>}
          {record.min_turning_radius && <span>最小半径: {record.min_turning_radius.toFixed(1)}m</span>}
        </Space>
      )
    },
    {
      title: '土方量',
      key: 'earthwork',
      render: (record: RoadData) => (
        <Space direction="vertical" size="small">
          {record.earthwork_cut && <span>挖方: {record.earthwork_cut.toFixed(0)}m³</span>}
          {record.earthwork_fill && <span>填方: {record.earthwork_fill.toFixed(0)}m³</span>}
        </Space>
      )
    },
    {
      title: '状态',
      dataIndex: 'design_status',
      key: 'design_status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: RoadData) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewProfile(record)}
            />
          </Tooltip>

          <Tooltip title="查看剖面">
            <Button
              type="text"
              icon={<LineChartOutlined />}
              size="small"
              onClick={() => handleViewProfile(record)}
            />
          </Tooltip>

          <Tooltip title="优化设计">
            <Button
              type="text"
              icon={<ThunderboltOutlined />}
              size="small"
              onClick={() => handleOptimizeRoad(record.id)}
            />
          </Tooltip>

          <Popconfirm
            title="确定要删除这条道路吗？"
            onConfirm={() => handleDeleteRoad(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 统计信息
  const getStatistics = () => {
    const total = roadList.length
    const designed = roadList.filter(r => r.design_status === 'designed').length
    const optimized = roadList.filter(r => r.design_status === 'optimized').length
    const totalLength = roadList.reduce((sum, r) => sum + r.total_length, 0)

    return { total, designed, optimized, totalLength }
  }

  const stats = getStatistics()

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          道路设计
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          露天矿山道路设计与优化，支持多种道路类型和设计标准
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="道路总数"
              value={stats.total}
              prefix={<CarOutlined />}
              suffix="条"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已设计"
              value={stats.designed}
              prefix={<CheckCircleOutlined />}
              suffix="条"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已优化"
              value={stats.optimized}
              prefix={<ThunderboltOutlined />}
              suffix="条"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总长度"
              value={stats.totalLength.toFixed(0)}
              prefix={<BarChartOutlined />}
              suffix="m"
            />
          </Card>
        </Col>
      </Row>

      {/* 道路列表 */}
      <Card
        title="道路列表"
        extra={
          <Button
            type="primary"
            icon={<PlusOutlined />}
            onClick={() => setDesignModalVisible(true)}
          >
            设计道路
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={roadList}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 设计道路模态框 */}
      <Modal
        title="设计道路"
        open={designModalVisible}
        onOk={handleDesignRoad}
        onCancel={() => {
          setDesignModalVisible(false)
          form.resetFields()
        }}
        width={800}
        okText="开始设计"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="name"
                label="道路名称"
                rules={[{ required: true, message: '请输入道路名称' }]}
              >
                <Input placeholder="例如: 主运输道路1号" />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="road_type"
                label="道路类型"
                initialValue="haul_road"
                rules={[{ required: true, message: '请选择道路类型' }]}
              >
                <Select>
                  <Select.Option value="haul_road">运输道路</Select.Option>
                  <Select.Option value="access_road">进场道路</Select.Option>
                  <Select.Option value="ramp">坡道</Select.Option>
                  <Select.Option value="service_road">服务道路</Select.Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>

          <Form.Item
            name="description"
            label="道路描述"
          >
            <Input.TextArea rows={2} placeholder="描述道路的用途和特点" />
          </Form.Item>

          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="design_speed"
                label="设计速度 (km/h)"
                initialValue={30}
                rules={[{ required: true, message: '请输入设计速度' }]}
              >
                <InputNumber min={10} max={80} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="design_load"
                label="设计载重 (吨)"
                initialValue={100}
                rules={[{ required: true, message: '请输入设计载重' }]}
              >
                <InputNumber min={10} max={500} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="road_width"
                label="道路宽度 (米)"
                initialValue={6}
                rules={[{ required: true, message: '请输入道路宽度' }]}
              >
                <InputNumber min={3} max={20} step={0.5} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ marginBottom: '16px', fontWeight: 'bold' }}>起点坐标</div>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="start_x"
                label="X坐标"
                rules={[{ required: true, message: '请输入X坐标' }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 100000" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="start_y"
                label="Y坐标"
                rules={[{ required: true, message: '请输入Y坐标' }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 200000" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="start_z"
                label="Z坐标(高程)"
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 100" />
              </Form.Item>
            </Col>
          </Row>

          <div style={{ marginBottom: '16px', fontWeight: 'bold' }}>终点坐标</div>
          <Row gutter={16}>
            <Col span={8}>
              <Form.Item
                name="end_x"
                label="X坐标"
                rules={[{ required: true, message: '请输入X坐标' }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 101000" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="end_y"
                label="Y坐标"
                rules={[{ required: true, message: '请输入Y坐标' }]}
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 201000" />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                name="end_z"
                label="Z坐标(高程)"
              >
                <InputNumber style={{ width: '100%' }} placeholder="例如: 120" />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>

      {/* 道路剖面模态框 */}
      <Modal
        title={`道路剖面 - ${selectedRoad?.name}`}
        open={profileModalVisible}
        onCancel={() => setProfileModalVisible(false)}
        footer={null}
        width={1000}
      >
        {selectedRoad && (
          <div>
            <Descriptions bordered size="small" style={{ marginBottom: '16px' }}>
              <Descriptions.Item label="道路长度">{selectedRoad.total_length.toFixed(1)}m</Descriptions.Item>
              <Descriptions.Item label="设计速度">{selectedRoad.design_speed}km/h</Descriptions.Item>
              <Descriptions.Item label="道路宽度">{selectedRoad.road_width}m</Descriptions.Item>
              <Descriptions.Item label="最大坡度">
                {selectedRoad.max_gradient ? `${selectedRoad.max_gradient.toFixed(1)}%` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="最小半径">
                {selectedRoad.min_turning_radius ? `${selectedRoad.min_turning_radius.toFixed(1)}m` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="设计状态">{getStatusTag(selectedRoad.design_status)}</Descriptions.Item>
            </Descriptions>

            <div style={{
              height: '300px',
              background: '#f5f5f5',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              border: '1px dashed #d9d9d9',
              borderRadius: '6px'
            }}>
              <div style={{ textAlign: 'center', color: '#999' }}>
                <LineChartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                <div>道路纵断面图</div>
                <div style={{ fontSize: '12px' }}>功能开发中...</div>
              </div>
            </div>
          </div>
        )}
      </Modal>
    </div>
  )
}

export default RoadDesign
