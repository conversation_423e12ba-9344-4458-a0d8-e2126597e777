"""
API v1 路由汇总
"""
from fastapi import APIRouter

from app.api.api_v1.endpoints import (
    auth, users, projects, terrain, roads, 
    design_standards, conflict_detection, 
    safety_analysis, optimization, autocad, 
    health
)

# 创建API路由器
api_router = APIRouter()

# 包含各个模块的路由
api_router.include_router(auth.router, prefix="/auth", tags=["认证"])
api_router.include_router(users.router, prefix="/users", tags=["用户管理"])
api_router.include_router(projects.router, prefix="/projects", tags=["项目管理"])
api_router.include_router(terrain.router, prefix="/terrain", tags=["地形数据"])
api_router.include_router(roads.router, prefix="/roads", tags=["道路设计"])
api_router.include_router(design_standards.router, prefix="/standards", tags=["设计标准"])
api_router.include_router(conflict_detection.router, prefix="/conflicts", tags=["冲突检测"])
api_router.include_router(safety_analysis.router, prefix="/safety", tags=["安全分析"])
api_router.include_router(optimization.router, prefix="/optimization", tags=["路线优化"])
api_router.include_router(autocad.router, prefix="/autocad", tags=["AutoCAD集成"])
api_router.include_router(health.router, prefix="/health", tags=["系统监控"])
