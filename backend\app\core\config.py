"""
应用配置模块
"""
import os
from typing import Any, Dict, List, Optional, Union
from pydantic import AnyHttpUrl, BaseSettings, PostgresDsn, validator


class Settings(BaseSettings):
    """应用设置"""
    
    # 基础配置
    PROJECT_NAME: str = "露天矿山道路设计软件"
    VERSION: str = "1.0.0"
    API_V1_STR: str = "/api/v1"
    SECRET_KEY: str = "your-secret-key-here"
    
    # 服务器配置
    SERVER_NAME: str = "localhost"
    SERVER_HOST: AnyHttpUrl = "http://localhost"
    BACKEND_CORS_ORIGINS: List[AnyHttpUrl] = [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://localhost:5173",
    ]
    
    @validator("BACKEND_CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str) and not v.startswith("["):
            return [i.strip() for i in v.split(",")]
        elif isinstance(v, (list, str)):
            return v
        raise ValueError(v)
    
    # 数据库配置
    POSTGRES_SERVER: str = "localhost"
    POSTGRES_USER: str = "postgres"
    POSTGRES_PASSWORD: str = "password"
    POSTGRES_DB: str = "openpit_road_design"
    POSTGRES_PORT: str = "5432"
    SQLALCHEMY_DATABASE_URI: Optional[PostgresDsn] = None
    
    @validator("SQLALCHEMY_DATABASE_URI", pre=True)
    def assemble_db_connection(cls, v: Optional[str], values: Dict[str, Any]) -> Any:
        if isinstance(v, str):
            return v
        return PostgresDsn.build(
            scheme="postgresql",
            user=values.get("POSTGRES_USER"),
            password=values.get("POSTGRES_PASSWORD"),
            host=values.get("POSTGRES_SERVER"),
            port=values.get("POSTGRES_PORT"),
            path=f"/{values.get('POSTGRES_DB') or ''}",
        )
    
    # Redis配置
    REDIS_HOST: str = "localhost"
    REDIS_PORT: int = 6379
    REDIS_DB: int = 0
    REDIS_PASSWORD: Optional[str] = None
    
    # Celery配置
    CELERY_BROKER_URL: str = "redis://localhost:6379/0"
    CELERY_RESULT_BACKEND: str = "redis://localhost:6379/0"
    
    # JWT配置
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8天
    REFRESH_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 30  # 30天
    ALGORITHM: str = "HS256"
    
    # 文件上传配置
    MAX_FILE_SIZE: int = 100 * 1024 * 1024  # 100MB
    UPLOAD_DIR: str = "uploads"
    ALLOWED_EXTENSIONS: List[str] = [
        ".dwg", ".dxf", ".shp", ".tif", ".tiff", ".las", ".laz",
        ".xyz", ".csv", ".json", ".geojson", ".kml", ".kmz"
    ]
    
    # 地形数据配置
    TERRAIN_DATA_DIR: str = "data/terrain"
    GEOLOGY_DATA_DIR: str = "data/geology"
    STANDARDS_DATA_DIR: str = "data/standards"
    
    # 道路设计标准
    MIN_ROAD_WIDTH: float = 6.0  # 最小道路宽度(米)
    MAX_GRADIENT: float = 8.0    # 最大坡度(%)
    MIN_TURNING_RADIUS: float = 15.0  # 最小转弯半径(米)
    MIN_SIGHT_DISTANCE: float = 50.0  # 最小视距(米)
    
    # 安全检测参数
    SAFETY_BUFFER_DISTANCE: float = 5.0  # 安全缓冲距离(米)
    MAX_SLOPE_ANGLE: float = 45.0  # 最大边坡角度(度)
    MIN_DRAINAGE_SLOPE: float = 0.5  # 最小排水坡度(%)
    
    # 优化算法参数
    OPTIMIZATION_ITERATIONS: int = 1000
    POPULATION_SIZE: int = 100
    MUTATION_RATE: float = 0.1
    CROSSOVER_RATE: float = 0.8
    
    # 日志配置
    LOG_LEVEL: str = "INFO"
    LOG_FILE: str = "logs/app.log"
    
    # 缓存配置
    CACHE_TTL: int = 3600  # 缓存过期时间(秒)
    
    # 邮件配置
    SMTP_TLS: bool = True
    SMTP_PORT: Optional[int] = None
    SMTP_HOST: Optional[str] = None
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: Optional[str] = None
    
    # 监控配置
    ENABLE_METRICS: bool = True
    METRICS_PORT: int = 9090
    
    # 开发模式
    DEBUG: bool = False
    TESTING: bool = False
    
    # 第三方服务配置
    CESIUM_ION_ACCESS_TOKEN: Optional[str] = None
    MAPBOX_ACCESS_TOKEN: Optional[str] = None
    
    class Config:
        case_sensitive = True
        env_file = ".env"


# 创建全局设置实例
settings = Settings()


# 数据库URL构建函数
def get_database_url() -> str:
    """获取数据库连接URL"""
    return str(settings.SQLALCHEMY_DATABASE_URI)


# Redis URL构建函数
def get_redis_url() -> str:
    """获取Redis连接URL"""
    if settings.REDIS_PASSWORD:
        return f"redis://:{settings.REDIS_PASSWORD}@{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"
    return f"redis://{settings.REDIS_HOST}:{settings.REDIS_PORT}/{settings.REDIS_DB}"


# 环境检查函数
def is_development() -> bool:
    """检查是否为开发环境"""
    return settings.DEBUG


def is_production() -> bool:
    """检查是否为生产环境"""
    return not settings.DEBUG and not settings.TESTING


def is_testing() -> bool:
    """检查是否为测试环境"""
    return settings.TESTING
