"""
运输路线优化服务
"""
import os
import json
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import asyncio

from app.models.route_optimization import RouteOptimization, OptimizedRoute as DBOptimizedRoute
from app.models.road import Road
from app.models.project import Project
from app.core.config import settings
from algorithms.route_optimization.route_optimizer import (
    RouteOptimizer, RouteNode, RouteEdge, Vehicle, TransportTask, 
    OptimizationObjective, VehicleType
)
from algorithms.route_optimization.path_finder import PathFinder, PathFindingAlgorithm
import logging

logger = logging.getLogger(__name__)


class RouteService:
    """运输路线优化服务类"""
    
    def __init__(self):
        self.route_optimizer = RouteOptimizer()
        self.path_finder = PathFinder()
    
    async def optimize_transport_routes(self, 
                                      db: Session,
                                      project_id: int,
                                      optimization_params: Dict) -> RouteOptimization:
        """优化运输路线"""
        try:
            logger.info(f"开始运输路线优化: 项目 {project_id}")
            
            # 构建道路网络
            await self._build_road_network(db, project_id)
            
            # 解析优化参数
            tasks = self._parse_transport_tasks(optimization_params.get('tasks', []))
            vehicles = self._parse_vehicles(optimization_params.get('vehicles', []))
            objectives = self._parse_objectives(optimization_params.get('objectives', []))
            
            # 执行路线优化
            optimized_routes = await asyncio.get_event_loop().run_in_executor(
                None,
                self.route_optimizer.optimize_routes,
                tasks,
                vehicles,
                objectives
            )
            
            # 生成优化报告
            optimization_report = self.route_optimizer.generate_optimization_report(
                optimized_routes, tasks
            )
            
            # 保存优化结果到数据库
            route_optimization = await self._save_optimization_result(
                db, project_id, optimized_routes, optimization_report, optimization_params
            )
            
            logger.info(f"运输路线优化完成，生成 {len(optimized_routes)} 条优化路线")
            return route_optimization
            
        except Exception as e:
            logger.error(f"运输路线优化失败: {str(e)}")
            raise
    
    async def find_optimal_path(self, 
                              db: Session,
                              project_id: int,
                              start_point: Dict,
                              end_point: Dict,
                              algorithm: str = 'a_star') -> Dict:
        """查找最优路径"""
        try:
            # 构建道路网络
            await self._build_road_network(db, project_id)
            
            # 添加起点和终点到网络
            start_node_id = f"start_{start_point['x']}_{start_point['y']}"
            end_node_id = f"end_{end_point['x']}_{end_point['y']}"
            
            self.path_finder.add_node(
                start_node_id, 
                start_point['x'], 
                start_point['y'], 
                start_point.get('z', 0)
            )
            self.path_finder.add_node(
                end_node_id, 
                end_point['x'], 
                end_point['y'], 
                end_point.get('z', 0)
            )
            
            # 连接到最近的道路节点
            await self._connect_to_nearest_nodes(start_node_id, end_node_id)
            
            # 选择算法
            algorithm_enum = {
                'dijkstra': PathFindingAlgorithm.DIJKSTRA,
                'a_star': PathFindingAlgorithm.A_STAR,
                'floyd_warshall': PathFindingAlgorithm.FLOYD_WARSHALL
            }.get(algorithm, PathFindingAlgorithm.A_STAR)
            
            # 查找路径
            path_result = self.path_finder.find_shortest_path(
                start_node_id, end_node_id, algorithm_enum
            )
            
            if not path_result:
                return {
                    'success': False,
                    'message': '未找到可行路径'
                }
            
            # 分析路径质量
            path_quality = self.path_finder.analyze_path_quality(path_result)
            
            return {
                'success': True,
                'path': path_result.path,
                'total_distance': path_result.total_distance,
                'total_cost': path_result.total_cost,
                'algorithm_used': path_result.algorithm_used,
                'computation_time': path_result.computation_time,
                'nodes_explored': path_result.nodes_explored,
                'path_quality': path_quality
            }
            
        except Exception as e:
            logger.error(f"路径查找失败: {str(e)}")
            raise
    
    async def analyze_route_network(self, db: Session, project_id: int) -> Dict:
        """分析路线网络"""
        try:
            # 构建道路网络
            await self._build_road_network(db, project_id)
            
            # 获取网络统计
            network_stats = self.path_finder.get_network_statistics()
            
            # 分析连通性
            connectivity_analysis = await self._analyze_connectivity(db, project_id)
            
            # 分析瓶颈路段
            bottleneck_analysis = await self._analyze_bottlenecks(db, project_id)
            
            return {
                'project_id': project_id,
                'network_statistics': network_stats,
                'connectivity_analysis': connectivity_analysis,
                'bottleneck_analysis': bottleneck_analysis,
                'analysis_time': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"路线网络分析失败: {str(e)}")
            raise
    
    async def _build_road_network(self, db: Session, project_id: int) -> None:
        """构建道路网络"""
        try:
            from app.models.road import RoadSegment, RoadPoint
            
            # 获取项目道路
            roads = db.query(Road).filter(Road.project_id == project_id).all()
            
            nodes = []
            edges = []
            
            for road in roads:
                # 获取道路段
                segments = db.query(RoadSegment).filter(RoadSegment.road_id == road.id).all()
                
                for segment in segments:
                    # 创建起点和终点节点
                    start_node_id = f"road_{road.id}_seg_{segment.id}_start"
                    end_node_id = f"road_{road.id}_seg_{segment.id}_end"
                    
                    if segment.start_point:
                        start_node = RouteNode(
                            node_id=start_node_id,
                            x=segment.start_point[0],
                            y=segment.start_point[1],
                            z=segment.start_point[2] if len(segment.start_point) > 2 else 0,
                            node_type='waypoint'
                        )
                        nodes.append(start_node)
                        
                        # 添加到路径查找器
                        self.path_finder.add_node(
                            start_node_id,
                            segment.start_point[0],
                            segment.start_point[1],
                            segment.start_point[2] if len(segment.start_point) > 2 else 0
                        )
                    
                    if segment.end_point:
                        end_node = RouteNode(
                            node_id=end_node_id,
                            x=segment.end_point[0],
                            y=segment.end_point[1],
                            z=segment.end_point[2] if len(segment.end_point) > 2 else 0,
                            node_type='waypoint'
                        )
                        nodes.append(end_node)
                        
                        # 添加到路径查找器
                        self.path_finder.add_node(
                            end_node_id,
                            segment.end_point[0],
                            segment.end_point[1],
                            segment.end_point[2] if len(segment.end_point) > 2 else 0
                        )
                    
                    # 创建边
                    if segment.start_point and segment.end_point:
                        distance = segment.length or self._calculate_distance(
                            segment.start_point, segment.end_point
                        )
                        
                        # 计算行驶时间
                        speed = min(road.design_speed, 30)  # 限制最大速度
                        travel_time = distance / speed * 3.6  # 转换为小时
                        
                        # 计算成本（基于距离和道路条件）
                        base_cost = distance * 1.0  # 基础成本
                        gradient_factor = 1.0 + abs(segment.gradient or 0) / 100
                        cost = base_cost * gradient_factor
                        
                        edge = RouteEdge(
                            edge_id=f"road_{road.id}_seg_{segment.id}",
                            from_node=start_node_id,
                            to_node=end_node_id,
                            distance=distance,
                            travel_time=travel_time,
                            cost=cost,
                            gradient=segment.gradient or 0,
                            road_condition='good',  # 默认道路条件
                            width=road.road_width,
                            speed_limit=road.design_speed
                        )
                        edges.append(edge)
                        
                        # 添加到路径查找器
                        self.path_finder.add_edge(
                            start_node_id, end_node_id, distance, cost, True
                        )
            
            # 构建路线优化器网络
            self.route_optimizer.build_road_network(nodes, edges)
            
            logger.info(f"道路网络构建完成: {len(nodes)}个节点, {len(edges)}条边")
            
        except Exception as e:
            logger.error(f"构建道路网络失败: {str(e)}")
            raise
    
    def _calculate_distance(self, point1: List[float], point2: List[float]) -> float:
        """计算两点间距离"""
        import math
        
        dx = point2[0] - point1[0]
        dy = point2[1] - point1[1]
        dz = (point2[2] - point1[2]) if len(point1) > 2 and len(point2) > 2 else 0
        
        return math.sqrt(dx*dx + dy*dy + dz*dz)
    
    async def _connect_to_nearest_nodes(self, start_node_id: str, end_node_id: str) -> None:
        """连接到最近的道路节点"""
        try:
            # 简化实现：连接到所有节点（实际应该只连接最近的几个节点）
            start_node = None
            end_node = None
            
            # 查找起点和终点
            for node_id, node in self.path_finder.nodes.items():
                if node_id == start_node_id:
                    start_node = node
                elif node_id == end_node_id:
                    end_node = node
            
            if not start_node or not end_node:
                return
            
            # 连接到最近的道路节点
            min_distance = 1000  # 最大连接距离
            
            for node_id, node in self.path_finder.nodes.items():
                if node_id in [start_node_id, end_node_id]:
                    continue
                
                # 计算到起点的距离
                start_dist = math.sqrt(
                    (node.x - start_node.x)**2 + 
                    (node.y - start_node.y)**2
                )
                
                if start_dist < min_distance:
                    self.path_finder.add_edge(start_node_id, node_id, start_dist, start_dist, True)
                
                # 计算到终点的距离
                end_dist = math.sqrt(
                    (node.x - end_node.x)**2 + 
                    (node.y - end_node.y)**2
                )
                
                if end_dist < min_distance:
                    self.path_finder.add_edge(end_node_id, node_id, end_dist, end_dist, True)
            
        except Exception as e:
            logger.error(f"连接最近节点失败: {str(e)}")
    
    def _parse_transport_tasks(self, tasks_data: List[Dict]) -> List[TransportTask]:
        """解析运输任务"""
        tasks = []
        
        for task_data in tasks_data:
            task = TransportTask(
                task_id=task_data.get('task_id', f"task_{len(tasks)}"),
                origin=task_data.get('origin'),
                destination=task_data.get('destination'),
                cargo_volume=task_data.get('cargo_volume', 0),
                cargo_weight=task_data.get('cargo_weight', 0),
                priority=task_data.get('priority', 3),
                time_window=task_data.get('time_window'),
                vehicle_requirements=task_data.get('vehicle_requirements')
            )
            tasks.append(task)
        
        return tasks
    
    def _parse_vehicles(self, vehicles_data: List[Dict]) -> List[Vehicle]:
        """解析车辆信息"""
        vehicles = []
        
        for vehicle_data in vehicles_data:
            vehicle_type_str = vehicle_data.get('vehicle_type', 'dump_truck')
            vehicle_type = VehicleType(vehicle_type_str)
            
            vehicle = Vehicle(
                vehicle_id=vehicle_data.get('vehicle_id', f"vehicle_{len(vehicles)}"),
                vehicle_type=vehicle_type,
                capacity=vehicle_data.get('capacity', 50),
                max_speed=vehicle_data.get('max_speed', 30),
                fuel_consumption=vehicle_data.get('fuel_consumption', 25),
                operating_cost=vehicle_data.get('operating_cost', 200),
                weight=vehicle_data.get('weight', 15000),
                dimensions=tuple(vehicle_data.get('dimensions', [8, 2.5, 3]))
            )
            vehicles.append(vehicle)
        
        return vehicles
    
    def _parse_objectives(self, objectives_data: List[str]) -> List[OptimizationObjective]:
        """解析优化目标"""
        objectives = []
        
        for obj_str in objectives_data:
            try:
                objective = OptimizationObjective(obj_str)
                objectives.append(objective)
            except ValueError:
                logger.warning(f"未知的优化目标: {obj_str}")
        
        if not objectives:
            objectives = [OptimizationObjective.MINIMIZE_DISTANCE, OptimizationObjective.MINIMIZE_TIME]
        
        return objectives
    
    async def _save_optimization_result(self, 
                                      db: Session,
                                      project_id: int,
                                      optimized_routes: List,
                                      optimization_report: Dict,
                                      optimization_params: Dict) -> RouteOptimization:
        """保存优化结果到数据库"""
        try:
            # 创建路线优化记录
            route_optimization = RouteOptimization(
                project_id=project_id,
                optimization_type='transport_routes',
                total_routes=len(optimized_routes),
                total_distance=optimization_report.get('performance_metrics', {}).get('total_distance', 0),
                total_time=optimization_report.get('performance_metrics', {}).get('total_time', 0),
                total_cost=optimization_report.get('performance_metrics', {}).get('total_cost', 0),
                optimization_score=optimization_report.get('performance_metrics', {}).get('average_efficiency', 0),
                optimization_params=optimization_params,
                optimization_report=optimization_report,
                optimized_at=datetime.utcnow()
            )
            
            db.add(route_optimization)
            db.commit()
            db.refresh(route_optimization)
            
            # 保存优化路线
            for route in optimized_routes:
                db_route = DBOptimizedRoute(
                    optimization_id=route_optimization.id,
                    route_id=route.route_id,
                    task_id=route.task_id,
                    vehicle_id=route.vehicle_id,
                    path_nodes=route.path,
                    total_distance=route.total_distance,
                    total_time=route.total_time,
                    total_cost=route.total_cost,
                    fuel_consumption=route.fuel_consumption,
                    safety_score=route.safety_score,
                    route_efficiency=route.route_efficiency
                )
                
                db.add(db_route)
            
            db.commit()
            
            return route_optimization
            
        except Exception as e:
            logger.error(f"保存优化结果失败: {str(e)}")
            raise
    
    async def _analyze_connectivity(self, db: Session, project_id: int) -> Dict:
        """分析连通性"""
        try:
            # 获取连通分量
            components = self.path_finder._find_connected_components()
            
            return {
                'connected_components': len(components),
                'largest_component_size': max(len(comp) for comp in components) if components else 0,
                'connectivity_ratio': len(components[0]) / len(self.path_finder.nodes) if components else 0
            }
            
        except Exception as e:
            logger.error(f"连通性分析失败: {str(e)}")
            return {}
    
    async def _analyze_bottlenecks(self, db: Session, project_id: int) -> Dict:
        """分析瓶颈路段"""
        try:
            # 简化的瓶颈分析
            bottlenecks = []
            
            # 查找度数为1的节点（死胡同）
            for node_id, neighbors in self.path_finder.adjacency_list.items():
                if len(neighbors) == 1:
                    bottlenecks.append({
                        'node_id': node_id,
                        'type': 'dead_end',
                        'severity': 'high'
                    })
            
            return {
                'bottleneck_count': len(bottlenecks),
                'bottlenecks': bottlenecks[:10]  # 只返回前10个
            }
            
        except Exception as e:
            logger.error(f"瓶颈分析失败: {str(e)}")
            return {}
    
    def get_project_optimizations(self, 
                                db: Session,
                                project_id: int,
                                skip: int = 0,
                                limit: int = 100) -> List[RouteOptimization]:
        """获取项目路线优化列表"""
        return db.query(RouteOptimization)\
                 .filter(RouteOptimization.project_id == project_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_optimization_detail(self, db: Session, optimization_id: int) -> Optional[RouteOptimization]:
        """获取路线优化详情"""
        return db.query(RouteOptimization).filter(RouteOptimization.id == optimization_id).first()
    
    def get_optimization_statistics(self, db: Session, project_id: int) -> Dict:
        """获取路线优化统计信息"""
        try:
            optimizations = db.query(RouteOptimization).filter(
                RouteOptimization.project_id == project_id
            ).all()
            
            if not optimizations:
                return {
                    'total_optimizations': 0,
                    'total_routes': 0,
                    'average_efficiency': 0,
                    'total_cost_savings': 0
                }
            
            total_optimizations = len(optimizations)
            total_routes = sum(opt.total_routes for opt in optimizations)
            avg_efficiency = sum(opt.optimization_score for opt in optimizations) / total_optimizations
            
            return {
                'total_optimizations': total_optimizations,
                'total_routes': total_routes,
                'average_efficiency': avg_efficiency,
                'latest_optimization_date': optimizations[-1].optimized_at.isoformat() if optimizations else None
            }
            
        except Exception as e:
            logger.error(f"获取路线优化统计失败: {str(e)}")
            return {}
