"""
道路设计API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from pydantic import BaseModel

from app.core.database import get_db
from app.services.road_service import RoadService
from app.models.road import Road

router = APIRouter()
road_service = RoadService()


class Point3DModel(BaseModel):
    """三维点模型"""
    x: float
    y: float
    z: Optional[float] = 0.0


class RoadDesignParams(BaseModel):
    """道路设计参数"""
    name: str
    description: Optional[str] = None
    road_type: str = "haul_road"
    start_point: Point3DModel
    end_point: Point3DModel
    control_points: Optional[List[Point3DModel]] = None
    design_speed: float = 30.0
    design_load: float = 100.0
    road_width: float = 6.0
    design_standard_id: Optional[int] = None


class RoadResponse(BaseModel):
    """道路响应模型"""
    id: int
    name: str
    description: Optional[str]
    road_type: str
    design_speed: float
    design_load: float
    road_width: float
    total_length: float
    max_gradient: Optional[float]
    min_turning_radius: Optional[float]
    design_status: str
    earthwork_cut: Optional[float]
    earthwork_fill: Optional[float]
    created_at: str

    class Config:
        from_attributes = True


class OptimizationParams(BaseModel):
    """优化参数"""
    optimization_type: str = "multi_objective"
    weight_cost: float = 0.4
    weight_safety: float = 0.3
    weight_environment: float = 0.3
    max_iterations: int = 100


@router.post("/{project_id}/design", response_model=RoadResponse)
async def design_road(
    project_id: int,
    design_params: RoadDesignParams,
    db: Session = Depends(get_db)
):
    """设计道路"""
    try:
        road = await road_service.design_road(
            db=db,
            project_id=project_id,
            design_params=design_params.dict()
        )

        return RoadResponse(
            id=road.id,
            name=road.name,
            description=road.description,
            road_type=road.road_type,
            design_speed=road.design_speed,
            design_load=road.design_load,
            road_width=road.road_width,
            total_length=road.total_length,
            max_gradient=road.max_gradient,
            min_turning_radius=road.min_turning_radius,
            design_status=road.design_status,
            earthwork_cut=road.earthwork_cut,
            earthwork_fill=road.earthwork_fill,
            created_at=road.created_at.isoformat() if road.created_at else ""
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"道路设计失败: {str(e)}")


@router.get("/{project_id}/roads", response_model=List[RoadResponse])
async def get_roads(
    project_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取项目道路列表"""
    try:
        roads = road_service.get_road_list(
            db=db,
            project_id=project_id,
            skip=skip,
            limit=limit
        )

        return [
            RoadResponse(
                id=road.id,
                name=road.name,
                description=road.description,
                road_type=road.road_type,
                design_speed=road.design_speed,
                design_load=road.design_load,
                road_width=road.road_width,
                total_length=road.total_length,
                max_gradient=road.max_gradient,
                min_turning_radius=road.min_turning_radius,
                design_status=road.design_status,
                earthwork_cut=road.earthwork_cut,
                earthwork_fill=road.earthwork_fill,
                created_at=road.created_at.isoformat() if road.created_at else ""
            )
            for road in roads
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取道路列表失败: {str(e)}")


@router.get("/{project_id}/roads/{road_id}", response_model=RoadResponse)
async def get_road_detail(
    project_id: int,
    road_id: int,
    db: Session = Depends(get_db)
):
    """获取道路详情"""
    try:
        road = road_service.get_road_detail(db=db, road_id=road_id)

        if not road:
            raise HTTPException(status_code=404, detail="道路不存在")

        if road.project_id != project_id:
            raise HTTPException(status_code=403, detail="无权访问该道路")

        return RoadResponse(
            id=road.id,
            name=road.name,
            description=road.description,
            road_type=road.road_type,
            design_speed=road.design_speed,
            design_load=road.design_load,
            road_width=road.road_width,
            total_length=road.total_length,
            max_gradient=road.max_gradient,
            min_turning_radius=road.min_turning_radius,
            design_status=road.design_status,
            earthwork_cut=road.earthwork_cut,
            earthwork_fill=road.earthwork_fill,
            created_at=road.created_at.isoformat() if road.created_at else ""
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取道路详情失败: {str(e)}")


@router.get("/{project_id}/roads/{road_id}/profile")
async def get_road_profile(
    project_id: int,
    road_id: int,
    start_chainage: float = Query(0, ge=0, description="起始里程"),
    end_chainage: Optional[float] = Query(None, description="结束里程"),
    db: Session = Depends(get_db)
):
    """获取道路纵断面"""
    try:
        profile_data = road_service.get_road_profile(
            db=db,
            road_id=road_id,
            start_chainage=start_chainage,
            end_chainage=end_chainage
        )

        if not profile_data:
            raise HTTPException(status_code=404, detail="未找到道路纵断面数据")

        return profile_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取道路纵断面失败: {str(e)}")


@router.get("/{project_id}/roads/{road_id}/cross-section")
async def get_road_cross_section(
    project_id: int,
    road_id: int,
    chainage: float = Query(..., description="里程桩号"),
    db: Session = Depends(get_db)
):
    """获取道路横断面"""
    try:
        cross_section_data = road_service.get_road_cross_section(
            db=db,
            road_id=road_id,
            chainage=chainage
        )

        if not cross_section_data:
            raise HTTPException(status_code=404, detail="未找到道路横断面数据")

        return cross_section_data

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取道路横断面失败: {str(e)}")


@router.post("/{project_id}/roads/{road_id}/optimize")
async def optimize_road_alignment(
    project_id: int,
    road_id: int,
    optimization_params: OptimizationParams,
    db: Session = Depends(get_db)
):
    """优化道路线形"""
    try:
        result = road_service.optimize_road_alignment(
            db=db,
            road_id=road_id,
            optimization_params=optimization_params.dict()
        )

        if result.get('status') == 'error':
            raise HTTPException(status_code=500, detail=result.get('error_message'))

        return result

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"优化道路线形失败: {str(e)}")


@router.delete("/{project_id}/roads/{road_id}")
async def delete_road(
    project_id: int,
    road_id: int,
    db: Session = Depends(get_db)
):
    """删除道路"""
    try:
        success = road_service.delete_road(db=db, road_id=road_id)

        if success:
            return {"message": "道路删除成功", "road_id": road_id}
        else:
            raise HTTPException(status_code=404, detail="道路不存在")

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除道路失败: {str(e)}")
