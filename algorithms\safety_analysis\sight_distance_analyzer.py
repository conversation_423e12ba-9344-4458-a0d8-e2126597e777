"""
视距分析器
"""
import numpy as np
import math
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class SightDistanceResult:
    """视距分析结果"""
    chainage: float
    location: Tuple[float, float, float]
    stopping_sight_distance: float
    actual_sight_distance: float
    meeting_sight_distance: float
    overtaking_sight_distance: float
    is_adequate: bool
    deficiency: float
    recommendations: List[str]


@dataclass
class VerticalCurveData:
    """竖曲线数据"""
    start_chainage: float
    end_chainage: float
    radius: float
    grade_in: float
    grade_out: float
    curve_type: str  # 'crest' or 'sag'


class SightDistanceAnalyzer:
    """视距分析器"""
    
    def __init__(self):
        self.driver_eye_height = 1.2  # 驾驶员眼高(米)
        self.object_height = 0.15     # 障碍物高度(米)
        self.headlight_height = 0.6   # 前照灯高度(米)
        self.headlight_angle = 1.0    # 前照灯光束角(度)
        
        # 反应时间和摩擦系数
        self.reaction_time = 2.5      # 反应时间(秒)
        self.friction_coefficient = 0.35  # 纵向摩擦系数
        
    def analyze_sight_distance(self, 
                             road_points: List[Tuple[float, float, float]],
                             chainages: List[float],
                             gradients: List[float],
                             design_speed: float,
                             vertical_curves: List[VerticalCurveData] = None) -> List[SightDistanceResult]:
        """分析道路视距"""
        try:
            logger.info("开始视距分析")
            
            results = []
            
            for i in range(len(road_points)):
                chainage = chainages[i]
                point = road_points[i]
                gradient = gradients[i] if i < len(gradients) else 0
                
                # 计算各种视距需求
                stopping_distance = self._calculate_stopping_sight_distance(design_speed, gradient)
                meeting_distance = self._calculate_meeting_sight_distance(design_speed, gradient)
                overtaking_distance = self._calculate_overtaking_sight_distance(design_speed)
                
                # 计算实际视距
                actual_distance = self._calculate_actual_sight_distance(
                    road_points, chainages, i, vertical_curves
                )
                
                # 判断是否充足
                is_adequate = actual_distance >= stopping_distance
                deficiency = max(0, stopping_distance - actual_distance)
                
                # 生成建议
                recommendations = self._generate_sight_recommendations(
                    actual_distance, stopping_distance, deficiency
                )
                
                result = SightDistanceResult(
                    chainage=chainage,
                    location=point,
                    stopping_sight_distance=stopping_distance,
                    actual_sight_distance=actual_distance,
                    meeting_sight_distance=meeting_distance,
                    overtaking_sight_distance=overtaking_distance,
                    is_adequate=is_adequate,
                    deficiency=deficiency,
                    recommendations=recommendations
                )
                
                results.append(result)
            
            logger.info(f"视距分析完成，分析了 {len(results)} 个点")
            return results
            
        except Exception as e:
            logger.error(f"视距分析失败: {str(e)}")
            raise
    
    def _calculate_stopping_sight_distance(self, design_speed: float, gradient: float) -> float:
        """计算停车视距"""
        try:
            # 停车视距公式: S = V*t + V²/(254(f±i))
            # V: 设计速度(km/h)
            # t: 反应时间(s)
            # f: 纵向摩擦系数
            # i: 纵坡(小数形式，上坡为正，下坡为负)
            
            grade_decimal = gradient / 100.0
            
            # 反应距离
            reaction_distance = design_speed * self.reaction_time / 3.6
            
            # 制动距离
            braking_distance = (design_speed ** 2) / (254 * (self.friction_coefficient + grade_decimal))
            
            total_distance = reaction_distance + braking_distance
            
            # 确保不小于最小值
            return max(total_distance, 30.0)
            
        except Exception as e:
            logger.error(f"计算停车视距失败: {str(e)}")
            return 50.0  # 返回默认值
    
    def _calculate_meeting_sight_distance(self, design_speed: float, gradient: float) -> float:
        """计算会车视距"""
        try:
            # 会车视距通常为停车视距的2倍
            stopping_distance = self._calculate_stopping_sight_distance(design_speed, gradient)
            return 2.0 * stopping_distance
            
        except Exception as e:
            logger.error(f"计算会车视距失败: {str(e)}")
            return 100.0
    
    def _calculate_overtaking_sight_distance(self, design_speed: float) -> float:
        """计算超车视距"""
        try:
            # 超车视距的简化计算
            # S = V * (t1 + t2) + S1
            # 其中 t1 为超车时间，t2 为对向车辆行驶时间，S1 为安全距离
            
            overtaking_time = 10.0  # 超车时间(秒)
            oncoming_time = 6.0     # 对向车辆时间(秒)
            safety_distance = 100.0  # 安全距离(米)
            
            overtaking_distance = (design_speed / 3.6) * (overtaking_time + oncoming_time) + safety_distance
            
            return max(overtaking_distance, 200.0)
            
        except Exception as e:
            logger.error(f"计算超车视距失败: {str(e)}")
            return 300.0
    
    def _calculate_actual_sight_distance(self, 
                                       road_points: List[Tuple[float, float, float]],
                                       chainages: List[float],
                                       current_index: int,
                                       vertical_curves: List[VerticalCurveData] = None) -> float:
        """计算实际视距"""
        try:
            if current_index >= len(road_points) - 1:
                return 100.0  # 默认值
            
            current_point = road_points[current_index]
            current_chainage = chainages[current_index]
            
            # 检查是否在竖曲线上
            curve_data = self._find_vertical_curve(current_chainage, vertical_curves)
            
            if curve_data:
                # 竖曲线视距计算
                return self._calculate_vertical_curve_sight_distance(
                    current_chainage, curve_data
                )
            else:
                # 直线段视距计算
                return self._calculate_straight_sight_distance(
                    road_points, current_index
                )
                
        except Exception as e:
            logger.error(f"计算实际视距失败: {str(e)}")
            return 50.0
    
    def _find_vertical_curve(self, 
                           chainage: float, 
                           vertical_curves: List[VerticalCurveData]) -> Optional[VerticalCurveData]:
        """查找当前位置的竖曲线"""
        if not vertical_curves:
            return None
        
        for curve in vertical_curves:
            if curve.start_chainage <= chainage <= curve.end_chainage:
                return curve
        
        return None
    
    def _calculate_vertical_curve_sight_distance(self, 
                                               chainage: float,
                                               curve_data: VerticalCurveData) -> float:
        """计算竖曲线视距"""
        try:
            if curve_data.curve_type == 'crest':
                # 凸形竖曲线视距
                return self._calculate_crest_curve_sight_distance(curve_data)
            else:
                # 凹形竖曲线视距
                return self._calculate_sag_curve_sight_distance(curve_data)
                
        except Exception as e:
            logger.error(f"计算竖曲线视距失败: {str(e)}")
            return 50.0
    
    def _calculate_crest_curve_sight_distance(self, curve_data: VerticalCurveData) -> float:
        """计算凸形竖曲线视距"""
        try:
            # 凸形竖曲线视距公式
            # S = 2 * sqrt(2 * R * (h1 + h2))
            # 其中 R 为竖曲线半径，h1 为驾驶员眼高，h2 为障碍物高度
            
            h1 = self.driver_eye_height
            h2 = self.object_height
            R = curve_data.radius
            
            sight_distance = 2 * math.sqrt(2 * R * (h1 + h2))
            
            return max(sight_distance, 30.0)
            
        except Exception as e:
            logger.error(f"计算凸形竖曲线视距失败: {str(e)}")
            return 50.0
    
    def _calculate_sag_curve_sight_distance(self, curve_data: VerticalCurveData) -> float:
        """计算凹形竖曲线视距"""
        try:
            # 凹形竖曲线夜间视距公式
            # S = 2 * sqrt(2 * R * h) / sin(α)
            # 其中 h 为前照灯高度，α 为光束角
            
            h = self.headlight_height
            R = curve_data.radius
            alpha_rad = math.radians(self.headlight_angle)
            
            sight_distance = 2 * math.sqrt(2 * R * h) / math.sin(alpha_rad)
            
            return max(sight_distance, 50.0)
            
        except Exception as e:
            logger.error(f"计算凹形竖曲线视距失败: {str(e)}")
            return 60.0
    
    def _calculate_straight_sight_distance(self, 
                                         road_points: List[Tuple[float, float, float]],
                                         current_index: int) -> float:
        """计算直线段视距"""
        try:
            # 在直线段上，视距主要受地形遮挡影响
            # 这里简化处理，假设视距为到下一个显著高程变化点的距离
            
            current_point = road_points[current_index]
            current_elevation = current_point[2] + self.driver_eye_height
            
            max_sight_distance = 0
            
            # 向前搜索，直到视线被遮挡
            for i in range(current_index + 1, min(current_index + 50, len(road_points))):
                next_point = road_points[i]
                
                # 计算视线高程
                distance = math.sqrt(
                    (next_point[0] - current_point[0])**2 + 
                    (next_point[1] - current_point[1])**2
                )
                
                # 简化的视线计算
                required_elevation = current_elevation - distance * 0.01  # 假设1%的下降
                actual_elevation = next_point[2] + self.object_height
                
                if actual_elevation > required_elevation:
                    # 视线被遮挡
                    break
                
                max_sight_distance = distance
            
            return max(max_sight_distance, 40.0)
            
        except Exception as e:
            logger.error(f"计算直线段视距失败: {str(e)}")
            return 60.0
    
    def _generate_sight_recommendations(self, 
                                      actual_distance: float,
                                      required_distance: float,
                                      deficiency: float) -> List[str]:
        """生成视距改善建议"""
        recommendations = []
        
        if deficiency <= 0:
            recommendations.append("视距满足要求")
            return recommendations
        
        if deficiency > required_distance * 0.5:
            recommendations.extend([
                "视距严重不足，建议重新设计线形",
                "考虑降低设计速度",
                "设置强制减速设施"
            ])
        elif deficiency > required_distance * 0.3:
            recommendations.extend([
                "视距不足较严重，建议削减边坡",
                "设置视距不良警示标志",
                "考虑局部线形调整"
            ])
        elif deficiency > required_distance * 0.1:
            recommendations.extend([
                "视距略有不足，建议设置警示标志",
                "加强路面标线",
                "定期清理遮挡物"
            ])
        else:
            recommendations.append("视距基本满足，建议定期检查维护")
        
        return recommendations
    
    def analyze_curve_sight_distance(self, 
                                   curve_radius: float,
                                   design_speed: float,
                                   lane_width: float) -> Dict[str, float]:
        """分析平曲线内侧视距"""
        try:
            # 计算所需视距
            required_sight_distance = self._calculate_stopping_sight_distance(design_speed, 0)
            
            # 计算曲线内侧净宽
            # M = R - sqrt(R² - (S/2)²)
            # 其中 M 为内侧净宽，R 为曲线半径，S 为视距
            
            if required_sight_distance > 2 * curve_radius:
                # 视距超过曲线长度，需要特殊处理
                inner_clearance = curve_radius
            else:
                inner_clearance = curve_radius - math.sqrt(
                    curve_radius**2 - (required_sight_distance / 2)**2
                )
            
            # 检查是否需要加宽
            available_clearance = lane_width / 2  # 假设可用的内侧宽度
            clearance_deficiency = max(0, inner_clearance - available_clearance)
            
            return {
                'required_sight_distance': required_sight_distance,
                'inner_clearance_required': inner_clearance,
                'available_clearance': available_clearance,
                'clearance_deficiency': clearance_deficiency,
                'curve_radius': curve_radius
            }
            
        except Exception as e:
            logger.error(f"分析平曲线视距失败: {str(e)}")
            return {}
    
    def generate_sight_distance_report(self, 
                                     results: List[SightDistanceResult]) -> Dict:
        """生成视距分析报告"""
        try:
            total_points = len(results)
            inadequate_points = len([r for r in results if not r.is_adequate])
            
            # 统计视距不足的严重程度
            severe_deficiency = len([r for r in results if r.deficiency > r.stopping_sight_distance * 0.3])
            moderate_deficiency = len([r for r in results if 0 < r.deficiency <= r.stopping_sight_distance * 0.3])
            
            # 计算平均视距
            avg_actual_distance = sum(r.actual_sight_distance for r in results) / total_points if total_points > 0 else 0
            avg_required_distance = sum(r.stopping_sight_distance for r in results) / total_points if total_points > 0 else 0
            
            report = {
                'summary': {
                    'total_analysis_points': total_points,
                    'inadequate_points': inadequate_points,
                    'adequacy_rate': (total_points - inadequate_points) / total_points * 100 if total_points > 0 else 100,
                    'severe_deficiency_points': severe_deficiency,
                    'moderate_deficiency_points': moderate_deficiency
                },
                'statistics': {
                    'average_actual_sight_distance': avg_actual_distance,
                    'average_required_sight_distance': avg_required_distance,
                    'max_deficiency': max((r.deficiency for r in results), default=0),
                    'total_deficiency_length': sum(r.deficiency for r in results if r.deficiency > 0)
                },
                'recommendations': self._generate_overall_recommendations(results)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成视距报告失败: {str(e)}")
            return {}
