"""
道路冲突检测核心算法
"""
import numpy as np
import math
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ConflictType(Enum):
    """冲突类型枚举"""
    TERRAIN_CONFLICT = "terrain_conflict"
    INFRASTRUCTURE_CONFLICT = "infrastructure_conflict"
    ENVIRONMENTAL_CONFLICT = "environmental_conflict"
    SAFETY_CONFLICT = "safety_conflict"
    GEOMETRIC_CONFLICT = "geometric_conflict"
    DRAINAGE_CONFLICT = "drainage_conflict"


class ConflictSeverity(Enum):
    """冲突严重程度枚举"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


@dataclass
class ConflictPoint:
    """冲突点"""
    x: float
    y: float
    z: float = 0.0
    chainage: float = 0.0


@dataclass
class ConflictResult:
    """冲突检测结果"""
    conflict_id: str
    conflict_type: ConflictType
    severity: ConflictSeverity
    location: ConflictPoint
    affected_area: List[Tuple[float, float]]
    description: str
    impact_assessment: Dict[str, Any]
    resolution_options: List[Dict[str, Any]]
    confidence_score: float


@dataclass
class RoadGeometry:
    """道路几何数据"""
    points: List[Tuple[float, float, float]]  # (x, y, z)
    chainages: List[float]
    widths: List[float]
    gradients: List[float]
    curvatures: List[float]


class ConflictDetector:
    """冲突检测器主类"""
    
    def __init__(self):
        self.detection_threshold = {
            'terrain_slope': 30.0,      # 地形坡度阈值(度)
            'gradient_limit': 8.0,      # 道路坡度限制(%)
            'radius_limit': 15.0,       # 最小转弯半径(米)
            'sight_distance': 50.0,     # 最小视距(米)
            'clearance_height': 5.0,    # 最小净空高度(米)
            'drainage_slope': 0.5,      # 最小排水坡度(%)
        }
        
        self.severity_weights = {
            'safety_impact': 0.4,
            'cost_impact': 0.3,
            'time_impact': 0.2,
            'environmental_impact': 0.1
        }
    
    def detect_conflicts(self, 
                        road_geometry: RoadGeometry,
                        terrain_data: np.ndarray = None,
                        infrastructure_data: List[Dict] = None,
                        environmental_data: List[Dict] = None) -> List[ConflictResult]:
        """检测道路冲突"""
        try:
            logger.info("开始道路冲突检测")
            
            conflicts = []
            
            # 1. 地形冲突检测
            if terrain_data is not None:
                terrain_conflicts = self._detect_terrain_conflicts(road_geometry, terrain_data)
                conflicts.extend(terrain_conflicts)
            
            # 2. 基础设施冲突检测
            if infrastructure_data:
                infrastructure_conflicts = self._detect_infrastructure_conflicts(
                    road_geometry, infrastructure_data
                )
                conflicts.extend(infrastructure_conflicts)
            
            # 3. 几何冲突检测
            geometric_conflicts = self._detect_geometric_conflicts(road_geometry)
            conflicts.extend(geometric_conflicts)
            
            # 4. 安全冲突检测
            safety_conflicts = self._detect_safety_conflicts(road_geometry)
            conflicts.extend(safety_conflicts)
            
            # 5. 排水冲突检测
            drainage_conflicts = self._detect_drainage_conflicts(road_geometry)
            conflicts.extend(drainage_conflicts)
            
            # 6. 环境冲突检测
            if environmental_data:
                environmental_conflicts = self._detect_environmental_conflicts(
                    road_geometry, environmental_data
                )
                conflicts.extend(environmental_conflicts)
            
            # 按严重程度排序
            conflicts.sort(key=lambda x: self._get_severity_score(x.severity), reverse=True)
            
            logger.info(f"冲突检测完成，发现 {len(conflicts)} 个冲突")
            return conflicts
            
        except Exception as e:
            logger.error(f"冲突检测失败: {str(e)}")
            raise
    
    def _detect_terrain_conflicts(self, 
                                 road_geometry: RoadGeometry,
                                 terrain_data: np.ndarray) -> List[ConflictResult]:
        """检测地形冲突"""
        conflicts = []
        
        try:
            for i, (x, y, z) in enumerate(road_geometry.points):
                chainage = road_geometry.chainages[i] if i < len(road_geometry.chainages) else 0
                
                # 获取地形高程
                terrain_elevation = self._get_terrain_elevation(x, y, terrain_data)
                if terrain_elevation is None:
                    continue
                
                # 计算挖填高度
                cut_fill_height = abs(z - terrain_elevation)
                
                # 检查过度挖填
                if cut_fill_height > 20.0:  # 超过20米的挖填
                    severity = ConflictSeverity.HIGH if cut_fill_height > 30.0 else ConflictSeverity.MEDIUM
                    
                    conflict = ConflictResult(
                        conflict_id=f"terrain_{i}",
                        conflict_type=ConflictType.TERRAIN_CONFLICT,
                        severity=severity,
                        location=ConflictPoint(x, y, z, chainage),
                        affected_area=[(x-10, y-10), (x+10, y-10), (x+10, y+10), (x-10, y+10)],
                        description=f"里程{chainage:.1f}m处存在{cut_fill_height:.1f}m的过度挖填",
                        impact_assessment={
                            'cut_fill_height': cut_fill_height,
                            'cost_impact': cut_fill_height * 100,  # 简化成本计算
                            'stability_risk': 'high' if cut_fill_height > 25 else 'medium'
                        },
                        resolution_options=[
                            {
                                'option': 'adjust_alignment',
                                'description': '调整道路线形',
                                'cost_factor': 0.3
                            },
                            {
                                'option': 'retaining_wall',
                                'description': '设置挡土墙',
                                'cost_factor': 0.8
                            }
                        ],
                        confidence_score=0.9
                    )
                    
                    conflicts.append(conflict)
                
                # 检查地形坡度
                terrain_slope = self._calculate_terrain_slope(x, y, terrain_data)
                if terrain_slope > self.detection_threshold['terrain_slope']:
                    conflict = ConflictResult(
                        conflict_id=f"slope_{i}",
                        conflict_type=ConflictType.TERRAIN_CONFLICT,
                        severity=ConflictSeverity.HIGH,
                        location=ConflictPoint(x, y, z, chainage),
                        affected_area=[(x-5, y-5), (x+5, y-5), (x+5, y+5), (x-5, y+5)],
                        description=f"里程{chainage:.1f}m处地形坡度过陡({terrain_slope:.1f}°)",
                        impact_assessment={
                            'terrain_slope': terrain_slope,
                            'stability_risk': 'critical',
                            'construction_difficulty': 'high'
                        },
                        resolution_options=[
                            {
                                'option': 'route_deviation',
                                'description': '绕行避开陡坡',
                                'cost_factor': 0.5
                            }
                        ],
                        confidence_score=0.85
                    )
                    
                    conflicts.append(conflict)
            
            return conflicts
            
        except Exception as e:
            logger.error(f"地形冲突检测失败: {str(e)}")
            return []
    
    def _detect_infrastructure_conflicts(self, 
                                       road_geometry: RoadGeometry,
                                       infrastructure_data: List[Dict]) -> List[ConflictResult]:
        """检测基础设施冲突"""
        conflicts = []
        
        try:
            for infrastructure in infrastructure_data:
                infra_type = infrastructure.get('type', 'unknown')
                infra_location = infrastructure.get('location', {})
                infra_x = infra_location.get('x', 0)
                infra_y = infra_location.get('y', 0)
                buffer_distance = infrastructure.get('buffer_distance', 10.0)
                
                # 检查道路是否与基础设施冲突
                for i, (x, y, z) in enumerate(road_geometry.points):
                    distance = math.sqrt((x - infra_x)**2 + (y - infra_y)**2)
                    
                    if distance < buffer_distance:
                        chainage = road_geometry.chainages[i] if i < len(road_geometry.chainages) else 0
                        
                        # 确定冲突严重程度
                        if infra_type in ['power_line', 'pipeline']:
                            severity = ConflictSeverity.CRITICAL
                        elif infra_type in ['building', 'structure']:
                            severity = ConflictSeverity.HIGH
                        else:
                            severity = ConflictSeverity.MEDIUM
                        
                        conflict = ConflictResult(
                            conflict_id=f"infra_{infra_type}_{i}",
                            conflict_type=ConflictType.INFRASTRUCTURE_CONFLICT,
                            severity=severity,
                            location=ConflictPoint(x, y, z, chainage),
                            affected_area=[(x-buffer_distance, y-buffer_distance), 
                                         (x+buffer_distance, y+buffer_distance)],
                            description=f"里程{chainage:.1f}m处与{infra_type}冲突(距离{distance:.1f}m)",
                            impact_assessment={
                                'infrastructure_type': infra_type,
                                'conflict_distance': distance,
                                'required_clearance': buffer_distance,
                                'relocation_required': distance < buffer_distance * 0.5
                            },
                            resolution_options=[
                                {
                                    'option': 'route_adjustment',
                                    'description': '调整道路路线',
                                    'cost_factor': 0.4
                                },
                                {
                                    'option': 'infrastructure_relocation',
                                    'description': '迁移基础设施',
                                    'cost_factor': 1.5
                                }
                            ],
                            confidence_score=0.95
                        )
                        
                        conflicts.append(conflict)
            
            return conflicts
            
        except Exception as e:
            logger.error(f"基础设施冲突检测失败: {str(e)}")
            return []
    
    def _detect_geometric_conflicts(self, road_geometry: RoadGeometry) -> List[ConflictResult]:
        """检测几何冲突"""
        conflicts = []
        
        try:
            # 检查坡度超限
            for i, gradient in enumerate(road_geometry.gradients):
                if abs(gradient) > self.detection_threshold['gradient_limit']:
                    chainage = road_geometry.chainages[i] if i < len(road_geometry.chainages) else 0
                    x, y, z = road_geometry.points[i] if i < len(road_geometry.points) else (0, 0, 0)
                    
                    severity = ConflictSeverity.CRITICAL if abs(gradient) > 12.0 else ConflictSeverity.HIGH
                    
                    conflict = ConflictResult(
                        conflict_id=f"gradient_{i}",
                        conflict_type=ConflictType.GEOMETRIC_CONFLICT,
                        severity=severity,
                        location=ConflictPoint(x, y, z, chainage),
                        affected_area=[(x-20, y-20), (x+20, y+20)],
                        description=f"里程{chainage:.1f}m处坡度超限({gradient:.1f}%)",
                        impact_assessment={
                            'current_gradient': gradient,
                            'limit_gradient': self.detection_threshold['gradient_limit'],
                            'safety_impact': 'high',
                            'vehicle_performance': 'degraded'
                        },
                        resolution_options=[
                            {
                                'option': 'vertical_curve',
                                'description': '增加竖曲线缓解坡度',
                                'cost_factor': 0.6
                            },
                            {
                                'option': 'route_optimization',
                                'description': '优化路线降低坡度',
                                'cost_factor': 0.8
                            }
                        ],
                        confidence_score=0.9
                    )
                    
                    conflicts.append(conflict)
            
            # 检查曲率半径
            for i, curvature in enumerate(road_geometry.curvatures):
                if curvature > 0:  # 有曲率
                    radius = 1.0 / curvature
                    if radius < self.detection_threshold['radius_limit']:
                        chainage = road_geometry.chainages[i] if i < len(road_geometry.chainages) else 0
                        x, y, z = road_geometry.points[i] if i < len(road_geometry.points) else (0, 0, 0)
                        
                        severity = ConflictSeverity.HIGH if radius < 10.0 else ConflictSeverity.MEDIUM
                        
                        conflict = ConflictResult(
                            conflict_id=f"radius_{i}",
                            conflict_type=ConflictType.GEOMETRIC_CONFLICT,
                            severity=severity,
                            location=ConflictPoint(x, y, z, chainage),
                            affected_area=[(x-radius, y-radius), (x+radius, y+radius)],
                            description=f"里程{chainage:.1f}m处转弯半径过小({radius:.1f}m)",
                            impact_assessment={
                                'current_radius': radius,
                                'minimum_radius': self.detection_threshold['radius_limit'],
                                'safety_impact': 'medium',
                                'speed_limitation': True
                            },
                            resolution_options=[
                                {
                                    'option': 'increase_radius',
                                    'description': '增大转弯半径',
                                    'cost_factor': 0.7
                                }
                            ],
                            confidence_score=0.85
                        )
                        
                        conflicts.append(conflict)
            
            return conflicts
            
        except Exception as e:
            logger.error(f"几何冲突检测失败: {str(e)}")
            return []
    
    def _detect_safety_conflicts(self, road_geometry: RoadGeometry) -> List[ConflictResult]:
        """检测安全冲突"""
        conflicts = []
        
        try:
            # 检查视距不足
            for i in range(len(road_geometry.points) - 1):
                # 简化的视距计算
                sight_distance = self._calculate_sight_distance(road_geometry, i)
                
                if sight_distance < self.detection_threshold['sight_distance']:
                    chainage = road_geometry.chainages[i] if i < len(road_geometry.chainages) else 0
                    x, y, z = road_geometry.points[i]
                    
                    conflict = ConflictResult(
                        conflict_id=f"sight_{i}",
                        conflict_type=ConflictType.SAFETY_CONFLICT,
                        severity=ConflictSeverity.HIGH,
                        location=ConflictPoint(x, y, z, chainage),
                        affected_area=[(x-25, y-25), (x+25, y+25)],
                        description=f"里程{chainage:.1f}m处视距不足({sight_distance:.1f}m)",
                        impact_assessment={
                            'current_sight_distance': sight_distance,
                            'required_sight_distance': self.detection_threshold['sight_distance'],
                            'safety_risk': 'high',
                            'accident_probability': 'increased'
                        },
                        resolution_options=[
                            {
                                'option': 'cut_slope',
                                'description': '削减边坡改善视距',
                                'cost_factor': 0.5
                            },
                            {
                                'option': 'warning_signs',
                                'description': '设置警示标志',
                                'cost_factor': 0.1
                            }
                        ],
                        confidence_score=0.8
                    )
                    
                    conflicts.append(conflict)
            
            return conflicts
            
        except Exception as e:
            logger.error(f"安全冲突检测失败: {str(e)}")
            return []
    
    def _detect_drainage_conflicts(self, road_geometry: RoadGeometry) -> List[ConflictResult]:
        """检测排水冲突"""
        conflicts = []
        
        try:
            # 检查排水坡度
            for i, gradient in enumerate(road_geometry.gradients):
                if abs(gradient) < self.detection_threshold['drainage_slope']:
                    chainage = road_geometry.chainages[i] if i < len(road_geometry.chainages) else 0
                    x, y, z = road_geometry.points[i] if i < len(road_geometry.points) else (0, 0, 0)
                    
                    conflict = ConflictResult(
                        conflict_id=f"drainage_{i}",
                        conflict_type=ConflictType.DRAINAGE_CONFLICT,
                        severity=ConflictSeverity.MEDIUM,
                        location=ConflictPoint(x, y, z, chainage),
                        affected_area=[(x-15, y-15), (x+15, y+15)],
                        description=f"里程{chainage:.1f}m处排水坡度不足({gradient:.2f}%)",
                        impact_assessment={
                            'current_gradient': gradient,
                            'minimum_drainage_slope': self.detection_threshold['drainage_slope'],
                            'water_accumulation_risk': 'high',
                            'pavement_damage_risk': 'medium'
                        },
                        resolution_options=[
                            {
                                'option': 'drainage_system',
                                'description': '增设排水设施',
                                'cost_factor': 0.3
                            },
                            {
                                'option': 'profile_adjustment',
                                'description': '调整纵断面',
                                'cost_factor': 0.6
                            }
                        ],
                        confidence_score=0.75
                    )
                    
                    conflicts.append(conflict)
            
            return conflicts
            
        except Exception as e:
            logger.error(f"排水冲突检测失败: {str(e)}")
            return []
    
    def _detect_environmental_conflicts(self, 
                                      road_geometry: RoadGeometry,
                                      environmental_data: List[Dict]) -> List[ConflictResult]:
        """检测环境冲突"""
        conflicts = []
        
        try:
            for env_feature in environmental_data:
                feature_type = env_feature.get('type', 'unknown')
                feature_location = env_feature.get('location', {})
                protection_buffer = env_feature.get('protection_buffer', 50.0)
                
                # 检查道路是否进入环境保护区域
                for i, (x, y, z) in enumerate(road_geometry.points):
                    if self._point_in_polygon((x, y), feature_location.get('boundary', [])):
                        chainage = road_geometry.chainages[i] if i < len(road_geometry.chainages) else 0
                        
                        severity = ConflictSeverity.CRITICAL if feature_type == 'protected_area' else ConflictSeverity.HIGH
                        
                        conflict = ConflictResult(
                            conflict_id=f"env_{feature_type}_{i}",
                            conflict_type=ConflictType.ENVIRONMENTAL_CONFLICT,
                            severity=severity,
                            location=ConflictPoint(x, y, z, chainage),
                            affected_area=feature_location.get('boundary', []),
                            description=f"里程{chainage:.1f}m处穿越{feature_type}",
                            impact_assessment={
                                'environmental_feature': feature_type,
                                'protection_level': env_feature.get('protection_level', 'medium'),
                                'ecological_impact': 'high',
                                'permit_required': True
                            },
                            resolution_options=[
                                {
                                    'option': 'route_avoidance',
                                    'description': '绕行避开环境敏感区',
                                    'cost_factor': 0.8
                                },
                                {
                                    'option': 'mitigation_measures',
                                    'description': '采取环境缓解措施',
                                    'cost_factor': 0.4
                                }
                            ],
                            confidence_score=0.9
                        )
                        
                        conflicts.append(conflict)
            
            return conflicts
            
        except Exception as e:
            logger.error(f"环境冲突检测失败: {str(e)}")
            return []
    
    def _get_terrain_elevation(self, x: float, y: float, terrain_data: np.ndarray) -> Optional[float]:
        """获取地形高程"""
        # 简化实现，实际应该进行插值
        return 100.0  # 模拟高程值
    
    def _calculate_terrain_slope(self, x: float, y: float, terrain_data: np.ndarray) -> float:
        """计算地形坡度"""
        # 简化实现
        return 15.0  # 模拟坡度值
    
    def _calculate_sight_distance(self, road_geometry: RoadGeometry, index: int) -> float:
        """计算视距"""
        # 简化实现
        return 45.0  # 模拟视距值
    
    def _point_in_polygon(self, point: Tuple[float, float], polygon: List[Tuple[float, float]]) -> bool:
        """判断点是否在多边形内"""
        if not polygon:
            return False
        
        x, y = point
        n = len(polygon)
        inside = False
        
        p1x, p1y = polygon[0]
        for i in range(1, n + 1):
            p2x, p2y = polygon[i % n]
            if y > min(p1y, p2y):
                if y <= max(p1y, p2y):
                    if x <= max(p1x, p2x):
                        if p1y != p2y:
                            xinters = (y - p1y) * (p2x - p1x) / (p2y - p1y) + p1x
                        if p1x == p2x or x <= xinters:
                            inside = not inside
            p1x, p1y = p2x, p2y
        
        return inside
    
    def _get_severity_score(self, severity: ConflictSeverity) -> int:
        """获取严重程度评分"""
        severity_scores = {
            ConflictSeverity.LOW: 1,
            ConflictSeverity.MEDIUM: 2,
            ConflictSeverity.HIGH: 3,
            ConflictSeverity.CRITICAL: 4
        }
        return severity_scores.get(severity, 0)
