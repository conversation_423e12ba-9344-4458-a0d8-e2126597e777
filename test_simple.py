#!/usr/bin/env python3
"""
最简单的测试脚本
"""
import sys
import os

def test_python():
    """测试Python环境"""
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    print(f"当前目录: {os.getcwd()}")
    
def test_imports():
    """测试基础导入"""
    modules = [
        ('json', 'JSON'),
        ('http.server', 'HTTP服务器'),
        ('pathlib', '路径处理'),
        ('datetime', '日期时间'),
    ]
    
    for module, name in modules:
        try:
            __import__(module)
            print(f"✅ {name}")
        except ImportError:
            print(f"❌ {name}")

def test_fastapi():
    """测试FastAPI"""
    try:
        import fastapi
        import uvicorn
        print("✅ FastAPI可用")
        return True
    except ImportError:
        print("❌ FastAPI不可用")
        return False

def create_simple_server():
    """创建简单的HTTP服务器"""
    print("创建简单HTTP服务器...")
    
    from http.server import HTTPServer, BaseHTTPRequestHandler
    import json
    
    class SimpleHandler(BaseHTTPRequestHandler):
        def do_GET(self):
            if self.path == '/':
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                response = {
                    "message": "露天矿山道路设计软件",
                    "version": "1.0.0",
                    "status": "running"
                }
                self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
            elif self.path == '/health':
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.send_header('Access-Control-Allow-Origin', '*')
                self.end_headers()
                response = {
                    "status": "healthy",
                    "timestamp": "2024-01-01T00:00:00Z"
                }
                self.wfile.write(json.dumps(response).encode('utf-8'))
            else:
                self.send_response(404)
                self.end_headers()
                
        def log_message(self, format, *args):
            print(f"[{self.address_string()}] {format % args}")
    
    try:
        server = HTTPServer(('localhost', 8000), SimpleHandler)
        print("🌐 服务器启动在: http://localhost:8000")
        print("🔍 健康检查: http://localhost:8000/health")
        print("按 Ctrl+C 停止服务器")
        server.serve_forever()
    except KeyboardInterrupt:
        print("\n✅ 服务器已停止")
        server.shutdown()

def main():
    """主函数"""
    print("=" * 50)
    print("🏔️  露天矿山道路设计软件 - 环境测试")
    print("=" * 50)
    
    test_python()
    print()
    test_imports()
    print()
    
    if test_fastapi():
        print("将使用FastAPI启动服务...")
        # 这里可以启动FastAPI服务
    else:
        print("将使用简单HTTP服务器...")
        create_simple_server()

if __name__ == "__main__":
    main()
