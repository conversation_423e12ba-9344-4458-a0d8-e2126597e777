"""
地形数据服务
"""
import os
import uuid
import shutil
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from fastapi import UploadFile
import asyncio
from datetime import datetime

from app.models.terrain import TerrainData
from app.models.project import Project
from app.core.config import settings
from algorithms.terrain_processing import TerrainProcessor
import logging

logger = logging.getLogger(__name__)


class TerrainService:
    """地形数据服务类"""
    
    def __init__(self):
        self.processor = TerrainProcessor()
        self.upload_dir = settings.UPLOAD_DIR
        self.terrain_dir = settings.TERRAIN_DATA_DIR
        
        # 确保目录存在
        os.makedirs(self.upload_dir, exist_ok=True)
        os.makedirs(self.terrain_dir, exist_ok=True)
    
    async def upload_terrain_file(self, 
                                 db: Session,
                                 project_id: int,
                                 file: UploadFile,
                                 description: str = None) -> TerrainData:
        """上传地形数据文件"""
        try:
            # 验证项目存在
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise ValueError(f"项目不存在: {project_id}")
            
            # 验证文件格式
            file_ext = os.path.splitext(file.filename)[1].lower()
            if file_ext not in self.processor.supported_formats:
                raise ValueError(f"不支持的文件格式: {file_ext}")
            
            # 验证文件大小
            if file.size > settings.MAX_FILE_SIZE:
                raise ValueError(f"文件大小超过限制: {file.size} > {settings.MAX_FILE_SIZE}")
            
            # 生成唯一文件名
            file_id = str(uuid.uuid4())
            filename = f"{file_id}{file_ext}"
            file_path = os.path.join(self.upload_dir, filename)
            
            # 保存文件
            with open(file_path, "wb") as buffer:
                content = await file.read()
                buffer.write(content)
            
            # 验证文件完整性
            is_valid, message = self.processor.validate_file(file_path)
            if not is_valid:
                os.remove(file_path)
                raise ValueError(f"文件验证失败: {message}")
            
            # 提取元数据
            metadata = self.processor.extract_metadata(file_path)
            if not metadata:
                os.remove(file_path)
                raise ValueError("无法提取文件元数据")
            
            # 创建数据库记录
            terrain_data = TerrainData(
                project_id=project_id,
                name=file.filename,
                description=description,
                data_type=metadata.data_type,
                file_format=file_ext,
                file_path=file_path,
                file_size=file.size,
                original_filename=file.filename,
                coordinate_system=metadata.coordinate_system,
                bounds=[
                    metadata.bounds.min_x, metadata.bounds.min_y,
                    metadata.bounds.max_x, metadata.bounds.max_y
                ],
                resolution=metadata.resolution,
                min_elevation=metadata.bounds.min_z,
                max_elevation=metadata.bounds.max_z,
                point_count=metadata.point_count,
                processing_status="uploaded"
            )
            
            db.add(terrain_data)
            db.commit()
            db.refresh(terrain_data)
            
            logger.info(f"地形文件上传成功: {file.filename} -> {terrain_data.id}")
            return terrain_data
            
        except Exception as e:
            logger.error(f"地形文件上传失败: {str(e)}")
            # 清理文件
            if 'file_path' in locals() and os.path.exists(file_path):
                os.remove(file_path)
            raise
    
    async def process_terrain_data(self, 
                                  db: Session,
                                  terrain_id: int,
                                  processing_params: Dict = None) -> bool:
        """处理地形数据"""
        try:
            # 获取地形数据记录
            terrain_data = db.query(TerrainData).filter(TerrainData.id == terrain_id).first()
            if not terrain_data:
                raise ValueError(f"地形数据不存在: {terrain_id}")
            
            # 检查文件是否存在
            if not os.path.exists(terrain_data.file_path):
                raise ValueError(f"地形数据文件不存在: {terrain_data.file_path}")
            
            # 更新状态为处理中
            terrain_data.processing_status = "processing"
            terrain_data.processing_progress = 0.0
            db.commit()
            
            # 创建输出目录
            output_dir = os.path.join(self.terrain_dir, str(terrain_id))
            os.makedirs(output_dir, exist_ok=True)
            
            # 异步处理
            result = await asyncio.get_event_loop().run_in_executor(
                None, 
                self.processor.process_terrain_data,
                terrain_data.file_path,
                output_dir,
                processing_params
            )
            
            if result['status'] == 'success':
                # 更新处理结果
                terrain_data.processing_status = "processed"
                terrain_data.processing_progress = 100.0
                terrain_data.processed_at = datetime.utcnow()
                terrain_data.processing_parameters = processing_params
                terrain_data.metadata = result['metadata']
                
                # 更新统计信息
                terrain_data.update_statistics()
                
                db.commit()
                logger.info(f"地形数据处理成功: {terrain_id}")
                return True
            else:
                # 处理失败
                terrain_data.processing_status = "error"
                terrain_data.error_message = result.get('error_message', '未知错误')
                db.commit()
                logger.error(f"地形数据处理失败: {terrain_id} - {result.get('error_message')}")
                return False
                
        except Exception as e:
            # 更新错误状态
            if 'terrain_data' in locals():
                terrain_data.processing_status = "error"
                terrain_data.error_message = str(e)
                db.commit()
            
            logger.error(f"地形数据处理异常: {str(e)}")
            return False
    
    def get_terrain_data_list(self, 
                             db: Session,
                             project_id: int,
                             skip: int = 0,
                             limit: int = 100) -> List[TerrainData]:
        """获取项目地形数据列表"""
        return db.query(TerrainData)\
                 .filter(TerrainData.project_id == project_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_terrain_data(self, db: Session, terrain_id: int) -> Optional[TerrainData]:
        """获取地形数据详情"""
        return db.query(TerrainData).filter(TerrainData.id == terrain_id).first()
    
    def delete_terrain_data(self, db: Session, terrain_id: int) -> bool:
        """删除地形数据"""
        try:
            terrain_data = db.query(TerrainData).filter(TerrainData.id == terrain_id).first()
            if not terrain_data:
                return False
            
            # 删除文件
            if os.path.exists(terrain_data.file_path):
                os.remove(terrain_data.file_path)
            
            # 删除处理结果目录
            output_dir = os.path.join(self.terrain_dir, str(terrain_id))
            if os.path.exists(output_dir):
                shutil.rmtree(output_dir)
            
            # 删除数据库记录
            db.delete(terrain_data)
            db.commit()
            
            logger.info(f"地形数据删除成功: {terrain_id}")
            return True
            
        except Exception as e:
            logger.error(f"地形数据删除失败: {str(e)}")
            return False
    
    def get_elevation_at_point(self, 
                              db: Session,
                              terrain_id: int,
                              x: float, 
                              y: float) -> Optional[float]:
        """获取指定坐标的高程值"""
        try:
            terrain_data = db.query(TerrainData).filter(TerrainData.id == terrain_id).first()
            if not terrain_data or not terrain_data.is_processed:
                return None
            
            # 这里应该实现从DEM数据中插值获取高程
            # 暂时返回模拟数据
            return 100.0 + (x + y) * 0.01
            
        except Exception as e:
            logger.error(f"获取高程值失败: {str(e)}")
            return None
    
    def get_terrain_profile(self, 
                           db: Session,
                           terrain_id: int,
                           start_point: Tuple[float, float],
                           end_point: Tuple[float, float],
                           sample_count: int = 100) -> Optional[List[Dict]]:
        """获取地形剖面"""
        try:
            terrain_data = db.query(TerrainData).filter(TerrainData.id == terrain_id).first()
            if not terrain_data or not terrain_data.is_processed:
                return None
            
            # 生成剖面线上的采样点
            x1, y1 = start_point
            x2, y2 = end_point
            
            profile_points = []
            for i in range(sample_count):
                t = i / (sample_count - 1)
                x = x1 + t * (x2 - x1)
                y = y1 + t * (y2 - y1)
                z = self.get_elevation_at_point(db, terrain_id, x, y)
                
                distance = t * ((x2 - x1)**2 + (y2 - y1)**2)**0.5
                
                profile_points.append({
                    'distance': distance,
                    'x': x,
                    'y': y,
                    'elevation': z
                })
            
            return profile_points
            
        except Exception as e:
            logger.error(f"获取地形剖面失败: {str(e)}")
            return None
    
    def generate_contours(self, 
                         db: Session,
                         terrain_id: int,
                         interval: float = 5.0) -> bool:
        """生成等高线"""
        try:
            terrain_data = db.query(TerrainData).filter(TerrainData.id == terrain_id).first()
            if not terrain_data or not terrain_data.is_processed:
                return False
            
            output_dir = os.path.join(self.terrain_dir, str(terrain_id))
            dem_path = os.path.join(output_dir, 'processed_dem.tif')
            contour_path = os.path.join(output_dir, 'contours.shp')
            
            return self.processor.generate_contours(dem_path, contour_path, interval)
            
        except Exception as e:
            logger.error(f"生成等高线失败: {str(e)}")
            return False
