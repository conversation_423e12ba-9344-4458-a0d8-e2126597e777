import React, { useState, useEffect } from 'react'
import {
  Card, Upload, message, Table, Button, Space, Progress,
  Modal, Form, Input, Select, Tag, Tooltip, Popconfirm,
  Row, Col, Statistic, Descriptions
} from 'antd'
import {
  UploadOutlined, DeleteOutlined, <PERSON>Outlined,
  PlayCircleOutlined, FileTextOutlined, GlobalOutlined,
  CloudUploadOutlined, CheckCircleOutlined, ExclamationCircleOutlined
} from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import axios from 'axios'

interface TerrainData {
  id: number
  name: string
  description?: string
  data_type: string
  file_format: string
  file_size: number
  processing_status: string
  processing_progress: number
  bounds?: number[]
  resolution?: number
  min_elevation?: number
  max_elevation?: number
  point_count?: number
  created_at: string
}

const TerrainData: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [terrainList, setTerrainList] = useState<TerrainData[]>([])
  const [loading, setLoading] = useState(false)
  const [uploadModalVisible, setUploadModalVisible] = useState(false)
  const [processModalVisible, setProcessModalVisible] = useState(false)
  const [selectedTerrain, setSelectedTerrain] = useState<TerrainData | null>(null)
  const [form] = Form.useForm()
  const [processForm] = Form.useForm()

  // 获取地形数据列表
  const fetchTerrainList = async () => {
    if (!projectId) return

    setLoading(true)
    try {
      const response = await axios.get(`/api/v1/terrain/${projectId}`)
      setTerrainList(response.data)
    } catch (error) {
      message.error('获取地形数据列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchTerrainList()
  }, [projectId])

  // 文件上传配置
  const uploadProps = {
    name: 'file',
    action: '/api/v1/terrain/upload',
    data: {
      project_id: projectId,
      description: form.getFieldValue('description')
    },
    accept: '.tif,.tiff,.las,.laz,.xyz,.txt,.asc,.dem,.shp,.json,.geojson',
    beforeUpload: (file: File) => {
      const isValidFormat = [
        'tif', 'tiff', 'las', 'laz', 'xyz', 'txt',
        'asc', 'dem', 'shp', 'json', 'geojson'
      ].some(ext => file.name.toLowerCase().endsWith(ext))

      if (!isValidFormat) {
        message.error('不支持的文件格式')
        return false
      }

      const isLt100M = file.size / 1024 / 1024 < 100
      if (!isLt100M) {
        message.error('文件大小不能超过100MB')
        return false
      }

      return true
    },
    onChange: (info: any) => {
      if (info.file.status === 'uploading') {
        message.loading('正在上传文件...', 0)
      } else if (info.file.status === 'done') {
        message.destroy()
        message.success('文件上传成功')
        setUploadModalVisible(false)
        form.resetFields()
        fetchTerrainList()
      } else if (info.file.status === 'error') {
        message.destroy()
        message.error('文件上传失败')
      }
    }
  }

  // 处理地形数据
  const handleProcessTerrain = async (terrain: TerrainData) => {
    setSelectedTerrain(terrain)
    setProcessModalVisible(true)
  }

  // 提交处理参数
  const handleProcessSubmit = async () => {
    if (!selectedTerrain) return

    try {
      const values = await processForm.validateFields()
      await axios.post(`/api/v1/terrain/${selectedTerrain.id}/process`, values)
      message.success('地形数据处理已开始')
      setProcessModalVisible(false)
      processForm.resetFields()
      fetchTerrainList()
    } catch (error) {
      message.error('启动处理失败')
    }
  }

  // 删除地形数据
  const handleDeleteTerrain = async (terrainId: number) => {
    try {
      await axios.delete(`/api/v1/terrain/${terrainId}`)
      message.success('地形数据删除成功')
      fetchTerrainList()
    } catch (error) {
      message.error('删除失败')
    }
  }

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      uploaded: { color: 'blue', text: '已上传' },
      processing: { color: 'orange', text: '处理中' },
      processed: { color: 'green', text: '已处理' },
      error: { color: 'red', text: '错误' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 格式化文件大小
  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 表格列定义
  const columns = [
    {
      title: '文件名',
      dataIndex: 'name',
      key: 'name',
      render: (text: string, record: TerrainData) => (
        <Space>
          <FileTextOutlined />
          <span>{text}</span>
          <Tag>{record.file_format}</Tag>
        </Space>
      )
    },
    {
      title: '数据类型',
      dataIndex: 'data_type',
      key: 'data_type'
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size: number) => formatFileSize(size)
    },
    {
      title: '处理状态',
      dataIndex: 'processing_status',
      key: 'processing_status',
      render: (status: string, record: TerrainData) => (
        <Space direction="vertical" size="small">
          {getStatusTag(status)}
          {status === 'processing' && (
            <Progress
              percent={record.processing_progress}
              size="small"
              status="active"
            />
          )}
        </Space>
      )
    },
    {
      title: '分辨率',
      dataIndex: 'resolution',
      key: 'resolution',
      render: (resolution: number) => resolution ? `${resolution}m` : '-'
    },
    {
      title: '高程范围',
      key: 'elevation_range',
      render: (record: TerrainData) => {
        if (record.min_elevation !== undefined && record.max_elevation !== undefined) {
          return `${record.min_elevation.toFixed(1)}m - ${record.max_elevation.toFixed(1)}m`
        }
        return '-'
      }
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: TerrainData) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => {
                // TODO: 实现查看详情
                message.info('查看详情功能开发中')
              }}
            />
          </Tooltip>

          {record.processing_status === 'uploaded' && (
            <Tooltip title="处理数据">
              <Button
                type="text"
                icon={<PlayCircleOutlined />}
                size="small"
                onClick={() => handleProcessTerrain(record)}
              />
            </Tooltip>
          )}

          <Popconfirm
            title="确定要删除这个地形数据吗？"
            onConfirm={() => handleDeleteTerrain(record.id)}
            okText="确定"
            cancelText="取消"
          >
            <Tooltip title="删除">
              <Button
                type="text"
                icon={<DeleteOutlined />}
                size="small"
                danger
              />
            </Tooltip>
          </Popconfirm>
        </Space>
      )
    }
  ]

  // 统计信息
  const getStatistics = () => {
    const total = terrainList.length
    const processed = terrainList.filter(t => t.processing_status === 'processed').length
    const processing = terrainList.filter(t => t.processing_status === 'processing').length
    const totalSize = terrainList.reduce((sum, t) => sum + t.file_size, 0)

    return { total, processed, processing, totalSize }
  }

  const stats = getStatistics()

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          地形数据管理
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          管理项目的三维地形地质数据，支持多种格式的数据导入和处理
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总数据量"
              value={stats.total}
              prefix={<GlobalOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="已处理"
              value={stats.processed}
              prefix={<CheckCircleOutlined />}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="处理中"
              value={stats.processing}
              prefix={<ExclamationCircleOutlined />}
              suffix="个"
              valueStyle={{ color: '#faad14' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总大小"
              value={formatFileSize(stats.totalSize)}
              prefix={<CloudUploadOutlined />}
            />
          </Card>
        </Col>
      </Row>

      {/* 数据列表 */}
      <Card
        title="地形数据列表"
        extra={
          <Button
            type="primary"
            icon={<UploadOutlined />}
            onClick={() => setUploadModalVisible(true)}
          >
            上传数据
          </Button>
        }
      >
        <Table
          columns={columns}
          dataSource={terrainList}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 上传模态框 */}
      <Modal
        title="上传地形数据"
        open={uploadModalVisible}
        onCancel={() => {
          setUploadModalVisible(false)
          form.resetFields()
        }}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="description"
            label="数据描述"
            rules={[{ required: true, message: '请输入数据描述' }]}
          >
            <Input.TextArea
              rows={3}
              placeholder="请描述地形数据的来源、用途等信息"
            />
          </Form.Item>

          <Form.Item label="选择文件">
            <Upload.Dragger {...uploadProps}>
              <p className="ant-upload-drag-icon">
                <UploadOutlined />
              </p>
              <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
              <p className="ant-upload-hint">
                支持格式：TIF, TIFF, LAS, LAZ, XYZ, TXT, ASC, DEM, SHP, JSON, GeoJSON<br/>
                文件大小限制：100MB
              </p>
            </Upload.Dragger>
          </Form.Item>
        </Form>
      </Modal>

      {/* 处理参数模态框 */}
      <Modal
        title="地形数据处理"
        open={processModalVisible}
        onOk={handleProcessSubmit}
        onCancel={() => {
          setProcessModalVisible(false)
          processForm.resetFields()
        }}
        width={600}
      >
        <Form form={processForm} layout="vertical">
          <Form.Item
            name="target_resolution"
            label="目标分辨率 (米)"
            tooltip="处理后的数据分辨率，留空则保持原分辨率"
          >
            <Input type="number" placeholder="例如: 1.0" />
          </Form.Item>

          <Form.Item
            name="interpolation_method"
            label="插值方法"
            initialValue="linear"
          >
            <Select>
              <Select.Option value="linear">线性插值</Select.Option>
              <Select.Option value="cubic">三次样条插值</Select.Option>
              <Select.Option value="nearest">最近邻插值</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="filter_noise"
            label="噪声过滤"
            valuePropName="checked"
            initialValue={true}
          >
            <input type="checkbox" />
          </Form.Item>

          <Form.Item
            name="generate_contours"
            label="生成等高线"
            valuePropName="checked"
            initialValue={true}
          >
            <input type="checkbox" />
          </Form.Item>

          <Form.Item
            name="contour_interval"
            label="等高线间距 (米)"
            initialValue={5.0}
          >
            <Input type="number" />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default TerrainData
