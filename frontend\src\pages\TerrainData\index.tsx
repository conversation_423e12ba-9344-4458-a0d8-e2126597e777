import React from 'react'
import { Card, Upload, Button, message } from 'antd'
import { UploadOutlined, GlobalOutlined } from '@ant-design/icons'

const TerrainData: React.FC = () => {
  const handleUpload = (info: any) => {
    if (info.file.status === 'done') {
      message.success('地形数据上传成功')
    }
  }

  return (
    <div>
      <h1 style={{ marginBottom: '24px' }}>地形数据管理</h1>
      <Card title="数据上传" icon={<GlobalOutlined />}>
        <Upload.Dragger
          name="file"
          multiple
          action="/api/v1/terrain/upload"
          onChange={handleUpload}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持单个或批量上传。支持格式：TIF, LAS, XYZ, SHP等
          </p>
        </Upload.Dragger>
      </Card>
    </div>
  )
}

export default TerrainData
