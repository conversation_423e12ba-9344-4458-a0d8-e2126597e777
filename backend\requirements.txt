# Web框架
fastapi==0.104.1
uvicorn[standard]==0.24.0
python-multipart==0.0.6

# 数据库
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9
redis==5.0.1

# 异步任务
celery==5.3.4
flower==2.0.1

# 认证和安全
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-decouple==3.8

# 数据处理和科学计算
numpy==1.24.4
scipy==1.11.4
pandas==2.1.3
shapely==2.0.2
rasterio==1.3.9
GDAL==3.7.3
geopandas==0.14.1

# AutoCAD文件处理
ezdxf==1.1.4

# 图像处理
Pillow==10.1.0
opencv-python==********

# 数学和优化
networkx==3.2.1
scikit-learn==1.3.2
matplotlib==3.8.2

# API文档
pydantic==2.5.0
pydantic-settings==2.1.0

# 测试
pytest==7.4.3
pytest-asyncio==0.21.1
httpx==0.25.2

# 开发工具
black==23.11.0
flake8==6.1.0
isort==5.12.0
mypy==1.7.1

# 日志和监控
loguru==0.7.2
prometheus-client==0.19.0

# 文件处理
openpyxl==3.1.2
xlsxwriter==3.1.9

# 缓存
python-memcached==1.62

# 配置管理
pyyaml==6.0.1
toml==0.10.2

# HTTP客户端
requests==2.31.0
aiohttp==3.9.1

# 时间处理
python-dateutil==2.8.2
pytz==2023.3

# 数据验证
cerberus==1.3.5

# 地理空间数据处理
pyproj==3.6.1
fiona==1.9.5

# 3D数据处理
trimesh==4.0.5
open3d==0.18.0

# 并行计算
joblib==1.3.2
dask==2023.11.0

# 数据库迁移
yoyo-migrations==8.2.0

# 环境变量
python-dotenv==1.0.0

# CORS支持
fastapi-cors==0.0.6

# 文件上传
aiofiles==23.2.1

# 数据序列化
orjson==3.9.10
msgpack==1.0.7

# 加密
cryptography==41.0.7

# 任务调度
apscheduler==3.10.4

# 内存分析
memory-profiler==0.61.0

# 代码质量
bandit==1.7.5
safety==2.3.5

# 性能监控
py-spy==0.3.14

# 数据库连接池
sqlalchemy-pool==1.3.0

# 异步数据库驱动
asyncpg==0.29.0

# WebSocket支持
websockets==12.0

# 模板引擎
jinja2==3.1.2

# 邮件发送
fastapi-mail==1.4.1

# 限流
slowapi==0.1.9

# 健康检查
fastapi-health==0.4.0
