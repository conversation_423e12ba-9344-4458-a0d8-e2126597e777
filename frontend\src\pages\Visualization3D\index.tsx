import React, { useState, useEffect } from 'react'
import { 
  Card, <PERSON>ton, Space, Slider, Switch, Select, message, 
  Row, Col, Statistic, Tabs, Modal, Form, Input, 
  Descriptions, Tooltip, Alert, List, Progress,
  Typography, Divider
} from 'antd'
import { 
  EyeOutlined, EyeInvisibleOutlined, SettingOutlined,
  FullscreenOutlined, ReloadOutlined, CameraOutlined,
  EnvironmentOutlined, Line<PERSON>hartOutlined, SaveOutlined,
  ExportOutlined, InfoCircleOutlined, ThunderboltOutlined,
  DesktopOutlined, MobileOutlined
} from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import axios from 'axios'
import Cesium3DViewer from '../../components/Cesium3DViewer'

const { TabPane } = Tabs
const { Text, Title } = Typography

interface TerrainData {
  has_terrain: boolean
  bounds?: number[]
  elevation_range?: {
    min: number
    max: number
  }
  resolution?: number
  terrain_count: number
}

interface RoadData {
  id: string
  name: string
  type: string
  coordinates: [number, number, number][]
  width: number
  design_speed: number
  total_length: number
}

interface VisualizationData {
  project_id: number
  project_name: string
  terrain_data: TerrainData
  road_data: RoadData[]
  project_bounds?: number[]
  visualization_config: any
}

interface Viewpoint {
  id: number
  name: string
  description: string
  camera_position: {
    longitude: number
    latitude: number
    height: number
    heading: number
    pitch: number
    roll: number
  }
}

const Visualization3D: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [visualizationData, setVisualizationData] = useState<VisualizationData | null>(null)
  const [viewpoints, setViewpoints] = useState<Viewpoint[]>([])
  const [statistics, setStatistics] = useState<any>(null)
  const [renderingPresets, setRenderingPresets] = useState<any>(null)
  const [systemRequirements, setSystemRequirements] = useState<any>(null)
  const [capabilities, setCapabilities] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [viewpointModalVisible, setViewpointModalVisible] = useState(false)
  const [settingsModalVisible, setSettingsModalVisible] = useState(false)
  const [infoModalVisible, setInfoModalVisible] = useState(false)
  const [currentCamera, setCurrentCamera] = useState<any>(null)
  const [form] = Form.useForm()

  // 获取三维可视化数据
  const fetchVisualizationData = async () => {
    if (!projectId) return
    
    setLoading(true)
    try {
      const response = await axios.get(`/api/v1/visualization/${projectId}/3d-data`)
      setVisualizationData(response.data)
    } catch (error) {
      message.error('获取三维数据失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取视点列表
  const fetchViewpoints = async () => {
    if (!projectId) return
    
    try {
      const response = await axios.get(`/api/v1/visualization/${projectId}/viewpoints`)
      setViewpoints(response.data.viewpoints)
    } catch (error) {
      message.error('获取视点列表失败')
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    if (!projectId) return
    
    try {
      const response = await axios.get(`/api/v1/visualization/${projectId}/statistics`)
      setStatistics(response.data)
    } catch (error) {
      message.error('获取统计信息失败')
    }
  }

  // 获取渲染预设
  const fetchRenderingPresets = async () => {
    try {
      const response = await axios.get('/api/v1/visualization/rendering-presets')
      setRenderingPresets(response.data)
    } catch (error) {
      message.error('获取渲染预设失败')
    }
  }

  // 获取系统要求
  const fetchSystemRequirements = async () => {
    try {
      const response = await axios.get('/api/v1/visualization/system-requirements')
      setSystemRequirements(response.data)
    } catch (error) {
      message.error('获取系统要求失败')
    }
  }

  // 获取功能特性
  const fetchCapabilities = async () => {
    try {
      const response = await axios.get('/api/v1/visualization/capabilities')
      setCapabilities(response.data)
    } catch (error) {
      message.error('获取功能特性失败')
    }
  }

  useEffect(() => {
    fetchVisualizationData()
    fetchViewpoints()
    fetchStatistics()
    fetchRenderingPresets()
    fetchSystemRequirements()
    fetchCapabilities()
  }, [projectId])

  // 保存视点
  const handleSaveViewpoint = async () => {
    if (!currentCamera) {
      message.warning('请先调整相机位置')
      return
    }

    try {
      const values = await form.validateFields()
      
      const response = await axios.post(
        `/api/v1/visualization/${projectId}/save-viewpoint`,
        currentCamera,
        {
          params: {
            name: values.name,
            description: values.description || ''
          }
        }
      )
      
      if (response.data.success) {
        message.success('视点保存成功')
        setViewpointModalVisible(false)
        form.resetFields()
        fetchViewpoints()
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '保存视点失败')
    }
  }

  // 导出三维场景
  const handleExportScene = async () => {
    try {
      const response = await axios.post(`/api/v1/visualization/${projectId}/export-scene`, {}, {
        params: {
          export_format: 'gltf',
          include_terrain: true,
          include_roads: true
        }
      })
      
      if (response.data.success) {
        message.success('三维场景导出完成')
        // 可以添加下载逻辑
      }
    } catch (error: any) {
      message.error(error.response?.data?.detail || '导出三维场景失败')
    }
  }

  // 相机位置变化处理
  const handleCameraChange = (position: any) => {
    setCurrentCamera(position)
  }

  // 实体点击处理
  const handleEntityClick = (entity: any) => {
    console.log('点击实体:', entity)
    // 可以显示实体详情
  }

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Progress type="circle" percent={50} />
          <div style={{ marginTop: '16px' }}>正在加载三维数据...</div>
        </div>
      </Card>
    )
  }

  if (!visualizationData) {
    return (
      <Card>
        <Alert
          message="无法加载三维数据"
          description="请检查项目是否包含地形或道路数据"
          type="error"
          showIcon
        />
      </Card>
    )
  }

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          三维可视化 - {visualizationData.project_name}
        </Title>
        <Text type="secondary">
          基于Cesium的专业三维地理信息可视化，支持地形渲染和道路展示
        </Text>
      </div>

      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="地形文件"
                value={statistics.terrain_files}
                prefix={<EnvironmentOutlined />}
                suffix="个"
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="道路数量"
                value={statistics.road_count}
                prefix={<LineChartOutlined />}
                suffix="条"
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="道路总长"
                value={statistics.total_road_length}
                prefix={<LineChartOutlined />}
                suffix="km"
                precision={1}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="可视化状态"
                value={statistics.visualization_ready ? "就绪" : "未就绪"}
                prefix={statistics.visualization_ready ? <EyeOutlined /> : <EyeInvisibleOutlined />}
                valueStyle={{ 
                  color: statistics.visualization_ready ? '#52c41a' : '#faad14' 
                }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 主要内容 */}
      <Card 
        title="三维场景"
        extra={
          <Space>
            <Button 
              icon={<InfoCircleOutlined />}
              onClick={() => setInfoModalVisible(true)}
            >
              系统信息
            </Button>
            <Button 
              icon={<SaveOutlined />}
              onClick={() => setViewpointModalVisible(true)}
            >
              保存视点
            </Button>
            <Button 
              icon={<ExportOutlined />}
              onClick={handleExportScene}
            >
              导出场景
            </Button>
            <Button 
              icon={<SettingOutlined />}
              onClick={() => setSettingsModalVisible(true)}
            >
              设置
            </Button>
          </Space>
        }
      >
        {/* 三维查看器 */}
        <Cesium3DViewer
          terrainData={visualizationData.terrain_data}
          roadData={visualizationData.road_data}
          projectBounds={visualizationData.project_bounds}
          onCameraChange={handleCameraChange}
          onEntityClick={handleEntityClick}
        />
      </Card>

      {/* 视点列表 */}
      {viewpoints.length > 0 && (
        <Card 
          title="预设视点" 
          style={{ marginTop: '24px' }}
          size="small"
        >
          <Row gutter={[16, 16]}>
            {viewpoints.map(viewpoint => (
              <Col xs={24} sm={12} md={8} lg={6} key={viewpoint.id}>
                <Card 
                  size="small"
                  hoverable
                  onClick={() => {
                    // 这里可以实现跳转到指定视点的逻辑
                    message.info(`跳转到视点: ${viewpoint.name}`)
                  }}
                >
                  <Card.Meta
                    title={viewpoint.name}
                    description={viewpoint.description}
                  />
                  <div style={{ marginTop: '8px', fontSize: '12px', color: '#666' }}>
                    高度: {viewpoint.camera_position.height.toFixed(0)}m
                  </div>
                </Card>
              </Col>
            ))}
          </Row>
        </Card>
      )}

      {/* 保存视点模态框 */}
      <Modal
        title="保存当前视点"
        open={viewpointModalVisible}
        onOk={handleSaveViewpoint}
        onCancel={() => {
          setViewpointModalVisible(false)
          form.resetFields()
        }}
        okText="保存"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="name"
            label="视点名称"
            rules={[{ required: true, message: '请输入视点名称' }]}
          >
            <Input placeholder="例如: 项目总览" />
          </Form.Item>
          
          <Form.Item
            name="description"
            label="描述"
          >
            <Input.TextArea 
              placeholder="视点描述（可选）"
              rows={3}
            />
          </Form.Item>
          
          {currentCamera && (
            <Alert
              message="当前相机位置"
              description={
                <div>
                  <p>经度: {currentCamera.longitude?.toFixed(6)}</p>
                  <p>纬度: {currentCamera.latitude?.toFixed(6)}</p>
                  <p>高度: {currentCamera.height?.toFixed(0)}m</p>
                </div>
              }
              type="info"
              showIcon
            />
          )}
        </Form>
      </Modal>

      {/* 设置模态框 */}
      <Modal
        title="三维可视化设置"
        open={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        footer={null}
        width={700}
      >
        {renderingPresets && (
          <Tabs defaultActiveKey="presets">
            <TabPane tab="渲染预设" key="presets">
              <Row gutter={[16, 16]}>
                {Object.entries(renderingPresets).map(([key, preset]: [string, any]) => (
                  <Col span={8} key={key}>
                    <Card 
                      size="small"
                      hoverable
                      onClick={() => {
                        message.info(`应用预设: ${preset.name}`)
                      }}
                    >
                      <Card.Meta
                        title={preset.name}
                        description={preset.description}
                      />
                    </Card>
                  </Col>
                ))}
              </Row>
            </TabPane>
            
            <TabPane tab="性能优化" key="performance">
              <Space direction="vertical" style={{ width: '100%' }}>
                <Alert
                  message="性能优化建议"
                  description="根据您的设备配置选择合适的渲染设置以获得最佳性能"
                  type="info"
                  showIcon
                />
                
                <Descriptions bordered size="small">
                  <Descriptions.Item label="帧率目标">60 FPS</Descriptions.Item>
                  <Descriptions.Item label="渲染质量">自适应</Descriptions.Item>
                  <Descriptions.Item label="内存使用">优化</Descriptions.Item>
                </Descriptions>
              </Space>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 系统信息模态框 */}
      <Modal
        title="系统信息"
        open={infoModalVisible}
        onCancel={() => setInfoModalVisible(false)}
        footer={null}
        width={800}
      >
        <Tabs defaultActiveKey="requirements">
          <TabPane tab="系统要求" key="requirements">
            {systemRequirements && (
              <Space direction="vertical" style={{ width: '100%' }}>
                {Object.entries(systemRequirements).map(([key, req]: [string, any]) => (
                  <Card key={key} size="small" title={req.description}>
                    <Descriptions size="small" column={1}>
                      <Descriptions.Item label="CPU">{req.cpu}</Descriptions.Item>
                      <Descriptions.Item label="内存">{req.memory}</Descriptions.Item>
                      <Descriptions.Item label="显卡">{req.graphics}</Descriptions.Item>
                      <Descriptions.Item label="存储">{req.storage}</Descriptions.Item>
                      <Descriptions.Item label="浏览器">{req.browser}</Descriptions.Item>
                    </Descriptions>
                  </Card>
                ))}
              </Space>
            )}
          </TabPane>
          
          <TabPane tab="功能特性" key="capabilities">
            {capabilities && (
              <Space direction="vertical" style={{ width: '100%' }}>
                {Object.entries(capabilities).map(([key, cap]: [string, any]) => (
                  <Card key={key} size="small" title={key}>
                    <List
                      size="small"
                      dataSource={cap.features || []}
                      renderItem={(item: string) => (
                        <List.Item>
                          <Text>{item}</Text>
                        </List.Item>
                      )}
                    />
                  </Card>
                ))}
              </Space>
            )}
          </TabPane>
        </Tabs>
      </Modal>
    </div>
  )
}

export default Visualization3D
