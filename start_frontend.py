#!/usr/bin/env python3
"""
前端启动脚本
"""
import os
import sys
import subprocess
import time
from pathlib import Path

def check_node():
    """检查Node.js"""
    try:
        result = subprocess.run(['node', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ Node.js版本: {version}")
            return True
        else:
            print("❌ Node.js不可用")
            return False
    except FileNotFoundError:
        print("❌ 未安装Node.js")
        return False

def check_npm():
    """检查npm"""
    try:
        result = subprocess.run(['npm', '--version'], capture_output=True, text=True)
        if result.returncode == 0:
            version = result.stdout.strip()
            print(f"✅ npm版本: {version}")
            return True
        else:
            print("❌ npm不可用")
            return False
    except FileNotFoundError:
        print("❌ 未安装npm")
        return False

def start_frontend():
    """启动前端开发服务器"""
    frontend_dir = Path('frontend')
    
    if not frontend_dir.exists():
        print("❌ 前端目录不存在")
        return False
    
    print("🚀 启动前端开发服务器...")
    
    try:
        # 切换到前端目录并启动开发服务器
        process = subprocess.Popen(
            ['npm', 'run', 'dev'],
            cwd=frontend_dir,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            text=True,
            bufsize=1,
            universal_newlines=True
        )
        
        print("✅ 前端服务器启动中...")
        print("🌐 前端地址: http://localhost:5173")
        print("按 Ctrl+C 停止服务器\n")
        
        # 实时输出日志
        try:
            for line in process.stdout:
                print(f"[前端] {line.strip()}")
        except KeyboardInterrupt:
            print("\n🛑 正在停止前端服务器...")
            process.terminate()
            process.wait()
            print("✅ 前端服务器已停止")
            
    except Exception as e:
        print(f"❌ 前端服务器启动失败: {e}")
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 50)
    print("🌐 前端开发服务器启动")
    print("=" * 50)
    
    # 检查环境
    if not check_node():
        print("请安装Node.js: https://nodejs.org")
        return False
    
    if not check_npm():
        print("请安装npm")
        return False
    
    # 启动前端
    return start_frontend()

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
