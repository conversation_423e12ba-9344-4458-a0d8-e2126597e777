import React, { Suspense } from 'react'
import { Routes, Route, Navigate } from 'react-router-dom'
import { Layout, Spin } from 'antd'
import { ErrorBoundary } from 'react-error-boundary'

import MainLayout from '@/components/Layout/MainLayout'
import LoadingSpinner from '@/components/Common/LoadingSpinner'
import ErrorFallback from '@/components/Common/ErrorFallback'

// 懒加载页面组件
const Dashboard = React.lazy(() => import('@/pages/Dashboard'))
const ProjectManagement = React.lazy(() => import('@/pages/ProjectManagement'))
const TerrainData = React.lazy(() => import('@/pages/TerrainData'))
const RoadDesign = React.lazy(() => import('@/pages/RoadDesign'))
const ConflictDetection = React.lazy(() => import('@/pages/ConflictDetection'))
const SafetyAnalysis = React.lazy(() => import('@/pages/SafetyAnalysis'))
const RouteOptimization = React.lazy(() => import('@/pages/RouteOptimization'))
const AutoCADIntegration = React.lazy(() => import('@/pages/AutoCADIntegration'))
const Settings = React.lazy(() => import('@/pages/Settings'))
const Login = React.lazy(() => import('@/pages/Auth/Login'))
const Register = React.lazy(() => import('@/pages/Auth/Register'))

// 页面加载组件
const PageLoading: React.FC = () => (
  <div style={{
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    height: '400px'
  }}>
    <LoadingSpinner size="large" tip="正在加载页面..." />
  </div>
)

// 路由保护组件
const ProtectedRoute: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  // TODO: 实现认证逻辑
  const isAuthenticated = true // 临时设置为true

  if (!isAuthenticated) {
    return <Navigate to="/login" replace />
  }

  return <>{children}</>
}

// 主应用组件
const App: React.FC = () => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onError={(error, errorInfo) => {
        console.error('应用错误:', error, errorInfo)
        // 这里可以添加错误上报逻辑
      }}
    >
      <Layout style={{ minHeight: '100vh' }}>
        <Routes>
          {/* 认证路由 */}
          <Route
            path="/login"
            element={
              <Suspense fallback={<PageLoading />}>
                <Login />
              </Suspense>
            }
          />
          <Route
            path="/register"
            element={
              <Suspense fallback={<PageLoading />}>
                <Register />
              </Suspense>
            }
          />

          {/* 主应用路由 */}
          <Route
            path="/*"
            element={
              <ProtectedRoute>
                <MainLayout>
                  <Suspense fallback={<PageLoading />}>
                    <Routes>
                      {/* 仪表板 */}
                      <Route path="/" element={<Dashboard />} />
                      <Route path="/dashboard" element={<Dashboard />} />

                      {/* 项目管理 */}
                      <Route path="/projects" element={<ProjectManagement />} />
                      <Route path="/projects/:id" element={<ProjectManagement />} />

                      {/* 地形数据 */}
                      <Route path="/terrain" element={<TerrainData />} />
                      <Route path="/terrain/:projectId" element={<TerrainData />} />

                      {/* 道路设计 */}
                      <Route path="/road-design" element={<RoadDesign />} />
                      <Route path="/road-design/:projectId" element={<RoadDesign />} />

                      {/* 冲突检测 */}
                      <Route path="/conflict-detection" element={<ConflictDetection />} />
                      <Route path="/conflict-detection/:projectId" element={<ConflictDetection />} />

                      {/* 安全分析 */}
                      <Route path="/safety-analysis" element={<SafetyAnalysis />} />
                      <Route path="/safety-analysis/:projectId" element={<SafetyAnalysis />} />

                      {/* 路线优化 */}
                      <Route path="/route-optimization" element={<RouteOptimization />} />
                      <Route path="/route-optimization/:projectId" element={<RouteOptimization />} />

                      {/* AutoCAD集成 */}
                      <Route path="/autocad" element={<AutoCADIntegration />} />
                      <Route path="/autocad/:projectId" element={<AutoCADIntegration />} />

                      {/* 设置 */}
                      <Route path="/settings" element={<Settings />} />

                      {/* 404页面 */}
                      <Route
                        path="*"
                        element={
                          <div style={{
                            display: 'flex',
                            flexDirection: 'column',
                            alignItems: 'center',
                            justifyContent: 'center',
                            height: '400px',
                            textAlign: 'center'
                          }}>
                            <h1 style={{ fontSize: '72px', color: '#faad14', margin: 0 }}>404</h1>
                            <h2 style={{ color: '#666', marginBottom: '20px' }}>页面未找到</h2>
                            <p style={{ color: '#999', marginBottom: '30px' }}>
                              抱歉，您访问的页面不存在或已被移除。
                            </p>
                            <button
                              onClick={() => window.history.back()}
                              style={{
                                background: '#faad14',
                                color: '#000',
                                border: 'none',
                                padding: '10px 20px',
                                borderRadius: '6px',
                                cursor: 'pointer',
                                fontSize: '14px'
                              }}
                            >
                              返回上一页
                            </button>
                          </div>
                        }
                      />
                    </Routes>
                  </Suspense>
                </MainLayout>
              </ProtectedRoute>
            }
          />
        </Routes>
      </Layout>
    </ErrorBoundary>
  )
}

export default App
