"""
道路几何计算器
"""
import numpy as np
import math
from typing import List, Tu<PERSON>, Dict, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class HorizontalCurve:
    """平曲线"""
    radius: float
    deflection_angle: float  # 偏转角(度)
    tangent_length: float    # 切线长
    curve_length: float      # 曲线长
    external_distance: float # 外距
    middle_ordinate: float   # 中矢距


@dataclass
class VerticalCurve:
    """竖曲线"""
    radius: float
    length: float
    grade_in: float   # 进入坡度(%)
    grade_out: float  # 离开坡度(%)
    curve_type: str   # 'crest' or 'sag'


@dataclass
class CrossSection:
    """横断面"""
    chainage: float
    center_elevation: float
    left_slope: float
    right_slope: float
    road_width: float
    cut_area: float = 0.0
    fill_area: float = 0.0


class GeometricCalculator:
    """几何计算器"""
    
    def __init__(self):
        pass
    
    def calculate_horizontal_curve(self, 
                                 radius: float, 
                                 deflection_angle: float) -> HorizontalCurve:
        """计算平曲线要素"""
        try:
            # 转换角度为弧度
            angle_rad = math.radians(abs(deflection_angle))
            
            # 计算曲线要素
            tangent_length = radius * math.tan(angle_rad / 2)
            curve_length = radius * angle_rad
            external_distance = radius * (1 / math.cos(angle_rad / 2) - 1)
            middle_ordinate = radius * (1 - math.cos(angle_rad / 2))
            
            return HorizontalCurve(
                radius=radius,
                deflection_angle=deflection_angle,
                tangent_length=tangent_length,
                curve_length=curve_length,
                external_distance=external_distance,
                middle_ordinate=middle_ordinate
            )
            
        except Exception as e:
            logger.error(f"计算平曲线要素失败: {str(e)}")
            raise
    
    def calculate_vertical_curve(self, 
                               grade_in: float, 
                               grade_out: float, 
                               design_speed: float = 30.0) -> VerticalCurve:
        """计算竖曲线要素"""
        try:
            # 计算坡度差
            grade_diff = abs(grade_out - grade_in)
            
            # 确定曲线类型
            curve_type = 'crest' if grade_in > grade_out else 'sag'
            
            # 计算最小竖曲线半径
            if curve_type == 'crest':
                # 凸形竖曲线：R = V²/(254(h1+h2)^0.5)
                # 简化计算，假设h1=h2=1.2m
                min_radius = (design_speed ** 2) / (254 * 2.4)
            else:
                # 凹形竖曲线：R = V²/(122+3.5S)
                min_radius = (design_speed ** 2) / (122 + 3.5 * grade_diff)
            
            # 取整到5的倍数
            radius = math.ceil(min_radius / 5) * 5
            
            # 计算竖曲线长度
            curve_length = radius * grade_diff / 100
            
            return VerticalCurve(
                radius=radius,
                length=curve_length,
                grade_in=grade_in,
                grade_out=grade_out,
                curve_type=curve_type
            )
            
        except Exception as e:
            logger.error(f"计算竖曲线要素失败: {str(e)}")
            raise
    
    def calculate_sight_distance(self, 
                                design_speed: float, 
                                curve_radius: float = None,
                                grade: float = 0.0) -> Dict[str, float]:
        """计算视距"""
        try:
            # 停车视距计算
            # S = V*t + V²/(254(f±i))
            # 其中：V=设计速度(km/h), t=反应时间(s), f=摩擦系数, i=纵坡(小数)
            
            reaction_time = 2.5  # 反应时间(秒)
            friction_coeff = 0.35  # 摩擦系数
            grade_decimal = grade / 100  # 转换为小数
            
            # 停车视距
            stopping_distance = (design_speed * reaction_time / 3.6 + 
                               design_speed ** 2 / (254 * (friction_coeff + grade_decimal)))
            
            # 会车视距(双向交通)
            meeting_distance = 2 * stopping_distance
            
            # 超车视距
            overtaking_distance = 4 * stopping_distance
            
            result = {
                'stopping_distance': stopping_distance,
                'meeting_distance': meeting_distance,
                'overtaking_distance': overtaking_distance
            }
            
            # 如果有曲线半径，计算曲线内侧视距
            if curve_radius:
                # 曲线内侧视距计算
                curve_sight_distance = self._calculate_curve_sight_distance(
                    stopping_distance, curve_radius
                )
                result['curve_sight_distance'] = curve_sight_distance
            
            return result
            
        except Exception as e:
            logger.error(f"计算视距失败: {str(e)}")
            raise
    
    def _calculate_curve_sight_distance(self, 
                                      sight_distance: float, 
                                      radius: float) -> float:
        """计算曲线内侧视距"""
        try:
            # 计算所需的内侧净宽
            # M = R - R*cos(S/(2R))
            # 其中：M=内侧净宽, R=曲线半径, S=视距
            
            angle = sight_distance / (2 * radius)
            if angle > 1:  # 防止数学错误
                angle = 1
            
            inner_clearance = radius - radius * math.cos(angle)
            
            return inner_clearance
            
        except Exception as e:
            logger.error(f"计算曲线内侧视距失败: {str(e)}")
            return 0.0
    
    def calculate_super_elevation(self, 
                                radius: float, 
                                design_speed: float,
                                max_super_elevation: float = 6.0) -> float:
        """计算超高"""
        try:
            # 超高计算公式：e = V²/(127R) - f
            # 其中：e=超高(%), V=设计速度(km/h), R=半径(m), f=横向摩擦系数
            
            friction_coeff = 0.15  # 横向摩擦系数
            
            required_super_elevation = (design_speed ** 2) / (127 * radius) - friction_coeff * 100
            
            # 限制在最大超高范围内
            super_elevation = max(0, min(required_super_elevation, max_super_elevation))
            
            return super_elevation
            
        except Exception as e:
            logger.error(f"计算超高失败: {str(e)}")
            return 0.0
    
    def calculate_transition_length(self, 
                                  radius: float, 
                                  design_speed: float,
                                  super_elevation: float) -> float:
        """计算缓和曲线长度"""
        try:
            # 缓和曲线长度计算
            # L = max(L1, L2, L3)
            # L1 = V³/(46.7R) (离心加速度变化率)
            # L2 = 2.7V (超高渐变率)
            # L3 = R/9 (经验公式)
            
            L1 = (design_speed ** 3) / (46.7 * radius)
            L2 = 2.7 * design_speed
            L3 = radius / 9
            
            transition_length = max(L1, L2, L3)
            
            # 取整到5的倍数
            transition_length = math.ceil(transition_length / 5) * 5
            
            return transition_length
            
        except Exception as e:
            logger.error(f"计算缓和曲线长度失败: {str(e)}")
            return 0.0
    
    def calculate_cross_section(self, 
                              chainage: float,
                              center_elevation: float,
                              ground_elevation_left: List[Tuple[float, float]],
                              ground_elevation_right: List[Tuple[float, float]],
                              road_width: float = 6.0,
                              side_slope_cut: float = 1.0,
                              side_slope_fill: float = 1.5) -> CrossSection:
        """计算横断面"""
        try:
            # 计算左右侧坡
            left_slope = 2.0   # 默认横坡2%
            right_slope = 2.0
            
            # 计算挖填面积
            cut_area = 0.0
            fill_area = 0.0
            
            # 左侧计算
            road_edge_left = center_elevation + (road_width / 2) * (left_slope / 100)
            for distance, ground_elev in ground_elevation_left:
                if distance <= road_width / 2:
                    continue
                
                road_elev = road_edge_left + (distance - road_width / 2) * (side_slope_cut / 100)
                
                if road_elev > ground_elev:
                    # 挖方
                    area = (road_elev - ground_elev) * (distance - road_width / 2)
                    cut_area += area
                else:
                    # 填方
                    area = (ground_elev - road_elev) * (distance - road_width / 2)
                    fill_area += area
            
            # 右侧计算(类似)
            road_edge_right = center_elevation + (road_width / 2) * (right_slope / 100)
            for distance, ground_elev in ground_elevation_right:
                if distance <= road_width / 2:
                    continue
                
                road_elev = road_edge_right + (distance - road_width / 2) * (side_slope_cut / 100)
                
                if road_elev > ground_elev:
                    area = (road_elev - ground_elev) * (distance - road_width / 2)
                    cut_area += area
                else:
                    area = (ground_elev - road_elev) * (distance - road_width / 2)
                    fill_area += area
            
            return CrossSection(
                chainage=chainage,
                center_elevation=center_elevation,
                left_slope=left_slope,
                right_slope=right_slope,
                road_width=road_width,
                cut_area=cut_area,
                fill_area=fill_area
            )
            
        except Exception as e:
            logger.error(f"计算横断面失败: {str(e)}")
            raise
    
    def calculate_curve_coordinates(self, 
                                  start_point: Tuple[float, float],
                                  end_point: Tuple[float, float],
                                  radius: float,
                                  deflection_angle: float,
                                  num_points: int = 20) -> List[Tuple[float, float]]:
        """计算曲线坐标点"""
        try:
            x1, y1 = start_point
            x2, y2 = end_point
            
            # 计算曲线中心
            mid_x = (x1 + x2) / 2
            mid_y = (y1 + y2) / 2
            
            # 计算起始角度
            start_angle = math.atan2(y1 - mid_y, x1 - mid_x)
            
            # 生成曲线点
            points = []
            angle_step = math.radians(deflection_angle) / (num_points - 1)
            
            for i in range(num_points):
                angle = start_angle + i * angle_step
                x = mid_x + radius * math.cos(angle)
                y = mid_y + radius * math.sin(angle)
                points.append((x, y))
            
            return points
            
        except Exception as e:
            logger.error(f"计算曲线坐标失败: {str(e)}")
            return []
    
    def validate_geometric_constraints(self, 
                                     segments: List[Dict],
                                     standards: Dict) -> Dict:
        """验证几何约束"""
        try:
            violations = []
            
            for i, segment in enumerate(segments):
                # 检查坡度
                if 'gradient' in segment:
                    gradient = abs(segment['gradient'])
                    max_gradient = standards.get('max_gradient', 8.0)
                    
                    if gradient > max_gradient:
                        violations.append({
                            'segment': i,
                            'type': 'gradient',
                            'value': gradient,
                            'limit': max_gradient,
                            'severity': 'high' if gradient > max_gradient * 1.2 else 'medium'
                        })
                
                # 检查曲线半径
                if 'radius' in segment and segment['radius']:
                    radius = segment['radius']
                    min_radius = standards.get('min_turning_radius', 15.0)
                    
                    if radius < min_radius:
                        violations.append({
                            'segment': i,
                            'type': 'radius',
                            'value': radius,
                            'limit': min_radius,
                            'severity': 'high' if radius < min_radius * 0.8 else 'medium'
                        })
            
            return {
                'is_valid': len(violations) == 0,
                'violations': violations,
                'total_violations': len(violations),
                'high_severity_count': len([v for v in violations if v['severity'] == 'high']),
                'medium_severity_count': len([v for v in violations if v['severity'] == 'medium'])
            }
            
        except Exception as e:
            logger.error(f"验证几何约束失败: {str(e)}")
            return {'is_valid': False, 'error': str(e)}
