"""
道路设计核心算法
"""
import numpy as np
import math
from typing import List, Tu<PERSON>, Dict, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class Point3D:
    """三维点"""
    x: float
    y: float
    z: float = 0.0
    
    def distance_to(self, other: 'Point3D') -> float:
        """计算到另一点的距离"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2 + (self.z - other.z)**2)
    
    def distance_2d_to(self, other: 'Point3D') -> float:
        """计算平面距离"""
        return math.sqrt((self.x - other.x)**2 + (self.y - other.y)**2)


@dataclass
class DesignStandards:
    """露天矿山道路设计标准"""
    # 几何标准
    min_road_width: float = 6.0          # 最小道路宽度(米)
    max_gradient: float = 8.0            # 最大坡度(%)
    min_turning_radius: float = 15.0     # 最小转弯半径(米)
    min_sight_distance: float = 50.0     # 最小视距(米)
    
    # 设计速度
    design_speed: float = 30.0           # 设计速度(km/h)
    
    # 载重标准
    max_axle_load: float = 25.0          # 最大轴载(吨)
    max_gross_weight: float = 100.0      # 最大总重(吨)
    
    # 安全标准
    safety_buffer: float = 2.0           # 安全缓冲距离(米)
    max_super_elevation: float = 6.0     # 最大超高(%)
    
    # 排水标准
    min_cross_slope: float = 2.0         # 最小横坡(%)
    max_cross_slope: float = 4.0         # 最大横坡(%)


@dataclass
class RoadSegment:
    """道路段"""
    start_point: Point3D
    end_point: Point3D
    segment_type: str  # 'straight', 'curve', 'transition'
    radius: Optional[float] = None
    length: Optional[float] = None
    gradient: Optional[float] = None
    
    def calculate_length(self) -> float:
        """计算段长度"""
        if self.length is None:
            self.length = self.start_point.distance_to(self.end_point)
        return self.length
    
    def calculate_gradient(self) -> float:
        """计算坡度"""
        if self.gradient is None:
            horizontal_distance = self.start_point.distance_2d_to(self.end_point)
            if horizontal_distance > 0:
                elevation_diff = self.end_point.z - self.start_point.z
                self.gradient = (elevation_diff / horizontal_distance) * 100
            else:
                self.gradient = 0.0
        return self.gradient


class RoadDesigner:
    """道路设计器"""
    
    def __init__(self, standards: DesignStandards = None):
        self.standards = standards or DesignStandards()
        
    def design_road_alignment(self, 
                            start_point: Point3D,
                            end_point: Point3D,
                            control_points: List[Point3D] = None,
                            terrain_data: np.ndarray = None) -> Dict:
        """设计道路线形"""
        try:
            logger.info(f"开始设计道路线形: {start_point} -> {end_point}")
            
            # 如果没有控制点，生成直线路径
            if not control_points:
                control_points = self._generate_control_points(
                    start_point, end_point, terrain_data
                )
            
            # 生成道路段
            segments = self._create_road_segments(
                start_point, end_point, control_points
            )
            
            # 优化线形
            optimized_segments = self._optimize_alignment(segments, terrain_data)
            
            # 验证设计标准
            validation_result = self._validate_design_standards(optimized_segments)
            
            # 计算几何参数
            geometry_data = self._calculate_geometry(optimized_segments)
            
            # 生成详细点位
            detailed_points = self._generate_detailed_points(optimized_segments)
            
            result = {
                'status': 'success',
                'alignment': {
                    'segments': [self._segment_to_dict(seg) for seg in optimized_segments],
                    'total_length': sum(seg.calculate_length() for seg in optimized_segments),
                    'max_gradient': max(abs(seg.calculate_gradient()) for seg in optimized_segments),
                    'min_radius': min((seg.radius for seg in optimized_segments if seg.radius), default=float('inf'))
                },
                'geometry': geometry_data,
                'detailed_points': detailed_points,
                'validation': validation_result,
                'design_parameters': {
                    'design_speed': self.standards.design_speed,
                    'road_width': self.standards.min_road_width,
                    'standards_compliance': validation_result['is_compliant']
                }
            }
            
            logger.info("道路线形设计完成")
            return result
            
        except Exception as e:
            logger.error(f"道路线形设计失败: {str(e)}")
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def _generate_control_points(self, 
                               start: Point3D, 
                               end: Point3D, 
                               terrain_data: np.ndarray = None) -> List[Point3D]:
        """生成控制点"""
        control_points = []
        
        # 计算直线距离
        total_distance = start.distance_2d_to(end)
        
        # 如果距离较短，不需要控制点
        if total_distance < 100:
            return control_points
        
        # 根据地形生成控制点
        if terrain_data is not None:
            # TODO: 基于地形数据生成最优控制点
            # 这里使用简化的方法
            num_points = max(2, int(total_distance / 200))  # 每200米一个控制点
            
            for i in range(1, num_points):
                t = i / num_points
                x = start.x + t * (end.x - start.x)
                y = start.y + t * (end.y - start.y)
                z = start.z + t * (end.z - start.z)  # 简化的高程插值
                
                control_points.append(Point3D(x, y, z))
        
        return control_points
    
    def _create_road_segments(self, 
                            start: Point3D, 
                            end: Point3D, 
                            control_points: List[Point3D]) -> List[RoadSegment]:
        """创建道路段"""
        segments = []
        
        # 构建点序列
        all_points = [start] + control_points + [end]
        
        # 创建段
        for i in range(len(all_points) - 1):
            segment = RoadSegment(
                start_point=all_points[i],
                end_point=all_points[i + 1],
                segment_type='straight'  # 初始都设为直线段
            )
            segments.append(segment)
        
        # 识别和处理曲线段
        segments = self._identify_curves(segments)
        
        return segments
    
    def _identify_curves(self, segments: List[RoadSegment]) -> List[RoadSegment]:
        """识别和处理曲线段"""
        if len(segments) < 2:
            return segments
        
        processed_segments = []
        
        for i, segment in enumerate(segments):
            # 检查是否需要在段间插入曲线
            if i > 0:
                prev_segment = segments[i - 1]
                
                # 计算转角
                deflection_angle = self._calculate_deflection_angle(
                    prev_segment, segment
                )
                
                # 如果转角较大，插入曲线段
                if abs(deflection_angle) > 5.0:  # 5度以上的转角
                    curve_radius = self._calculate_curve_radius(
                        deflection_angle, self.standards.design_speed
                    )
                    
                    # 确保满足最小转弯半径
                    curve_radius = max(curve_radius, self.standards.min_turning_radius)
                    
                    # 创建曲线段
                    curve_segment = self._create_curve_segment(
                        prev_segment.end_point, segment.start_point,
                        curve_radius, deflection_angle
                    )
                    
                    if curve_segment:
                        processed_segments.append(curve_segment)
            
            processed_segments.append(segment)
        
        return processed_segments
    
    def _calculate_deflection_angle(self, 
                                  seg1: RoadSegment, 
                                  seg2: RoadSegment) -> float:
        """计算偏转角"""
        # 计算两段的方向角
        dx1 = seg1.end_point.x - seg1.start_point.x
        dy1 = seg1.end_point.y - seg1.start_point.y
        angle1 = math.atan2(dy1, dx1)
        
        dx2 = seg2.end_point.x - seg2.start_point.x
        dy2 = seg2.end_point.y - seg2.start_point.y
        angle2 = math.atan2(dy2, dx2)
        
        # 计算偏转角
        deflection = angle2 - angle1
        
        # 标准化到[-π, π]
        while deflection > math.pi:
            deflection -= 2 * math.pi
        while deflection < -math.pi:
            deflection += 2 * math.pi
        
        return math.degrees(deflection)
    
    def _calculate_curve_radius(self, deflection_angle: float, design_speed: float) -> float:
        """计算曲线半径"""
        # 基于设计速度和超高计算最小半径
        # R = V²/(127(e + f))
        # 其中 V=设计速度(km/h), e=超高, f=横向摩擦系数
        
        max_super_elevation = self.standards.max_super_elevation / 100  # 转换为小数
        friction_coefficient = 0.15  # 典型的横向摩擦系数
        
        min_radius = (design_speed ** 2) / (127 * (max_super_elevation + friction_coefficient))
        
        # 根据偏转角调整半径
        angle_factor = abs(deflection_angle) / 90.0  # 标准化到0-1
        radius = min_radius * (1 + angle_factor)
        
        return max(radius, self.standards.min_turning_radius)
    
    def _create_curve_segment(self, 
                            start: Point3D, 
                            end: Point3D,
                            radius: float, 
                            deflection_angle: float) -> Optional[RoadSegment]:
        """创建曲线段"""
        try:
            # 计算曲线中点
            mid_x = (start.x + end.x) / 2
            mid_y = (start.y + end.y) / 2
            mid_z = (start.z + end.z) / 2
            
            curve_segment = RoadSegment(
                start_point=start,
                end_point=end,
                segment_type='curve',
                radius=radius
            )
            
            return curve_segment
            
        except Exception as e:
            logger.warning(f"创建曲线段失败: {str(e)}")
            return None
    
    def _optimize_alignment(self, 
                          segments: List[RoadSegment], 
                          terrain_data: np.ndarray = None) -> List[RoadSegment]:
        """优化线形"""
        # 这里可以实现更复杂的优化算法
        # 目前返回原始段
        return segments
    
    def _validate_design_standards(self, segments: List[RoadSegment]) -> Dict:
        """验证设计标准"""
        violations = []
        
        for i, segment in enumerate(segments):
            # 检查坡度
            gradient = abs(segment.calculate_gradient())
            if gradient > self.standards.max_gradient:
                violations.append({
                    'segment': i,
                    'type': 'gradient',
                    'value': gradient,
                    'limit': self.standards.max_gradient,
                    'message': f'段{i}坡度{gradient:.1f}%超过限制{self.standards.max_gradient}%'
                })
            
            # 检查转弯半径
            if segment.radius and segment.radius < self.standards.min_turning_radius:
                violations.append({
                    'segment': i,
                    'type': 'radius',
                    'value': segment.radius,
                    'limit': self.standards.min_turning_radius,
                    'message': f'段{i}转弯半径{segment.radius:.1f}m小于最小值{self.standards.min_turning_radius}m'
                })
        
        return {
            'is_compliant': len(violations) == 0,
            'violations': violations,
            'total_violations': len(violations)
        }
    
    def _calculate_geometry(self, segments: List[RoadSegment]) -> Dict:
        """计算几何参数"""
        total_length = sum(seg.calculate_length() for seg in segments)
        max_gradient = max(abs(seg.calculate_gradient()) for seg in segments)
        
        # 计算曲线段统计
        curve_segments = [seg for seg in segments if seg.segment_type == 'curve']
        total_curve_length = sum(seg.calculate_length() for seg in curve_segments)
        
        return {
            'total_length': total_length,
            'straight_length': total_length - total_curve_length,
            'curve_length': total_curve_length,
            'curve_percentage': (total_curve_length / total_length * 100) if total_length > 0 else 0,
            'max_gradient': max_gradient,
            'segment_count': len(segments),
            'curve_count': len(curve_segments)
        }
    
    def _generate_detailed_points(self, segments: List[RoadSegment], interval: float = 10.0) -> List[Dict]:
        """生成详细点位"""
        detailed_points = []
        chainage = 0.0
        
        for segment in segments:
            segment_length = segment.calculate_length()
            num_points = max(2, int(segment_length / interval))
            
            for i in range(num_points):
                t = i / (num_points - 1) if num_points > 1 else 0
                
                # 线性插值计算点位
                x = segment.start_point.x + t * (segment.end_point.x - segment.start_point.x)
                y = segment.start_point.y + t * (segment.end_point.y - segment.start_point.y)
                z = segment.start_point.z + t * (segment.end_point.z - segment.start_point.z)
                
                point_chainage = chainage + t * segment_length
                
                detailed_points.append({
                    'chainage': point_chainage,
                    'x': x,
                    'y': y,
                    'z': z,
                    'segment_type': segment.segment_type,
                    'gradient': segment.calculate_gradient()
                })
            
            chainage += segment_length
        
        return detailed_points
    
    def _segment_to_dict(self, segment: RoadSegment) -> Dict:
        """将段转换为字典"""
        return {
            'start_point': {
                'x': segment.start_point.x,
                'y': segment.start_point.y,
                'z': segment.start_point.z
            },
            'end_point': {
                'x': segment.end_point.x,
                'y': segment.end_point.y,
                'z': segment.end_point.z
            },
            'segment_type': segment.segment_type,
            'length': segment.calculate_length(),
            'gradient': segment.calculate_gradient(),
            'radius': segment.radius
        }
