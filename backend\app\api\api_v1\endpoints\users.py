"""
用户管理API端点
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.core.database import get_db

router = APIRouter()


@router.get("/me")
async def get_current_user(db: Session = Depends(get_db)):
    """获取当前用户信息"""
    return {"message": "获取当前用户信息"}


@router.put("/me")
async def update_current_user(db: Session = Depends(get_db)):
    """更新当前用户信息"""
    return {"message": "更新用户信息成功"}


@router.get("/")
async def get_users(db: Session = Depends(get_db)):
    """获取用户列表"""
    return {"message": "获取用户列表"}


@router.get("/{user_id}")
async def get_user(user_id: int, db: Session = Depends(get_db)):
    """获取指定用户信息"""
    return {"message": f"获取用户{user_id}信息"}
