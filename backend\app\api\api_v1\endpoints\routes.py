"""
运输路线优化API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from app.core.database import get_db
from app.services.route_service import RouteService
from app.models.route_optimization import RouteOptimization

router = APIRouter()
route_service = RouteService()


class TransportTaskModel(BaseModel):
    """运输任务模型"""
    task_id: Optional[str] = None
    origin: str
    destination: str
    cargo_volume: float
    cargo_weight: float
    priority: int = 3
    time_window: Optional[List[float]] = None
    vehicle_requirements: Optional[List[str]] = None


class VehicleModel(BaseModel):
    """车辆模型"""
    vehicle_id: Optional[str] = None
    vehicle_type: str = "dump_truck"
    capacity: float
    max_speed: float = 30.0
    fuel_consumption: float = 25.0
    operating_cost: float = 200.0
    weight: float = 15000.0
    dimensions: List[float] = [8.0, 2.5, 3.0]


class RouteOptimizationParams(BaseModel):
    """路线优化参数"""
    tasks: List[TransportTaskModel]
    vehicles: List[VehicleModel]
    objectives: List[str] = ["minimize_distance", "minimize_time"]
    optimization_type: str = "comprehensive"


class PathFindingParams(BaseModel):
    """路径查找参数"""
    start_point: Dict[str, float]  # {"x": 0, "y": 0, "z": 0}
    end_point: Dict[str, float]
    algorithm: str = "a_star"
    cost_function: str = "distance"


class RouteOptimizationResponse(BaseModel):
    """路线优化响应模型"""
    id: int
    project_id: int
    optimization_type: str
    total_routes: int
    total_distance: Optional[float]
    total_time: Optional[float]
    total_cost: Optional[float]
    optimization_score: Optional[float]
    optimized_at: str
    
    class Config:
        from_attributes = True


@router.post("/{project_id}/optimize")
async def optimize_transport_routes(
    project_id: int,
    optimization_params: RouteOptimizationParams,
    db: Session = Depends(get_db)
):
    """优化运输路线"""
    try:
        route_optimization = await route_service.optimize_transport_routes(
            db=db,
            project_id=project_id,
            optimization_params=optimization_params.dict()
        )
        
        return RouteOptimizationResponse(
            id=route_optimization.id,
            project_id=route_optimization.project_id,
            optimization_type=route_optimization.optimization_type,
            total_routes=route_optimization.total_routes,
            total_distance=route_optimization.total_distance,
            total_time=route_optimization.total_time,
            total_cost=route_optimization.total_cost,
            optimization_score=route_optimization.optimization_score,
            optimized_at=route_optimization.optimized_at.isoformat()
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"路线优化失败: {str(e)}")


@router.post("/{project_id}/find-path")
async def find_optimal_path(
    project_id: int,
    path_params: PathFindingParams,
    db: Session = Depends(get_db)
):
    """查找最优路径"""
    try:
        path_result = await route_service.find_optimal_path(
            db=db,
            project_id=project_id,
            start_point=path_params.start_point,
            end_point=path_params.end_point,
            algorithm=path_params.algorithm
        )
        
        return path_result
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"路径查找失败: {str(e)}")


@router.get("/{project_id}/optimizations", response_model=List[RouteOptimizationResponse])
async def get_project_optimizations(
    project_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取项目路线优化列表"""
    try:
        optimizations = route_service.get_project_optimizations(
            db=db,
            project_id=project_id,
            skip=skip,
            limit=limit
        )
        
        return [
            RouteOptimizationResponse(
                id=opt.id,
                project_id=opt.project_id,
                optimization_type=opt.optimization_type,
                total_routes=opt.total_routes,
                total_distance=opt.total_distance,
                total_time=opt.total_time,
                total_cost=opt.total_cost,
                optimization_score=opt.optimization_score,
                optimized_at=opt.optimized_at.isoformat()
            )
            for opt in optimizations
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取路线优化列表失败: {str(e)}")


@router.get("/optimizations/{optimization_id}", response_model=RouteOptimizationResponse)
async def get_optimization_detail(
    optimization_id: int,
    db: Session = Depends(get_db)
):
    """获取路线优化详情"""
    try:
        optimization = route_service.get_optimization_detail(db=db, optimization_id=optimization_id)
        
        if not optimization:
            raise HTTPException(status_code=404, detail="路线优化不存在")
        
        return RouteOptimizationResponse(
            id=optimization.id,
            project_id=optimization.project_id,
            optimization_type=optimization.optimization_type,
            total_routes=optimization.total_routes,
            total_distance=optimization.total_distance,
            total_time=optimization.total_time,
            total_cost=optimization.total_cost,
            optimization_score=optimization.optimization_score,
            optimized_at=optimization.optimized_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取路线优化详情失败: {str(e)}")


@router.get("/{project_id}/network-analysis")
async def analyze_route_network(
    project_id: int,
    db: Session = Depends(get_db)
):
    """分析路线网络"""
    try:
        network_analysis = await route_service.analyze_route_network(
            db=db,
            project_id=project_id
        )
        
        return network_analysis
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"路线网络分析失败: {str(e)}")


@router.get("/{project_id}/optimization-statistics")
async def get_optimization_statistics(
    project_id: int,
    db: Session = Depends(get_db)
):
    """获取路线优化统计信息"""
    try:
        statistics = route_service.get_optimization_statistics(db=db, project_id=project_id)
        
        return {
            "project_id": project_id,
            "statistics": statistics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取路线优化统计失败: {str(e)}")


@router.post("/{project_id}/batch-optimize")
async def batch_optimize_routes(
    project_id: int,
    optimization_params: RouteOptimizationParams,
    scenarios: List[Dict[str, Any]] = Query(None, description="优化场景列表"),
    db: Session = Depends(get_db)
):
    """批量路线优化"""
    try:
        if not scenarios:
            scenarios = [{"name": "default", "params": optimization_params.dict()}]
        
        results = []
        for i, scenario in enumerate(scenarios):
            try:
                scenario_params = scenario.get('params', optimization_params.dict())
                
                route_optimization = await route_service.optimize_transport_routes(
                    db=db,
                    project_id=project_id,
                    optimization_params=scenario_params
                )
                
                results.append({
                    'scenario_name': scenario.get('name', f'scenario_{i}'),
                    'optimization_id': route_optimization.id,
                    'total_routes': route_optimization.total_routes,
                    'total_distance': route_optimization.total_distance,
                    'total_cost': route_optimization.total_cost,
                    'optimization_score': route_optimization.optimization_score,
                    'status': 'success'
                })
                
            except Exception as e:
                results.append({
                    'scenario_name': scenario.get('name', f'scenario_{i}'),
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'project_id': project_id,
            'total_scenarios': len(scenarios),
            'successful_optimizations': len([r for r in results if r['status'] == 'success']),
            'failed_optimizations': len([r for r in results if r['status'] == 'failed']),
            'results': results
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量路线优化失败: {str(e)}")


@router.get("/{project_id}/route-comparison")
async def compare_routes(
    project_id: int,
    optimization_ids: List[int] = Query(..., description="要比较的优化ID列表"),
    db: Session = Depends(get_db)
):
    """比较不同的路线优化结果"""
    try:
        comparisons = []
        
        for opt_id in optimization_ids:
            optimization = route_service.get_optimization_detail(db=db, optimization_id=opt_id)
            
            if optimization:
                comparisons.append({
                    'optimization_id': opt_id,
                    'total_routes': optimization.total_routes,
                    'total_distance': optimization.total_distance,
                    'total_time': optimization.total_time,
                    'total_cost': optimization.total_cost,
                    'optimization_score': optimization.optimization_score,
                    'optimized_at': optimization.optimized_at.isoformat()
                })
        
        # 计算比较指标
        if len(comparisons) > 1:
            best_distance = min(c['total_distance'] for c in comparisons if c['total_distance'])
            best_cost = min(c['total_cost'] for c in comparisons if c['total_cost'])
            best_score = max(c['optimization_score'] for c in comparisons if c['optimization_score'])
            
            for comparison in comparisons:
                comparison['distance_rank'] = sorted([c['total_distance'] for c in comparisons if c['total_distance']]).index(comparison['total_distance']) + 1 if comparison['total_distance'] else None
                comparison['cost_rank'] = sorted([c['total_cost'] for c in comparisons if c['total_cost']]).index(comparison['total_cost']) + 1 if comparison['total_cost'] else None
                comparison['score_rank'] = sorted([c['optimization_score'] for c in comparisons if c['optimization_score']], reverse=True).index(comparison['optimization_score']) + 1 if comparison['optimization_score'] else None
        
        return {
            'project_id': project_id,
            'comparison_count': len(comparisons),
            'comparisons': comparisons
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"路线比较失败: {str(e)}")


@router.get("/algorithms/available")
async def get_available_algorithms():
    """获取可用的路径查找算法"""
    try:
        algorithms = {
            'path_finding': {
                'dijkstra': {
                    'name': 'Dijkstra算法',
                    'description': '经典最短路径算法，保证找到最优解',
                    'complexity': 'O(V²)',
                    'best_for': '小到中等规模的图'
                },
                'a_star': {
                    'name': 'A*算法',
                    'description': '启发式搜索算法，通常比Dijkstra更快',
                    'complexity': 'O(b^d)',
                    'best_for': '有良好启发函数的问题'
                },
                'floyd_warshall': {
                    'name': 'Floyd-Warshall算法',
                    'description': '计算所有节点对之间的最短路径',
                    'complexity': 'O(V³)',
                    'best_for': '小规模图的全对最短路径'
                }
            },
            'optimization_objectives': {
                'minimize_distance': '最小化距离',
                'minimize_time': '最小化时间',
                'minimize_cost': '最小化成本',
                'minimize_fuel': '最小化燃料消耗',
                'maximize_safety': '最大化安全性',
                'minimize_environmental_impact': '最小化环境影响'
            },
            'vehicle_types': {
                'dump_truck': '自卸卡车',
                'excavator': '挖掘机',
                'bulldozer': '推土机',
                'grader': '平地机',
                'water_truck': '洒水车'
            }
        }
        
        return algorithms
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取算法信息失败: {str(e)}")


@router.get("/standards/optimization-criteria")
async def get_optimization_criteria():
    """获取路线优化标准和评判标准"""
    try:
        criteria = {
            'distance_optimization': {
                'description': '距离优化标准',
                'parameters': {
                    'shortest_path': '最短路径优先',
                    'road_quality': '道路质量权重',
                    'traffic_conditions': '交通状况考虑'
                },
                'evaluation': {
                    'excellent': '距离减少 >= 20%',
                    'good': '距离减少 >= 10%',
                    'acceptable': '距离减少 >= 5%',
                    'poor': '距离增加或减少 < 5%'
                }
            },
            'cost_optimization': {
                'description': '成本优化标准',
                'parameters': {
                    'fuel_cost': '燃料成本',
                    'time_cost': '时间成本',
                    'vehicle_wear': '车辆磨损成本',
                    'maintenance_cost': '维护成本'
                },
                'evaluation': {
                    'excellent': '成本降低 >= 25%',
                    'good': '成本降低 >= 15%',
                    'acceptable': '成本降低 >= 8%',
                    'poor': '成本降低 < 8%'
                }
            },
            'safety_optimization': {
                'description': '安全优化标准',
                'parameters': {
                    'road_condition': '道路状况评分',
                    'gradient_safety': '坡度安全性',
                    'weather_impact': '天气影响',
                    'traffic_safety': '交通安全'
                },
                'evaluation': {
                    'excellent': '安全评分 >= 90',
                    'good': '安全评分 >= 80',
                    'acceptable': '安全评分 >= 70',
                    'poor': '安全评分 < 70'
                }
            },
            'efficiency_metrics': {
                'description': '效率评估指标',
                'parameters': {
                    'route_efficiency': '路线效率',
                    'vehicle_utilization': '车辆利用率',
                    'time_efficiency': '时间效率',
                    'resource_optimization': '资源优化'
                }
            }
        }
        
        return criteria
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取优化标准失败: {str(e)}")
