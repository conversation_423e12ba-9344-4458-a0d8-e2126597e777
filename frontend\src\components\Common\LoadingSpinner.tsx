import React from 'react'
import { Spin } from 'antd'
import { LoadingOutlined } from '@ant-design/icons'

interface LoadingSpinnerProps {
  size?: 'small' | 'default' | 'large'
  tip?: string
  spinning?: boolean
  children?: React.ReactNode
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'default',
  tip = '加载中...',
  spinning = true,
  children
}) => {
  const antIcon = <LoadingOutlined style={{ fontSize: size === 'large' ? 24 : 16 }} spin />

  if (children) {
    return (
      <Spin indicator={antIcon} spinning={spinning} tip={tip} size={size}>
        {children}
      </Spin>
    )
  }

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      padding: '20px'
    }}>
      <Spin indicator={antIcon} size={size} />
      {tip && (
        <div style={{
          marginTop: '12px',
          color: '#666',
          fontSize: '14px'
        }}>
          {tip}
        </div>
      )}
    </div>
  )
}

export default LoadingSpinner
