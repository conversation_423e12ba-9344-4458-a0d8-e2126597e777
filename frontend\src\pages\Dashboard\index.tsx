import React from 'react'
import { Row, Col, Card, Statistic, Progress, List, Avatar, Button, Tag } from 'antd'
import {
  ProjectOutlined,
  CarOutlined,
  WarningOutlined,
  SafetyOutlined,
  ArrowUpOutlined,
  ArrowDownOutlined,
  EyeOutlined,
  EditOutlined
} from '@ant-design/icons'

const Dashboard: React.FC = () => {
  // 模拟数据
  const stats = [
    {
      title: '总项目数',
      value: 12,
      prefix: <ProjectOutlined />,
      valueStyle: { color: '#3f8600' },
      suffix: <ArrowUpOutlined style={{ color: '#3f8600' }} />
    },
    {
      title: '设计道路',
      value: 45,
      prefix: <CarOutlined />,
      valueStyle: { color: '#1890ff' },
      suffix: '条'
    },
    {
      title: '检测冲突',
      value: 8,
      prefix: <WarningOutlined />,
      valueStyle: { color: '#cf1322' },
      suffix: <ArrowDownOutlined style={{ color: '#cf1322' }} />
    },
    {
      title: '安全评分',
      value: 92.5,
      prefix: <SafetyOutlined />,
      valueStyle: { color: '#faad14' },
      suffix: '分'
    }
  ]

  const recentProjects = [
    {
      id: 1,
      name: '东山露天矿道路设计',
      status: 'active',
      progress: 75,
      lastModified: '2024-01-15'
    },
    {
      id: 2,
      name: '西部矿区运输路线优化',
      status: 'completed',
      progress: 100,
      lastModified: '2024-01-14'
    },
    {
      id: 3,
      name: '南山采矿区道路安全分析',
      status: 'pending',
      progress: 30,
      lastModified: '2024-01-13'
    },
    {
      id: 4,
      name: '北区新建道路冲突检测',
      status: 'active',
      progress: 60,
      lastModified: '2024-01-12'
    }
  ]

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'processing'
      case 'completed': return 'success'
      case 'pending': return 'warning'
      default: return 'default'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'active': return '进行中'
      case 'completed': return '已完成'
      case 'pending': return '待开始'
      default: return '未知'
    }
  }

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          仪表板
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          露天矿山道路设计系统概览
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        {stats.map((stat, index) => (
          <Col xs={24} sm={12} lg={6} key={index}>
            <Card>
              <Statistic
                title={stat.title}
                value={stat.value}
                prefix={stat.prefix}
                suffix={stat.suffix}
                valueStyle={stat.valueStyle}
              />
            </Card>
          </Col>
        ))}
      </Row>

      <Row gutter={[16, 16]}>
        {/* 最近项目 */}
        <Col xs={24} lg={16}>
          <Card 
            title="最近项目" 
            extra={<Button type="link">查看全部</Button>}
          >
            <List
              itemLayout="horizontal"
              dataSource={recentProjects}
              renderItem={(item) => (
                <List.Item
                  actions={[
                    <Button type="text" icon={<EyeOutlined />} key="view">
                      查看
                    </Button>,
                    <Button type="text" icon={<EditOutlined />} key="edit">
                      编辑
                    </Button>
                  ]}
                >
                  <List.Item.Meta
                    avatar={
                      <Avatar 
                        style={{ backgroundColor: '#faad14' }}
                        icon={<ProjectOutlined />}
                      />
                    }
                    title={
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <span>{item.name}</span>
                        <Tag color={getStatusColor(item.status)}>
                          {getStatusText(item.status)}
                        </Tag>
                      </div>
                    }
                    description={
                      <div>
                        <div style={{ marginBottom: '8px' }}>
                          最后修改: {item.lastModified}
                        </div>
                        <Progress 
                          percent={item.progress} 
                          size="small" 
                          status={item.status === 'completed' ? 'success' : 'active'}
                        />
                      </div>
                    }
                  />
                </List.Item>
              )}
            />
          </Card>
        </Col>

        {/* 系统状态 */}
        <Col xs={24} lg={8}>
          <Card title="系统状态" style={{ marginBottom: '16px' }}>
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>CPU使用率</span>
                <span>45%</span>
              </div>
              <Progress percent={45} strokeColor="#52c41a" />
            </div>
            
            <div style={{ marginBottom: '16px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>内存使用率</span>
                <span>68%</span>
              </div>
              <Progress percent={68} strokeColor="#faad14" />
            </div>
            
            <div>
              <div style={{ display: 'flex', justifyContent: 'space-between', marginBottom: '8px' }}>
                <span>存储使用率</span>
                <span>32%</span>
              </div>
              <Progress percent={32} strokeColor="#1890ff" />
            </div>
          </Card>

          {/* 快速操作 */}
          <Card title="快速操作">
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
              <Button type="primary" block icon={<ProjectOutlined />}>
                新建项目
              </Button>
              <Button block icon={<CarOutlined />}>
                道路设计
              </Button>
              <Button block icon={<WarningOutlined />}>
                冲突检测
              </Button>
              <Button block icon={<SafetyOutlined />}>
                安全分析
              </Button>
            </div>
          </Card>
        </Col>
      </Row>
    </div>
  )
}

export default Dashboard
