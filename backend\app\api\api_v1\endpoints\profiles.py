"""
剖切分析API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from pydantic import BaseModel

from app.core.database import get_db
from app.services.profile_service import ProfileService
from app.models.profile_analysis import ProfileAnalysis

router = APIRouter()
profile_service = ProfileService()


class ProfileAnalysisParams(BaseModel):
    """剖面分析参数"""
    analysis_type: str = "comprehensive"
    include_longitudinal: bool = True
    include_cross_sections: bool = True
    include_volumes: bool = True
    cross_section_interval: float = 20.0
    analysis_interval: float = 10.0


class ProfileAnalysisResponse(BaseModel):
    """剖面分析响应模型"""
    id: int
    road_id: int
    analysis_type: str
    total_length: float
    max_gradient: Optional[float]
    min_gradient: Optional[float]
    average_gradient: Optional[float]
    total_cut_volume: Optional[float]
    total_fill_volume: Optional[float]
    net_volume: Optional[float]
    balance_ratio: Optional[float]
    cross_sections_count: int
    analyzed_at: str
    
    class Config:
        from_attributes = True


@router.post("/{project_id}/roads/{road_id}/analyze")
async def analyze_road_profile(
    project_id: int,
    road_id: int,
    analysis_params: ProfileAnalysisParams,
    db: Session = Depends(get_db)
):
    """分析道路剖面"""
    try:
        profile_analysis = await profile_service.analyze_road_profile(
            db=db,
            road_id=road_id,
            analysis_params=analysis_params.dict()
        )
        
        return ProfileAnalysisResponse(
            id=profile_analysis.id,
            road_id=profile_analysis.road_id,
            analysis_type=profile_analysis.analysis_type,
            total_length=profile_analysis.total_length,
            max_gradient=profile_analysis.max_gradient,
            min_gradient=profile_analysis.min_gradient,
            average_gradient=profile_analysis.average_gradient,
            total_cut_volume=profile_analysis.total_cut_volume,
            total_fill_volume=profile_analysis.total_fill_volume,
            net_volume=profile_analysis.net_volume,
            balance_ratio=profile_analysis.balance_ratio,
            cross_sections_count=profile_analysis.cross_sections_count,
            analyzed_at=profile_analysis.analyzed_at.isoformat()
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"剖面分析失败: {str(e)}")


@router.get("/{project_id}/profile-analyses", response_model=List[ProfileAnalysisResponse])
async def get_project_profile_analyses(
    project_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取项目剖面分析列表"""
    try:
        analyses = profile_service.get_project_profile_analyses(
            db=db,
            project_id=project_id,
            skip=skip,
            limit=limit
        )
        
        return [
            ProfileAnalysisResponse(
                id=analysis.id,
                road_id=analysis.road_id,
                analysis_type=analysis.analysis_type,
                total_length=analysis.total_length,
                max_gradient=analysis.max_gradient,
                min_gradient=analysis.min_gradient,
                average_gradient=analysis.average_gradient,
                total_cut_volume=analysis.total_cut_volume,
                total_fill_volume=analysis.total_fill_volume,
                net_volume=analysis.net_volume,
                balance_ratio=analysis.balance_ratio,
                cross_sections_count=analysis.cross_sections_count,
                analyzed_at=analysis.analyzed_at.isoformat()
            )
            for analysis in analyses
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取剖面分析列表失败: {str(e)}")


@router.get("/analyses/{analysis_id}", response_model=ProfileAnalysisResponse)
async def get_profile_analysis_detail(
    analysis_id: int,
    db: Session = Depends(get_db)
):
    """获取剖面分析详情"""
    try:
        analysis = profile_service.get_profile_analysis_detail(db=db, analysis_id=analysis_id)
        
        if not analysis:
            raise HTTPException(status_code=404, detail="剖面分析不存在")
        
        return ProfileAnalysisResponse(
            id=analysis.id,
            road_id=analysis.road_id,
            analysis_type=analysis.analysis_type,
            total_length=analysis.total_length,
            max_gradient=analysis.max_gradient,
            min_gradient=analysis.min_gradient,
            average_gradient=analysis.average_gradient,
            total_cut_volume=analysis.total_cut_volume,
            total_fill_volume=analysis.total_fill_volume,
            net_volume=analysis.net_volume,
            balance_ratio=analysis.balance_ratio,
            cross_sections_count=analysis.cross_sections_count,
            analyzed_at=analysis.analyzed_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取剖面分析详情失败: {str(e)}")


@router.get("/{project_id}/roads/{road_id}/longitudinal-profile")
async def get_longitudinal_profile(
    project_id: int,
    road_id: int,
    db: Session = Depends(get_db)
):
    """获取纵断面"""
    try:
        longitudinal_data = await profile_service.analyze_longitudinal_profile(
            db=db,
            road_id=road_id
        )
        
        return longitudinal_data
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取纵断面失败: {str(e)}")


@router.get("/{project_id}/roads/{road_id}/cross-section")
async def get_cross_section(
    project_id: int,
    road_id: int,
    chainage: float = Query(..., description="里程桩号"),
    db: Session = Depends(get_db)
):
    """获取横断面"""
    try:
        cross_section_data = await profile_service.get_cross_section(
            db=db,
            road_id=road_id,
            chainage=chainage
        )
        
        return cross_section_data
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取横断面失败: {str(e)}")


@router.get("/{project_id}/roads/{road_id}/earthwork-volumes")
async def calculate_earthwork_volumes(
    project_id: int,
    road_id: int,
    start_chainage: Optional[float] = Query(None, description="起始里程"),
    end_chainage: Optional[float] = Query(None, description="结束里程"),
    db: Session = Depends(get_db)
):
    """计算土方量"""
    try:
        volume_data = await profile_service.calculate_earthwork_volumes(
            db=db,
            road_id=road_id,
            start_chainage=start_chainage,
            end_chainage=end_chainage
        )
        
        return volume_data
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"计算土方量失败: {str(e)}")


@router.get("/{project_id}/profile-statistics")
async def get_profile_statistics(
    project_id: int,
    db: Session = Depends(get_db)
):
    """获取剖面统计信息"""
    try:
        statistics = profile_service.get_profile_statistics(db=db, project_id=project_id)
        
        return {
            "project_id": project_id,
            "statistics": statistics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取剖面统计失败: {str(e)}")


@router.post("/{project_id}/batch-analyze")
async def batch_analyze_profiles(
    project_id: int,
    analysis_params: ProfileAnalysisParams,
    road_ids: Optional[List[int]] = None,
    db: Session = Depends(get_db)
):
    """批量剖面分析"""
    try:
        # 获取要分析的道路
        from app.models.road import Road
        
        if road_ids:
            roads = db.query(Road).filter(
                Road.project_id == project_id,
                Road.id.in_(road_ids)
            ).all()
        else:
            roads = db.query(Road).filter(Road.project_id == project_id).all()
        
        if not roads:
            raise HTTPException(status_code=404, detail="未找到要分析的道路")
        
        # 批量执行剖面分析
        results = []
        for road in roads:
            try:
                profile_analysis = await profile_service.analyze_road_profile(
                    db=db,
                    road_id=road.id,
                    analysis_params=analysis_params.dict()
                )
                
                results.append({
                    'road_id': road.id,
                    'road_name': road.name,
                    'analysis_id': profile_analysis.id,
                    'total_length': profile_analysis.total_length,
                    'cut_volume': profile_analysis.total_cut_volume,
                    'fill_volume': profile_analysis.total_fill_volume,
                    'balance_ratio': profile_analysis.balance_ratio,
                    'status': 'success'
                })
                
            except Exception as e:
                results.append({
                    'road_id': road.id,
                    'road_name': road.name,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'project_id': project_id,
            'total_roads': len(roads),
            'successful_analyses': len([r for r in results if r['status'] == 'success']),
            'failed_analyses': len([r for r in results if r['status'] == 'failed']),
            'results': results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量剖面分析失败: {str(e)}")


@router.get("/{project_id}/roads/{road_id}/profile-chart")
async def get_profile_chart_data(
    project_id: int,
    road_id: int,
    chart_type: str = Query("longitudinal", description="图表类型: longitudinal, cross_section, volume"),
    chainage: Optional[float] = Query(None, description="横断面里程(仅chart_type=cross_section时需要)"),
    db: Session = Depends(get_db)
):
    """获取剖面图表数据"""
    try:
        if chart_type == "longitudinal":
            # 纵断面图表数据
            longitudinal_data = await profile_service.analyze_longitudinal_profile(
                db=db,
                road_id=road_id
            )
            return longitudinal_data['chart_data']
            
        elif chart_type == "cross_section":
            # 横断面图表数据
            if chainage is None:
                raise HTTPException(status_code=400, detail="横断面图表需要指定里程")
            
            cross_section_data = await profile_service.get_cross_section(
                db=db,
                road_id=road_id,
                chainage=chainage
            )
            return cross_section_data['cross_section_data']
            
        elif chart_type == "volume":
            # 土方量图表数据
            volume_data = await profile_service.calculate_earthwork_volumes(
                db=db,
                road_id=road_id
            )
            return {
                'volume_distribution': volume_data['volume_distribution'],
                'cut_sections': volume_data['cut_sections'],
                'fill_sections': volume_data['fill_sections']
            }
            
        else:
            raise HTTPException(status_code=400, detail="不支持的图表类型")
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图表数据失败: {str(e)}")


@router.get("/standards/profile-criteria")
async def get_profile_criteria():
    """获取剖面设计标准和评判标准"""
    try:
        criteria = {
            'longitudinal_profile': {
                'description': '纵断面设计标准',
                'parameters': {
                    'max_gradient': '最大坡度(8%)',
                    'min_curve_radius': '最小竖曲线半径(200米)',
                    'max_steep_length': '最大连续陡坡长度(500米)'
                },
                'evaluation': {
                    'excellent': '坡度 <= 6%, 曲线半径 >= 300米',
                    'good': '坡度 <= 8%, 曲线半径 >= 200米',
                    'acceptable': '坡度 <= 10%, 曲线半径 >= 150米',
                    'poor': '超出设计标准'
                }
            },
            'cross_section': {
                'description': '横断面设计标准',
                'parameters': {
                    'road_width': '道路宽度(6-12米)',
                    'side_slope': '边坡坡度(1:1.5)',
                    'drainage_slope': '排水坡度(2%)'
                },
                'evaluation': {
                    'stable': '边坡稳定，排水良好',
                    'caution': '需要注意边坡稳定性',
                    'unstable': '边坡不稳定，需要支护'
                }
            },
            'earthwork': {
                'description': '土方工程标准',
                'parameters': {
                    'balance_ratio': '土方平衡比例(>80%)',
                    'expansion_factor': '土方松散系数(1.2)',
                    'compaction_factor': '土方压实系数(0.9)'
                },
                'evaluation': {
                    'balanced': '平衡比例 >= 80%',
                    'acceptable': '平衡比例 >= 60%',
                    'unbalanced': '平衡比例 < 60%'
                }
            }
        }
        
        return criteria
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取剖面标准失败: {str(e)}")
