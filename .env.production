# 露天矿山道路设计软件 - 生产环境配置
# 请根据实际部署环境修改以下配置

# =============================================================================
# 基本配置
# =============================================================================
ENVIRONMENT=production
DOMAIN=your-domain.com
DEBUG=false
ALLOWED_HOSTS=your-domain.com,www.your-domain.com
CORS_ORIGINS=https://your-domain.com,https://www.your-domain.com

# =============================================================================
# 安全配置
# =============================================================================
SECRET_KEY=your-very-long-and-random-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
ENCRYPTION_SALT=your-encryption-salt-change-this-in-production

# =============================================================================
# 数据库配置
# =============================================================================
POSTGRES_PASSWORD=your-strong-postgres-password
DATABASE_URL=postgresql://postgres:${POSTGRES_PASSWORD}@postgres:5432/mining_road_design
DB_POOL_SIZE=20
DB_MAX_OVERFLOW=30
DB_POOL_TIMEOUT=30

# =============================================================================
# Redis配置
# =============================================================================
REDIS_PASSWORD=your-strong-redis-password
REDIS_URL=redis://:${REDIS_PASSWORD}@redis:6379/0
REDIS_POOL_SIZE=10
REDIS_POOL_TIMEOUT=5

# =============================================================================
# 文件存储配置
# =============================================================================
UPLOAD_DIR=/app/uploads
MAX_UPLOAD_SIZE=104857600
ALLOWED_FILE_TYPES=tif,tiff,dem,asc,xyz,dwg,dxf,shp,kml,kmz
STORAGE_TYPE=local

# =============================================================================
# 外部服务配置
# =============================================================================
CESIUM_ION_ACCESS_TOKEN=your-cesium-ion-access-token
MAP_SERVICE_TYPE=osm
MAP_SERVICE_API_KEY=your-map-service-api-key

# =============================================================================
# 邮件服务配置
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
SMTP_TLS=true

# =============================================================================
# 监控配置
# =============================================================================
GRAFANA_USER=admin
GRAFANA_PASSWORD=your-strong-grafana-password
GRAFANA_SECRET_KEY=your-grafana-secret-key
PROMETHEUS_RETENTION_TIME=200h
LOG_LEVEL=INFO
LOG_FILE=/app/logs/app.log

# =============================================================================
# 性能配置
# =============================================================================
WORKERS=4
MAX_CONNECTIONS=100
REQUEST_TIMEOUT=30
DB_QUERY_TIMEOUT=30
CACHE_EXPIRE_TIME=3600

# =============================================================================
# SSL证书配置
# =============================================================================
SSL_EMAIL=<EMAIL>
SSL_CERT_PATH=/etc/nginx/ssl/fullchain.pem
SSL_KEY_PATH=/etc/nginx/ssl/privkey.pem

# =============================================================================
# 备份配置
# =============================================================================
BACKUP_RETENTION_DAYS=30
BACKUP_SCHEDULE=0 2 * * *
BACKUP_S3_BUCKET=your-backup-s3-bucket
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# =============================================================================
# 功能开关
# =============================================================================
FEATURE_3D_VISUALIZATION=true
FEATURE_AUTOCAD_INTEGRATION=true
FEATURE_ROUTE_OPTIMIZATION=true
FEATURE_SAFETY_ANALYSIS=true

# =============================================================================
# 算法配置
# =============================================================================
ALGORITHM_TIMEOUT=300
ALGORITHM_MAX_ITERATIONS=1000
MAX_CONCURRENT_CALCULATIONS=5
MAX_CONCURRENT_UPLOADS=10
MEMORY_LIMIT=4096
TEMP_FILE_CLEANUP_INTERVAL=6
