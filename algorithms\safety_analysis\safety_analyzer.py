"""
道路安全分析核心算法
"""
import numpy as np
import math
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class SafetyLevel(Enum):
    """安全等级枚举"""
    SAFE = "safe"
    CAUTION = "caution"
    WARNING = "warning"
    DANGER = "danger"


class SafetyIssueType(Enum):
    """安全问题类型枚举"""
    SIGHT_DISTANCE = "sight_distance"
    SLOPE_STABILITY = "slope_stability"
    DRAINAGE_ISSUE = "drainage_issue"
    GEOMETRIC_HAZARD = "geometric_hazard"
    TRAFFIC_SAFETY = "traffic_safety"
    STRUCTURAL_SAFETY = "structural_safety"


@dataclass
class SafetyIssue:
    """安全问题"""
    issue_id: str
    issue_type: SafetyIssueType
    severity_level: SafetyLevel
    location: Tuple[float, float, float]  # (x, y, z)
    chainage: float
    affected_length: float
    description: str
    measured_values: Dict[str, float]
    threshold_values: Dict[str, float]
    deviation: float
    risk_score: float
    recommendations: List[Dict[str, Any]]
    urgency_level: int  # 1-5


@dataclass
class RoadGeometry:
    """道路几何数据"""
    points: List[Tuple[float, float, float]]
    chainages: List[float]
    gradients: List[float]
    curvatures: List[float]
    widths: List[float]
    design_speed: float


@dataclass
class SafetyAnalysisResult:
    """安全分析结果"""
    overall_safety_score: float  # 0-100
    safety_level: SafetyLevel
    total_issues: int
    critical_issues: int
    warning_issues: int
    caution_issues: int
    issues: List[SafetyIssue]
    recommendations: List[str]
    analysis_summary: Dict[str, Any]


class SafetyAnalyzer:
    """安全分析器主类"""
    
    def __init__(self):
        self.safety_standards = {
            'min_sight_distance': 50.0,      # 最小视距(米)
            'max_gradient': 8.0,             # 最大坡度(%)
            'min_radius': 15.0,              # 最小转弯半径(米)
            'max_super_elevation': 6.0,      # 最大超高(%)
            'min_lane_width': 3.0,           # 最小车道宽度(米)
            'max_cut_height': 20.0,          # 最大挖方高度(米)
            'min_drainage_slope': 0.5,       # 最小排水坡度(%)
            'safety_clearance': 0.5,         # 安全净空(米)
        }
        
        self.risk_weights = {
            'sight_distance': 0.25,
            'slope_stability': 0.20,
            'geometric_safety': 0.20,
            'drainage': 0.15,
            'traffic_safety': 0.15,
            'structural_safety': 0.05
        }
    
    def analyze_road_safety(self, 
                           road_geometry: RoadGeometry,
                           terrain_data: np.ndarray = None,
                           traffic_data: Dict = None) -> SafetyAnalysisResult:
        """分析道路安全性"""
        try:
            logger.info("开始道路安全分析")
            
            all_issues = []
            
            # 1. 视距分析
            sight_issues = self._analyze_sight_distance(road_geometry)
            all_issues.extend(sight_issues)
            
            # 2. 边坡稳定性分析
            if terrain_data is not None:
                slope_issues = self._analyze_slope_stability(road_geometry, terrain_data)
                all_issues.extend(slope_issues)
            
            # 3. 几何安全分析
            geometric_issues = self._analyze_geometric_safety(road_geometry)
            all_issues.extend(geometric_issues)
            
            # 4. 排水安全分析
            drainage_issues = self._analyze_drainage_safety(road_geometry)
            all_issues.extend(drainage_issues)
            
            # 5. 交通安全分析
            if traffic_data:
                traffic_issues = self._analyze_traffic_safety(road_geometry, traffic_data)
                all_issues.extend(traffic_issues)
            
            # 6. 结构安全分析
            structural_issues = self._analyze_structural_safety(road_geometry)
            all_issues.extend(structural_issues)
            
            # 计算总体安全评分
            overall_score = self._calculate_overall_safety_score(all_issues)
            safety_level = self._determine_safety_level(overall_score)
            
            # 统计问题数量
            critical_count = len([i for i in all_issues if i.severity_level == SafetyLevel.DANGER])
            warning_count = len([i for i in all_issues if i.severity_level == SafetyLevel.WARNING])
            caution_count = len([i for i in all_issues if i.severity_level == SafetyLevel.CAUTION])
            
            # 生成建议
            recommendations = self._generate_recommendations(all_issues)
            
            # 生成分析摘要
            analysis_summary = self._generate_analysis_summary(all_issues, road_geometry)
            
            result = SafetyAnalysisResult(
                overall_safety_score=overall_score,
                safety_level=safety_level,
                total_issues=len(all_issues),
                critical_issues=critical_count,
                warning_issues=warning_count,
                caution_issues=caution_count,
                issues=all_issues,
                recommendations=recommendations,
                analysis_summary=analysis_summary
            )
            
            logger.info(f"安全分析完成，发现 {len(all_issues)} 个安全问题")
            return result
            
        except Exception as e:
            logger.error(f"安全分析失败: {str(e)}")
            raise
    
    def _analyze_sight_distance(self, road_geometry: RoadGeometry) -> List[SafetyIssue]:
        """分析视距安全"""
        issues = []
        
        try:
            for i in range(len(road_geometry.points) - 1):
                chainage = road_geometry.chainages[i]
                point = road_geometry.points[i]
                
                # 计算停车视距
                stopping_sight_distance = self._calculate_stopping_sight_distance(
                    road_geometry.design_speed,
                    road_geometry.gradients[i] if i < len(road_geometry.gradients) else 0
                )
                
                # 计算实际视距
                actual_sight_distance = self._calculate_actual_sight_distance(
                    road_geometry, i
                )
                
                # 检查视距是否足够
                if actual_sight_distance < stopping_sight_distance:
                    deviation = (stopping_sight_distance - actual_sight_distance) / stopping_sight_distance
                    
                    # 确定严重程度
                    if deviation > 0.5:
                        severity = SafetyLevel.DANGER
                        urgency = 5
                    elif deviation > 0.3:
                        severity = SafetyLevel.WARNING
                        urgency = 4
                    elif deviation > 0.1:
                        severity = SafetyLevel.CAUTION
                        urgency = 3
                    else:
                        severity = SafetyLevel.CAUTION
                        urgency = 2
                    
                    risk_score = deviation * 100
                    
                    issue = SafetyIssue(
                        issue_id=f"sight_{i}",
                        issue_type=SafetyIssueType.SIGHT_DISTANCE,
                        severity_level=severity,
                        location=point,
                        chainage=chainage,
                        affected_length=50.0,  # 影响长度
                        description=f"里程{chainage:.1f}m处视距不足",
                        measured_values={
                            'actual_sight_distance': actual_sight_distance,
                            'design_speed': road_geometry.design_speed
                        },
                        threshold_values={
                            'required_sight_distance': stopping_sight_distance,
                            'min_sight_distance': self.safety_standards['min_sight_distance']
                        },
                        deviation=deviation,
                        risk_score=risk_score,
                        recommendations=[
                            {
                                'action': 'cut_slope',
                                'description': '削减边坡改善视距',
                                'cost_factor': 0.6,
                                'effectiveness': 0.8
                            },
                            {
                                'action': 'warning_signs',
                                'description': '设置视距不良警示标志',
                                'cost_factor': 0.1,
                                'effectiveness': 0.4
                            }
                        ],
                        urgency_level=urgency
                    )
                    
                    issues.append(issue)
            
            return issues
            
        except Exception as e:
            logger.error(f"视距分析失败: {str(e)}")
            return []
    
    def _analyze_slope_stability(self, 
                               road_geometry: RoadGeometry,
                               terrain_data: np.ndarray) -> List[SafetyIssue]:
        """分析边坡稳定性"""
        issues = []
        
        try:
            for i, point in enumerate(road_geometry.points):
                chainage = road_geometry.chainages[i]
                
                # 获取地形高程
                terrain_elevation = self._get_terrain_elevation(point[0], point[1], terrain_data)
                if terrain_elevation is None:
                    continue
                
                # 计算挖填高度
                cut_fill_height = abs(point[2] - terrain_elevation)
                
                # 检查是否超过安全高度
                if cut_fill_height > self.safety_standards['max_cut_height']:
                    # 计算边坡稳定性
                    stability_factor = self._calculate_slope_stability_factor(
                        cut_fill_height, point[2] > terrain_elevation
                    )
                    
                    if stability_factor < 1.5:  # 安全系数小于1.5
                        severity = SafetyLevel.DANGER if stability_factor < 1.2 else SafetyLevel.WARNING
                        risk_score = (1.5 - stability_factor) / 1.5 * 100
                        
                        issue = SafetyIssue(
                            issue_id=f"slope_{i}",
                            issue_type=SafetyIssueType.SLOPE_STABILITY,
                            severity_level=severity,
                            location=point,
                            chainage=chainage,
                            affected_length=30.0,
                            description=f"里程{chainage:.1f}m处边坡稳定性不足",
                            measured_values={
                                'cut_fill_height': cut_fill_height,
                                'stability_factor': stability_factor
                            },
                            threshold_values={
                                'max_cut_height': self.safety_standards['max_cut_height'],
                                'min_stability_factor': 1.5
                            },
                            deviation=(self.safety_standards['max_cut_height'] - cut_fill_height) / self.safety_standards['max_cut_height'],
                            risk_score=risk_score,
                            recommendations=[
                                {
                                    'action': 'retaining_wall',
                                    'description': '设置挡土墙',
                                    'cost_factor': 1.2,
                                    'effectiveness': 0.9
                                },
                                {
                                    'action': 'slope_reduction',
                                    'description': '减缓边坡坡度',
                                    'cost_factor': 0.8,
                                    'effectiveness': 0.7
                                }
                            ],
                            urgency_level=5 if severity == SafetyLevel.DANGER else 4
                        )
                        
                        issues.append(issue)
            
            return issues
            
        except Exception as e:
            logger.error(f"边坡稳定性分析失败: {str(e)}")
            return []
    
    def _analyze_geometric_safety(self, road_geometry: RoadGeometry) -> List[SafetyIssue]:
        """分析几何安全性"""
        issues = []
        
        try:
            # 检查坡度安全
            for i, gradient in enumerate(road_geometry.gradients):
                if abs(gradient) > self.safety_standards['max_gradient']:
                    chainage = road_geometry.chainages[i]
                    point = road_geometry.points[i]
                    
                    deviation = (abs(gradient) - self.safety_standards['max_gradient']) / self.safety_standards['max_gradient']
                    severity = SafetyLevel.DANGER if deviation > 0.5 else SafetyLevel.WARNING
                    
                    issue = SafetyIssue(
                        issue_id=f"gradient_{i}",
                        issue_type=SafetyIssueType.GEOMETRIC_HAZARD,
                        severity_level=severity,
                        location=point,
                        chainage=chainage,
                        affected_length=20.0,
                        description=f"里程{chainage:.1f}m处坡度过陡({gradient:.1f}%)",
                        measured_values={'gradient': gradient},
                        threshold_values={'max_gradient': self.safety_standards['max_gradient']},
                        deviation=deviation,
                        risk_score=deviation * 100,
                        recommendations=[
                            {
                                'action': 'grade_reduction',
                                'description': '降低道路坡度',
                                'cost_factor': 0.7,
                                'effectiveness': 0.9
                            }
                        ],
                        urgency_level=4
                    )
                    
                    issues.append(issue)
            
            # 检查转弯半径安全
            for i, curvature in enumerate(road_geometry.curvatures):
                if curvature > 0:
                    radius = 1.0 / curvature
                    if radius < self.safety_standards['min_radius']:
                        chainage = road_geometry.chainages[i]
                        point = road_geometry.points[i]
                        
                        deviation = (self.safety_standards['min_radius'] - radius) / self.safety_standards['min_radius']
                        severity = SafetyLevel.WARNING if deviation > 0.3 else SafetyLevel.CAUTION
                        
                        issue = SafetyIssue(
                            issue_id=f"radius_{i}",
                            issue_type=SafetyIssueType.GEOMETRIC_HAZARD,
                            severity_level=severity,
                            location=point,
                            chainage=chainage,
                            affected_length=radius * 2,
                            description=f"里程{chainage:.1f}m处转弯半径过小({radius:.1f}m)",
                            measured_values={'radius': radius},
                            threshold_values={'min_radius': self.safety_standards['min_radius']},
                            deviation=deviation,
                            risk_score=deviation * 80,
                            recommendations=[
                                {
                                    'action': 'increase_radius',
                                    'description': '增大转弯半径',
                                    'cost_factor': 0.8,
                                    'effectiveness': 0.9
                                }
                            ],
                            urgency_level=3
                        )
                        
                        issues.append(issue)
            
            return issues
            
        except Exception as e:
            logger.error(f"几何安全分析失败: {str(e)}")
            return []
    
    def _analyze_drainage_safety(self, road_geometry: RoadGeometry) -> List[SafetyIssue]:
        """分析排水安全性"""
        issues = []
        
        try:
            for i, gradient in enumerate(road_geometry.gradients):
                if abs(gradient) < self.safety_standards['min_drainage_slope']:
                    chainage = road_geometry.chainages[i]
                    point = road_geometry.points[i]
                    
                    issue = SafetyIssue(
                        issue_id=f"drainage_{i}",
                        issue_type=SafetyIssueType.DRAINAGE_ISSUE,
                        severity_level=SafetyLevel.CAUTION,
                        location=point,
                        chainage=chainage,
                        affected_length=15.0,
                        description=f"里程{chainage:.1f}m处排水坡度不足",
                        measured_values={'gradient': gradient},
                        threshold_values={'min_drainage_slope': self.safety_standards['min_drainage_slope']},
                        deviation=(self.safety_standards['min_drainage_slope'] - abs(gradient)) / self.safety_standards['min_drainage_slope'],
                        risk_score=30.0,
                        recommendations=[
                            {
                                'action': 'drainage_system',
                                'description': '增设排水设施',
                                'cost_factor': 0.3,
                                'effectiveness': 0.8
                            }
                        ],
                        urgency_level=2
                    )
                    
                    issues.append(issue)
            
            return issues
            
        except Exception as e:
            logger.error(f"排水安全分析失败: {str(e)}")
            return []
    
    def _analyze_traffic_safety(self, 
                              road_geometry: RoadGeometry,
                              traffic_data: Dict) -> List[SafetyIssue]:
        """分析交通安全性"""
        issues = []
        
        try:
            # 检查车道宽度
            for i, width in enumerate(road_geometry.widths):
                lane_width = width / 2  # 假设双车道
                
                if lane_width < self.safety_standards['min_lane_width']:
                    chainage = road_geometry.chainages[i]
                    point = road_geometry.points[i]
                    
                    issue = SafetyIssue(
                        issue_id=f"lane_width_{i}",
                        issue_type=SafetyIssueType.TRAFFIC_SAFETY,
                        severity_level=SafetyLevel.WARNING,
                        location=point,
                        chainage=chainage,
                        affected_length=10.0,
                        description=f"里程{chainage:.1f}m处车道宽度不足",
                        measured_values={'lane_width': lane_width},
                        threshold_values={'min_lane_width': self.safety_standards['min_lane_width']},
                        deviation=(self.safety_standards['min_lane_width'] - lane_width) / self.safety_standards['min_lane_width'],
                        risk_score=50.0,
                        recommendations=[
                            {
                                'action': 'widen_road',
                                'description': '拓宽道路',
                                'cost_factor': 0.6,
                                'effectiveness': 0.9
                            }
                        ],
                        urgency_level=3
                    )
                    
                    issues.append(issue)
            
            return issues
            
        except Exception as e:
            logger.error(f"交通安全分析失败: {str(e)}")
            return []
    
    def _analyze_structural_safety(self, road_geometry: RoadGeometry) -> List[SafetyIssue]:
        """分析结构安全性"""
        issues = []
        
        try:
            # 这里可以添加路面结构、桥梁等结构安全分析
            # 目前返回空列表
            return issues
            
        except Exception as e:
            logger.error(f"结构安全分析失败: {str(e)}")
            return []
    
    def _calculate_stopping_sight_distance(self, design_speed: float, gradient: float) -> float:
        """计算停车视距"""
        # 停车视距公式: S = V*t + V²/(254(f±i))
        reaction_time = 2.5  # 反应时间(秒)
        friction_coeff = 0.35  # 摩擦系数
        grade_factor = gradient / 100  # 坡度系数
        
        sight_distance = (design_speed * reaction_time / 3.6 + 
                         design_speed ** 2 / (254 * (friction_coeff + grade_factor)))
        
        return max(sight_distance, self.safety_standards['min_sight_distance'])
    
    def _calculate_actual_sight_distance(self, road_geometry: RoadGeometry, index: int) -> float:
        """计算实际视距"""
        # 简化计算，实际应该考虑地形遮挡
        return 60.0  # 模拟值
    
    def _get_terrain_elevation(self, x: float, y: float, terrain_data: np.ndarray) -> Optional[float]:
        """获取地形高程"""
        # 简化实现
        return 100.0  # 模拟值
    
    def _calculate_slope_stability_factor(self, height: float, is_cut: bool) -> float:
        """计算边坡稳定性系数"""
        # 简化的稳定性计算
        if is_cut:
            return max(0.8, 2.0 - height / 30.0)
        else:
            return max(1.0, 2.5 - height / 25.0)
    
    def _calculate_overall_safety_score(self, issues: List[SafetyIssue]) -> float:
        """计算总体安全评分"""
        if not issues:
            return 100.0
        
        total_deduction = 0
        for issue in issues:
            if issue.severity_level == SafetyLevel.DANGER:
                total_deduction += 25
            elif issue.severity_level == SafetyLevel.WARNING:
                total_deduction += 15
            elif issue.severity_level == SafetyLevel.CAUTION:
                total_deduction += 8
        
        return max(0, 100 - total_deduction)
    
    def _determine_safety_level(self, score: float) -> SafetyLevel:
        """确定安全等级"""
        if score >= 90:
            return SafetyLevel.SAFE
        elif score >= 70:
            return SafetyLevel.CAUTION
        elif score >= 50:
            return SafetyLevel.WARNING
        else:
            return SafetyLevel.DANGER
    
    def _generate_recommendations(self, issues: List[SafetyIssue]) -> List[str]:
        """生成安全建议"""
        recommendations = []
        
        critical_count = len([i for i in issues if i.severity_level == SafetyLevel.DANGER])
        if critical_count > 0:
            recommendations.append(f"发现{critical_count}个严重安全问题，建议立即处理")
        
        sight_issues = len([i for i in issues if i.issue_type == SafetyIssueType.SIGHT_DISTANCE])
        if sight_issues > 3:
            recommendations.append("视距问题较多，建议系统性改善道路线形")
        
        slope_issues = len([i for i in issues if i.issue_type == SafetyIssueType.SLOPE_STABILITY])
        if slope_issues > 2:
            recommendations.append("边坡稳定性问题需要重点关注，建议进行详细地质勘察")
        
        return recommendations
    
    def _generate_analysis_summary(self, 
                                 issues: List[SafetyIssue], 
                                 road_geometry: RoadGeometry) -> Dict[str, Any]:
        """生成分析摘要"""
        return {
            'road_length': sum(road_geometry.chainages[-1:]) if road_geometry.chainages else 0,
            'design_speed': road_geometry.design_speed,
            'analysis_date': 'current',
            'issue_distribution': {
                issue_type.value: len([i for i in issues if i.issue_type == issue_type])
                for issue_type in SafetyIssueType
            },
            'severity_distribution': {
                level.value: len([i for i in issues if i.severity_level == level])
                for level in SafetyLevel
            }
        }
