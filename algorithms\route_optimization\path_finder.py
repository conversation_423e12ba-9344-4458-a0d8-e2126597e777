"""
路径查找算法
"""
import numpy as np
import math
from typing import List, Dict, Tuple, Optional, Set
from dataclasses import dataclass
import heapq
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class PathFindingAlgorithm(Enum):
    """路径查找算法枚举"""
    DIJKSTRA = "dijkstra"
    A_STAR = "a_star"
    FLOYD_WARSHALL = "floyd_warshall"
    GENETIC_ALGORITHM = "genetic_algorithm"


@dataclass
class PathNode:
    """路径节点"""
    node_id: str
    x: float
    y: float
    z: float
    g_cost: float = float('inf')  # 从起点到当前节点的实际成本
    h_cost: float = 0.0  # 从当前节点到终点的启发式成本
    f_cost: float = float('inf')  # g_cost + h_cost
    parent: Optional[str] = None


@dataclass
class PathResult:
    """路径查找结果"""
    path: List[str]
    total_distance: float
    total_cost: float
    algorithm_used: str
    computation_time: float
    nodes_explored: int


class PathFinder:
    """路径查找器"""
    
    def __init__(self):
        self.nodes: Dict[str, PathNode] = {}
        self.edges: Dict[Tuple[str, str], Dict[str, float]] = {}
        self.adjacency_list: Dict[str, List[str]] = {}
    
    def add_node(self, node_id: str, x: float, y: float, z: float = 0.0) -> None:
        """添加节点"""
        self.nodes[node_id] = PathNode(node_id, x, y, z)
        if node_id not in self.adjacency_list:
            self.adjacency_list[node_id] = []
    
    def add_edge(self, from_node: str, to_node: str, 
                distance: float, cost: float = None, bidirectional: bool = True) -> None:
        """添加边"""
        if cost is None:
            cost = distance
        
        self.edges[(from_node, to_node)] = {
            'distance': distance,
            'cost': cost
        }
        
        if from_node in self.adjacency_list:
            self.adjacency_list[from_node].append(to_node)
        
        if bidirectional:
            self.edges[(to_node, from_node)] = {
                'distance': distance,
                'cost': cost
            }
            if to_node in self.adjacency_list:
                self.adjacency_list[to_node].append(from_node)
    
    def find_shortest_path(self, 
                          start: str,
                          goal: str,
                          algorithm: PathFindingAlgorithm = PathFindingAlgorithm.A_STAR,
                          cost_function: str = 'distance') -> Optional[PathResult]:
        """查找最短路径"""
        try:
            import time
            start_time = time.time()
            
            if algorithm == PathFindingAlgorithm.DIJKSTRA:
                result = self._dijkstra(start, goal, cost_function)
            elif algorithm == PathFindingAlgorithm.A_STAR:
                result = self._a_star(start, goal, cost_function)
            elif algorithm == PathFindingAlgorithm.FLOYD_WARSHALL:
                result = self._floyd_warshall(start, goal, cost_function)
            else:
                raise ValueError(f"不支持的算法: {algorithm}")
            
            if result:
                computation_time = time.time() - start_time
                result.computation_time = computation_time
                result.algorithm_used = algorithm.value
            
            return result
            
        except Exception as e:
            logger.error(f"路径查找失败: {str(e)}")
            return None
    
    def _dijkstra(self, start: str, goal: str, cost_function: str) -> Optional[PathResult]:
        """Dijkstra算法"""
        try:
            if start not in self.nodes or goal not in self.nodes:
                return None
            
            # 初始化
            distances = {node_id: float('inf') for node_id in self.nodes}
            distances[start] = 0
            previous = {}
            visited = set()
            priority_queue = [(0, start)]
            nodes_explored = 0
            
            while priority_queue:
                current_distance, current_node = heapq.heappop(priority_queue)
                
                if current_node in visited:
                    continue
                
                visited.add(current_node)
                nodes_explored += 1
                
                if current_node == goal:
                    break
                
                # 检查所有邻居
                for neighbor in self.adjacency_list.get(current_node, []):
                    if neighbor in visited:
                        continue
                    
                    edge_data = self.edges.get((current_node, neighbor))
                    if not edge_data:
                        continue
                    
                    edge_cost = edge_data.get(cost_function, edge_data['distance'])
                    new_distance = current_distance + edge_cost
                    
                    if new_distance < distances[neighbor]:
                        distances[neighbor] = new_distance
                        previous[neighbor] = current_node
                        heapq.heappush(priority_queue, (new_distance, neighbor))
            
            # 重构路径
            if goal not in previous and goal != start:
                return None
            
            path = self._reconstruct_path(previous, start, goal)
            total_distance = self._calculate_path_distance(path)
            total_cost = distances[goal]
            
            return PathResult(
                path=path,
                total_distance=total_distance,
                total_cost=total_cost,
                algorithm_used='dijkstra',
                computation_time=0,
                nodes_explored=nodes_explored
            )
            
        except Exception as e:
            logger.error(f"Dijkstra算法失败: {str(e)}")
            return None
    
    def _a_star(self, start: str, goal: str, cost_function: str) -> Optional[PathResult]:
        """A*算法"""
        try:
            if start not in self.nodes or goal not in self.nodes:
                return None
            
            # 初始化
            open_set = [(0, start)]
            closed_set = set()
            g_scores = {start: 0}
            f_scores = {start: self._heuristic(start, goal)}
            came_from = {}
            nodes_explored = 0
            
            while open_set:
                current_f, current = heapq.heappop(open_set)
                
                if current in closed_set:
                    continue
                
                closed_set.add(current)
                nodes_explored += 1
                
                if current == goal:
                    path = self._reconstruct_path(came_from, start, goal)
                    total_distance = self._calculate_path_distance(path)
                    total_cost = g_scores[goal]
                    
                    return PathResult(
                        path=path,
                        total_distance=total_distance,
                        total_cost=total_cost,
                        algorithm_used='a_star',
                        computation_time=0,
                        nodes_explored=nodes_explored
                    )
                
                # 检查所有邻居
                for neighbor in self.adjacency_list.get(current, []):
                    if neighbor in closed_set:
                        continue
                    
                    edge_data = self.edges.get((current, neighbor))
                    if not edge_data:
                        continue
                    
                    edge_cost = edge_data.get(cost_function, edge_data['distance'])
                    tentative_g = g_scores[current] + edge_cost
                    
                    if neighbor not in g_scores or tentative_g < g_scores[neighbor]:
                        came_from[neighbor] = current
                        g_scores[neighbor] = tentative_g
                        h_score = self._heuristic(neighbor, goal)
                        f_scores[neighbor] = tentative_g + h_score
                        
                        heapq.heappush(open_set, (f_scores[neighbor], neighbor))
            
            return None  # 没有找到路径
            
        except Exception as e:
            logger.error(f"A*算法失败: {str(e)}")
            return None
    
    def _floyd_warshall(self, start: str, goal: str, cost_function: str) -> Optional[PathResult]:
        """Floyd-Warshall算法（适用于小规模图）"""
        try:
            node_list = list(self.nodes.keys())
            n = len(node_list)
            
            if n > 100:  # 限制规模
                logger.warning("图规模过大，不适合使用Floyd-Warshall算法")
                return self._a_star(start, goal, cost_function)
            
            # 初始化距离矩阵
            dist = [[float('inf')] * n for _ in range(n)]
            next_node = [[None] * n for _ in range(n)]
            
            # 节点索引映射
            node_to_index = {node: i for i, node in enumerate(node_list)}
            
            # 初始化对角线
            for i in range(n):
                dist[i][i] = 0
            
            # 初始化边
            for (from_node, to_node), edge_data in self.edges.items():
                i = node_to_index[from_node]
                j = node_to_index[to_node]
                cost = edge_data.get(cost_function, edge_data['distance'])
                dist[i][j] = cost
                next_node[i][j] = to_node
            
            # Floyd-Warshall主循环
            for k in range(n):
                for i in range(n):
                    for j in range(n):
                        if dist[i][k] + dist[k][j] < dist[i][j]:
                            dist[i][j] = dist[i][k] + dist[k][j]
                            next_node[i][j] = next_node[i][k]
            
            # 重构路径
            start_idx = node_to_index[start]
            goal_idx = node_to_index[goal]
            
            if dist[start_idx][goal_idx] == float('inf'):
                return None
            
            path = [start]
            current = start
            while current != goal:
                current_idx = node_to_index[current]
                goal_idx = node_to_index[goal]
                current = next_node[current_idx][goal_idx]
                if current is None:
                    break
                path.append(current)
            
            total_distance = self._calculate_path_distance(path)
            total_cost = dist[start_idx][goal_idx]
            
            return PathResult(
                path=path,
                total_distance=total_distance,
                total_cost=total_cost,
                algorithm_used='floyd_warshall',
                computation_time=0,
                nodes_explored=n * n * n
            )
            
        except Exception as e:
            logger.error(f"Floyd-Warshall算法失败: {str(e)}")
            return None
    
    def _heuristic(self, node1: str, node2: str) -> float:
        """启发式函数（欧几里得距离）"""
        if node1 not in self.nodes or node2 not in self.nodes:
            return 0
        
        n1 = self.nodes[node1]
        n2 = self.nodes[node2]
        
        return math.sqrt((n1.x - n2.x)**2 + (n1.y - n2.y)**2 + (n1.z - n2.z)**2)
    
    def _reconstruct_path(self, came_from: Dict[str, str], start: str, goal: str) -> List[str]:
        """重构路径"""
        path = [goal]
        current = goal
        
        while current != start:
            if current not in came_from:
                break
            current = came_from[current]
            path.append(current)
        
        path.reverse()
        return path
    
    def _calculate_path_distance(self, path: List[str]) -> float:
        """计算路径总距离"""
        total_distance = 0
        
        for i in range(len(path) - 1):
            edge_data = self.edges.get((path[i], path[i + 1]))
            if edge_data:
                total_distance += edge_data['distance']
        
        return total_distance
    
    def find_multiple_paths(self, 
                           start: str,
                           goal: str,
                           k: int = 3,
                           algorithm: PathFindingAlgorithm = PathFindingAlgorithm.A_STAR) -> List[PathResult]:
        """查找K条最短路径"""
        try:
            paths = []
            
            # 第一条路径
            first_path = self.find_shortest_path(start, goal, algorithm)
            if first_path:
                paths.append(first_path)
            
            # 使用Yen's算法查找K条最短路径
            for i in range(1, k):
                candidate_paths = []
                
                if i - 1 >= len(paths):
                    break
                
                previous_path = paths[i - 1].path
                
                # 对于前一条路径的每个节点
                for j in range(len(previous_path) - 1):
                    spur_node = previous_path[j]
                    root_path = previous_path[:j + 1]
                    
                    # 临时移除已使用的边
                    removed_edges = []
                    for path in paths:
                        if len(path.path) > j and path.path[:j + 1] == root_path:
                            if j + 1 < len(path.path):
                                edge_key = (path.path[j], path.path[j + 1])
                                if edge_key in self.edges:
                                    removed_edges.append(edge_key)
                                    del self.edges[edge_key]
                    
                    # 查找从spur_node到目标的路径
                    spur_path = self.find_shortest_path(spur_node, goal, algorithm)
                    
                    # 恢复移除的边
                    for edge_key in removed_edges:
                        # 这里需要保存原始边数据，简化处理
                        pass
                    
                    if spur_path and len(spur_path.path) > 1:
                        # 组合路径
                        total_path = root_path[:-1] + spur_path.path
                        total_distance = self._calculate_path_distance(total_path)
                        
                        candidate_path = PathResult(
                            path=total_path,
                            total_distance=total_distance,
                            total_cost=spur_path.total_cost,
                            algorithm_used=algorithm.value,
                            computation_time=0,
                            nodes_explored=0
                        )
                        
                        candidate_paths.append(candidate_path)
                
                # 选择最短的候选路径
                if candidate_paths:
                    candidate_paths.sort(key=lambda p: p.total_distance)
                    paths.append(candidate_paths[0])
            
            return paths
            
        except Exception as e:
            logger.error(f"多路径查找失败: {str(e)}")
            return []
    
    def analyze_path_quality(self, path_result: PathResult) -> Dict[str, float]:
        """分析路径质量"""
        try:
            if not path_result or len(path_result.path) < 2:
                return {}
            
            path = path_result.path
            
            # 计算路径特征
            total_turns = 0
            elevation_changes = []
            segment_lengths = []
            
            for i in range(len(path) - 1):
                # 计算段长度
                edge_data = self.edges.get((path[i], path[i + 1]))
                if edge_data:
                    segment_lengths.append(edge_data['distance'])
                
                # 计算高程变化
                if i < len(path) - 1:
                    node1 = self.nodes[path[i]]
                    node2 = self.nodes[path[i + 1]]
                    elevation_change = abs(node2.z - node1.z)
                    elevation_changes.append(elevation_change)
                
                # 计算转弯次数（简化）
                if i > 0 and i < len(path) - 1:
                    # 计算角度变化
                    prev_node = self.nodes[path[i - 1]]
                    curr_node = self.nodes[path[i]]
                    next_node = self.nodes[path[i + 1]]
                    
                    # 简化的角度计算
                    angle1 = math.atan2(curr_node.y - prev_node.y, curr_node.x - prev_node.x)
                    angle2 = math.atan2(next_node.y - curr_node.y, next_node.x - curr_node.x)
                    angle_diff = abs(angle2 - angle1)
                    
                    if angle_diff > math.pi / 6:  # 30度以上认为是转弯
                        total_turns += 1
            
            # 计算质量指标
            avg_segment_length = sum(segment_lengths) / len(segment_lengths) if segment_lengths else 0
            total_elevation_change = sum(elevation_changes)
            path_straightness = path_result.total_distance / self._heuristic(path[0], path[-1]) if len(path) > 1 else 1
            
            return {
                'total_distance': path_result.total_distance,
                'total_cost': path_result.total_cost,
                'segment_count': len(path) - 1,
                'average_segment_length': avg_segment_length,
                'total_turns': total_turns,
                'total_elevation_change': total_elevation_change,
                'path_straightness': path_straightness,
                'efficiency_score': 1.0 / path_straightness if path_straightness > 0 else 0,
                'complexity_score': total_turns + total_elevation_change / 100
            }
            
        except Exception as e:
            logger.error(f"路径质量分析失败: {str(e)}")
            return {}
    
    def get_network_statistics(self) -> Dict[str, Any]:
        """获取网络统计信息"""
        try:
            node_count = len(self.nodes)
            edge_count = len(self.edges)
            
            # 计算连通性
            connected_components = self._find_connected_components()
            
            # 计算度分布
            degree_distribution = {}
            for node_id in self.nodes:
                degree = len(self.adjacency_list.get(node_id, []))
                degree_distribution[degree] = degree_distribution.get(degree, 0) + 1
            
            # 计算平均度
            total_degree = sum(len(neighbors) for neighbors in self.adjacency_list.values())
            avg_degree = total_degree / node_count if node_count > 0 else 0
            
            return {
                'node_count': node_count,
                'edge_count': edge_count,
                'connected_components': len(connected_components),
                'average_degree': avg_degree,
                'degree_distribution': degree_distribution,
                'network_density': edge_count / (node_count * (node_count - 1) / 2) if node_count > 1 else 0
            }
            
        except Exception as e:
            logger.error(f"网络统计失败: {str(e)}")
            return {}
    
    def _find_connected_components(self) -> List[Set[str]]:
        """查找连通分量"""
        visited = set()
        components = []
        
        for node_id in self.nodes:
            if node_id not in visited:
                component = set()
                stack = [node_id]
                
                while stack:
                    current = stack.pop()
                    if current not in visited:
                        visited.add(current)
                        component.add(current)
                        
                        for neighbor in self.adjacency_list.get(current, []):
                            if neighbor not in visited:
                                stack.append(neighbor)
                
                components.append(component)
        
        return components
