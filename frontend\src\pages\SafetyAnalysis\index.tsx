import React, { useState, useEffect } from 'react'
import {
  Card, Button, Table, Tag, Space, Modal, Form, Select,
  Checkbox, message, Row, Col, Statistic, Progress,
  Descriptions, Tooltip, Alert, Tabs, List, Badge
} from 'antd'
import {
  PlayCircleOutlined, EyeOutlined, SafetyOutlined,
  Exclamation<PERSON>ircleOutlined, WarningOutlined, CheckCircleOutlined,
  BugOutlined, EnvironmentOutlined, Bar<PERSON>hartOutlined,
  FileTextOutlined, ReloadOutlined, <PERSON>boltOutlined,
  CarOutlined, LineChartOutlined
} from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import axios from 'axios'

const { TabPane } = Tabs

interface SafetyAnalysisData {
  id: number
  road_id: number
  analysis_type: string
  overall_safety_score: number
  safety_level: string
  total_issues: number
  critical_issues: number
  warning_issues: number
  analyzed_at: string
}

interface SafetyIssueData {
  id: number
  issue_type: string
  severity_level: string
  title: string
  description: string
  location: number[]
  chainage?: number
  risk_score?: number
  urgency_level?: number
}

interface SafetyStatistics {
  total_analyses: number
  average_safety_score: number
  total_issues: number
  critical_issues: number
  safety_trend: string
  safety_level_distribution: Record<string, number>
}

interface SightDistanceResult {
  chainage: number
  location: number[]
  stopping_sight_distance: number
  actual_sight_distance: number
  is_adequate: boolean
  deficiency: number
  recommendations: string[]
}

const SafetyAnalysis: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [analysisList, setAnalysisList] = useState<SafetyAnalysisData[]>([])
  const [statistics, setStatistics] = useState<SafetyStatistics | null>(null)
  const [selectedAnalysis, setSelectedAnalysis] = useState<SafetyAnalysisData | null>(null)
  const [issuesList, setIssuesList] = useState<SafetyIssueData[]>([])
  const [sightDistanceData, setSightDistanceData] = useState<SightDistanceResult[]>([])
  const [loading, setLoading] = useState(false)
  const [analyzing, setAnalyzing] = useState(false)
  const [analysisModalVisible, setAnalysisModalVisible] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [sightModalVisible, setSightModalVisible] = useState(false)
  const [form] = Form.useForm()

  // 获取安全分析列表
  const fetchAnalysisList = async () => {
    if (!projectId) return

    setLoading(true)
    try {
      const response = await axios.get(`/api/v1/safety/${projectId}/safety-analyses`)
      setAnalysisList(response.data)
    } catch (error) {
      message.error('获取安全分析列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    if (!projectId) return

    try {
      const response = await axios.get(`/api/v1/safety/${projectId}/safety-statistics`)
      setStatistics(response.data.statistics)
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  useEffect(() => {
    fetchAnalysisList()
    fetchStatistics()
  }, [projectId])

  // 开始安全分析
  const handleStartAnalysis = async () => {
    try {
      const values = await form.validateFields()
      setAnalyzing(true)

      const analysisParams = {
        analysis_type: values.analysis_type,
        include_sight_distance: values.include_sight_distance,
        include_slope_stability: values.include_slope_stability,
        include_geometric_safety: values.include_geometric_safety,
        include_drainage: values.include_drainage,
        include_traffic_safety: values.include_traffic_safety
      }

      if (values.road_ids && values.road_ids.length > 0) {
        // 单个道路分析
        for (const roadId of values.road_ids) {
          await axios.post(
            `/api/v1/safety/${projectId}/roads/${roadId}/analyze`,
            analysisParams
          )
        }
      } else {
        // 批量分析
        await axios.post(
          `/api/v1/safety/${projectId}/batch-analyze`,
          analysisParams
        )
      }

      message.success('安全分析完成')
      setAnalysisModalVisible(false)
      form.resetFields()
      fetchAnalysisList()
      fetchStatistics()
    } catch (error) {
      message.error('安全分析失败')
    } finally {
      setAnalyzing(false)
    }
  }

  // 查看分析详情
  const handleViewDetail = async (analysis: SafetyAnalysisData) => {
    setSelectedAnalysis(analysis)

    try {
      const response = await axios.get(`/api/v1/safety/analyses/${analysis.id}/issues`)
      setIssuesList(response.data)
      setDetailModalVisible(true)
    } catch (error) {
      message.error('获取安全问题详情失败')
    }
  }

  // 分析视距
  const handleAnalyzeSightDistance = async (roadId: number) => {
    try {
      const response = await axios.get(`/api/v1/safety/${projectId}/roads/${roadId}/sight-distance`)
      setSightDistanceData(response.data.analysis_results)
      setSightModalVisible(true)
    } catch (error) {
      message.error('视距分析失败')
    }
  }

  // 获取安全等级标签
  const getSafetyLevelTag = (level: string) => {
    const levelMap = {
      safe: { color: 'green', text: '安全', icon: <CheckCircleOutlined /> },
      caution: { color: 'orange', text: '注意', icon: <ExclamationCircleOutlined /> },
      warning: { color: 'red', text: '警告', icon: <WarningOutlined /> },
      danger: { color: 'purple', text: '危险', icon: <BugOutlined /> }
    }
    const config = levelMap[level as keyof typeof levelMap] || { color: 'default', text: level, icon: null }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 获取严重程度标签
  const getSeverityTag = (severity: string) => {
    const severityMap = {
      safe: { color: 'green', text: '安全' },
      caution: { color: 'blue', text: '注意' },
      warning: { color: 'orange', text: '警告' },
      danger: { color: 'red', text: '危险' }
    }
    const config = severityMap[severity as keyof typeof severityMap] || { color: 'default', text: severity }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 获取问题类型文本
  const getIssueTypeText = (type: string) => {
    const typeMap = {
      sight_distance: '视距问题',
      slope_stability: '边坡稳定性',
      geometric_hazard: '几何危险',
      drainage_issue: '排水问题',
      traffic_safety: '交通安全',
      structural_safety: '结构安全'
    }
    return typeMap[type as keyof typeof typeMap] || type
  }

  // 分析表格列定义
  const analysisColumns = [
    {
      title: '道路ID',
      dataIndex: 'road_id',
      key: 'road_id',
      render: (roadId: number) => (
        <Space>
          <CarOutlined />
          <span>道路 {roadId}</span>
        </Space>
      )
    },
    {
      title: '分析类型',
      dataIndex: 'analysis_type',
      key: 'analysis_type',
      render: (type: string) => type === 'comprehensive' ? '综合分析' : type
    },
    {
      title: '安全评分',
      dataIndex: 'overall_safety_score',
      key: 'overall_safety_score',
      render: (score: number) => (
        <Space>
          <Progress
            type="circle"
            size={50}
            percent={score}
            status={score >= 80 ? 'success' : score >= 60 ? 'normal' : 'exception'}
          />
          <span>{score.toFixed(1)}</span>
        </Space>
      )
    },
    {
      title: '安全等级',
      dataIndex: 'safety_level',
      key: 'safety_level',
      render: (level: string) => getSafetyLevelTag(level)
    },
    {
      title: '问题统计',
      key: 'issues',
      render: (record: SafetyAnalysisData) => (
        <Space direction="vertical" size="small">
          <span>总计: {record.total_issues}</span>
          <span style={{ color: '#f5222d' }}>严重: {record.critical_issues}</span>
          <span style={{ color: '#faad14' }}>警告: {record.warning_issues}</span>
        </Space>
      )
    },
    {
      title: '分析时间',
      dataIndex: 'analyzed_at',
      key: 'analyzed_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: SafetyAnalysisData) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>

          <Tooltip title="视距分析">
            <Button
              type="text"
              icon={<LineChartOutlined />}
              size="small"
              onClick={() => handleAnalyzeSightDistance(record.road_id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  // 问题表格列定义
  const issueColumns = [
    {
      title: '问题类型',
      dataIndex: 'issue_type',
      key: 'issue_type',
      render: (type: string) => getIssueTypeText(type)
    },
    {
      title: '严重程度',
      dataIndex: 'severity_level',
      key: 'severity_level',
      render: (severity: string) => getSeverityTag(severity)
    },
    {
      title: '问题描述',
      dataIndex: 'description',
      key: 'description'
    },
    {
      title: '位置',
      key: 'location',
      render: (record: SafetyIssueData) => (
        <Space direction="vertical" size="small">
          <span>
            <EnvironmentOutlined />
            {record.location[0]?.toFixed(1)}, {record.location[1]?.toFixed(1)}
          </span>
          {record.chainage && (
            <span style={{ fontSize: '12px', color: '#666' }}>
              里程: {record.chainage.toFixed(1)}m
            </span>
          )}
        </Space>
      )
    },
    {
      title: '风险评分',
      dataIndex: 'risk_score',
      key: 'risk_score',
      render: (score: number) => score ? (
        <Progress
          percent={Math.round(score)}
          size="small"
          status={score > 80 ? 'exception' : score > 60 ? 'normal' : 'success'}
        />
      ) : '-'
    },
    {
      title: '紧急程度',
      dataIndex: 'urgency_level',
      key: 'urgency_level',
      render: (level: number) => level ? (
        <Badge
          count={level}
          style={{ backgroundColor: level >= 4 ? '#f5222d' : level >= 3 ? '#faad14' : '#52c41a' }}
        />
      ) : '-'
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          安全分析
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          道路安全性综合评估，包括视距、边坡稳定性、几何安全等多维度分析
        </p>
      </div>

      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="分析总数"
                value={statistics.total_analyses}
                prefix={<SafetyOutlined />}
                suffix="次"
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="平均安全评分"
                value={statistics.average_safety_score}
                prefix={<BarChartOutlined />}
                precision={1}
                valueStyle={{
                  color: statistics.average_safety_score >= 80 ? '#52c41a' :
                         statistics.average_safety_score >= 60 ? '#faad14' : '#f5222d'
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="安全问题"
                value={statistics.total_issues}
                prefix={<BugOutlined />}
                suffix="个"
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="严重问题"
                value={statistics.critical_issues}
                prefix={<WarningOutlined />}
                suffix="个"
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 安全趋势提示 */}
      {statistics && statistics.safety_trend !== 'stable' && (
        <Alert
          message={`安全趋势: ${statistics.safety_trend === 'improving' ? '改善中' : '下降中'}`}
          description={
            statistics.safety_trend === 'improving'
              ? '项目安全水平正在改善，请继续保持良好的安全管理'
              : '项目安全水平有所下降，建议加强安全检查和改进措施'
          }
          type={statistics.safety_trend === 'improving' ? 'success' : 'warning'}
          showIcon
          style={{ marginBottom: '24px' }}
        />
      )}

      {/* 主要内容 */}
      <Card
        title="安全分析记录"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchAnalysisList()
                fetchStatistics()
              }}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => setAnalysisModalVisible(true)}
              loading={analyzing}
            >
              开始分析
            </Button>
          </Space>
        }
      >
        <Table
          columns={analysisColumns}
          dataSource={analysisList}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 安全分析参数模态框 */}
      <Modal
        title="安全分析设置"
        open={analysisModalVisible}
        onOk={handleStartAnalysis}
        onCancel={() => {
          setAnalysisModalVisible(false)
          form.resetFields()
        }}
        confirmLoading={analyzing}
        okText="开始分析"
        cancelText="取消"
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="analysis_type"
            label="分析类型"
            initialValue="comprehensive"
          >
            <Select>
              <Select.Option value="comprehensive">综合安全分析</Select.Option>
              <Select.Option value="sight_distance">视距专项分析</Select.Option>
              <Select.Option value="slope_stability">边坡稳定性分析</Select.Option>
              <Select.Option value="geometric_safety">几何安全分析</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item label="分析内容">
            <Space direction="vertical">
              <Form.Item
                name="include_sight_distance"
                valuePropName="checked"
                initialValue={true}
                style={{ margin: 0 }}
              >
                <Checkbox>视距安全检查</Checkbox>
              </Form.Item>

              <Form.Item
                name="include_slope_stability"
                valuePropName="checked"
                initialValue={true}
                style={{ margin: 0 }}
              >
                <Checkbox>边坡稳定性检查</Checkbox>
              </Form.Item>

              <Form.Item
                name="include_geometric_safety"
                valuePropName="checked"
                initialValue={true}
                style={{ margin: 0 }}
              >
                <Checkbox>几何安全检查</Checkbox>
              </Form.Item>

              <Form.Item
                name="include_drainage"
                valuePropName="checked"
                initialValue={true}
                style={{ margin: 0 }}
              >
                <Checkbox>排水安全检查</Checkbox>
              </Form.Item>

              <Form.Item
                name="include_traffic_safety"
                valuePropName="checked"
                initialValue={true}
                style={{ margin: 0 }}
              >
                <Checkbox>交通安全检查</Checkbox>
              </Form.Item>
            </Space>
          </Form.Item>

          <Form.Item
            name="road_ids"
            label="选择道路"
            help="留空则分析项目下所有道路"
          >
            <Select
              mode="multiple"
              placeholder="选择要分析的道路，留空分析全部"
              allowClear
            >
              {/* TODO: 从API获取道路列表 */}
              <Select.Option value={1}>道路 1</Select.Option>
              <Select.Option value={2}>道路 2</Select.Option>
              <Select.Option value={3}>道路 3</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分析详情模态框 */}
      <Modal
        title={`安全分析详情 - 道路 ${selectedAnalysis?.road_id}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={1200}
      >
        {selectedAnalysis && (
          <Tabs defaultActiveKey="overview">
            <TabPane tab="分析概览" key="overview">
              <Descriptions bordered size="small" style={{ marginBottom: '16px' }}>
                <Descriptions.Item label="安全评分" span={2}>
                  <Space>
                    <Progress
                      type="circle"
                      size={60}
                      percent={selectedAnalysis.overall_safety_score}
                      status={selectedAnalysis.overall_safety_score >= 80 ? 'success' : 'exception'}
                    />
                    <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                      {selectedAnalysis.overall_safety_score.toFixed(1)}
                    </span>
                  </Space>
                </Descriptions.Item>
                <Descriptions.Item label="安全等级">
                  {getSafetyLevelTag(selectedAnalysis.safety_level)}
                </Descriptions.Item>
                <Descriptions.Item label="总问题数">{selectedAnalysis.total_issues}</Descriptions.Item>
                <Descriptions.Item label="严重问题">{selectedAnalysis.critical_issues}</Descriptions.Item>
                <Descriptions.Item label="警告问题">{selectedAnalysis.warning_issues}</Descriptions.Item>
                <Descriptions.Item label="分析时间" span={3}>
                  {new Date(selectedAnalysis.analyzed_at).toLocaleString()}
                </Descriptions.Item>
              </Descriptions>
            </TabPane>

            <TabPane tab="安全问题" key="issues">
              <Table
                columns={issueColumns}
                dataSource={issuesList}
                rowKey="id"
                size="small"
                pagination={{ pageSize: 10 }}
              />
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 视距分析模态框 */}
      <Modal
        title="视距分析结果"
        open={sightModalVisible}
        onCancel={() => setSightModalVisible(false)}
        footer={null}
        width={1000}
      >
        <List
          dataSource={sightDistanceData}
          renderItem={(item) => (
            <List.Item>
              <List.Item.Meta
                title={`里程 ${item.chainage.toFixed(1)}m`}
                description={
                  <Space direction="vertical" size="small">
                    <Space>
                      <span>要求视距: {item.stopping_sight_distance.toFixed(1)}m</span>
                      <span>实际视距: {item.actual_sight_distance.toFixed(1)}m</span>
                      <Tag color={item.is_adequate ? 'green' : 'red'}>
                        {item.is_adequate ? '满足' : '不足'}
                      </Tag>
                    </Space>
                    {!item.is_adequate && (
                      <span style={{ color: '#f5222d' }}>
                        视距不足: {item.deficiency.toFixed(1)}m
                      </span>
                    )}
                    <div>
                      <strong>建议:</strong>
                      <ul style={{ margin: '4px 0', paddingLeft: '20px' }}>
                        {item.recommendations.map((rec, index) => (
                          <li key={index}>{rec}</li>
                        ))}
                      </ul>
                    </div>
                  </Space>
                }
              />
            </List.Item>
          )}
          pagination={{ pageSize: 5 }}
        />
      </Modal>
    </div>
  )
}

export default SafetyAnalysis
