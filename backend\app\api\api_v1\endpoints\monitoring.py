"""
系统监控API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from pydantic import BaseModel

from app.core.database import get_db
from app.services.monitoring_service import MonitoringService

router = APIRouter()
monitoring_service = MonitoringService()


class AlertThreshold(BaseModel):
    """告警阈值模型"""
    cpu_usage: float = 80.0
    memory_usage: float = 85.0
    disk_usage: float = 90.0
    response_time: float = 2.0
    error_rate: float = 5.0


@router.get("/health")
async def get_health_status():
    """获取系统健康状态"""
    try:
        system_metrics = monitoring_service.get_system_metrics()
        app_metrics = monitoring_service.get_application_metrics()
        
        return {
            "status": app_metrics.get("health_status", "unknown"),
            "timestamp": system_metrics.get("timestamp"),
            "uptime": "系统运行正常",
            "version": "1.0.0"
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取健康状态失败: {str(e)}")


@router.get("/metrics/system")
async def get_system_metrics():
    """获取系统指标"""
    try:
        metrics = monitoring_service.get_system_metrics()
        return metrics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统指标失败: {str(e)}")


@router.get("/metrics/application")
async def get_application_metrics():
    """获取应用程序指标"""
    try:
        metrics = monitoring_service.get_application_metrics()
        return metrics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取应用程序指标失败: {str(e)}")


@router.get("/metrics/database")
async def get_database_metrics(db: Session = Depends(get_db)):
    """获取数据库指标"""
    try:
        metrics = monitoring_service.get_database_metrics(db)
        return metrics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取数据库指标失败: {str(e)}")


@router.get("/performance/summary")
async def get_performance_summary(
    hours: int = Query(24, ge=1, le=168, description="统计时间范围（小时）")
):
    """获取性能摘要"""
    try:
        summary = monitoring_service.get_performance_summary(hours=hours)
        return summary
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能摘要失败: {str(e)}")


@router.get("/alerts")
async def get_alerts():
    """获取当前告警"""
    try:
        alerts = monitoring_service.check_alerts()
        
        return {
            "alert_count": len(alerts),
            "alerts": alerts,
            "timestamp": monitoring_service.get_system_metrics().get("timestamp")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取告警信息失败: {str(e)}")


@router.post("/alerts/thresholds")
async def update_alert_thresholds(thresholds: AlertThreshold):
    """更新告警阈值"""
    try:
        monitoring_service.alert_thresholds.update(thresholds.dict())
        
        return {
            "success": True,
            "message": "告警阈值更新成功",
            "thresholds": monitoring_service.alert_thresholds
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"更新告警阈值失败: {str(e)}")


@router.get("/alerts/thresholds")
async def get_alert_thresholds():
    """获取告警阈值"""
    try:
        return {
            "thresholds": monitoring_service.alert_thresholds
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取告警阈值失败: {str(e)}")


@router.get("/system/info")
async def get_system_info():
    """获取系统信息"""
    try:
        info = monitoring_service.get_system_info()
        return info
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")


@router.get("/reports/health")
async def get_health_report(db: Session = Depends(get_db)):
    """获取健康报告"""
    try:
        report = monitoring_service.generate_health_report(db=db)
        return report
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成健康报告失败: {str(e)}")


@router.post("/counters/{counter_name}/increment")
async def increment_counter(
    counter_name: str,
    value: int = Query(1, ge=1, description="增加的值")
):
    """增加性能计数器"""
    try:
        monitoring_service.increment_counter(counter_name, value)
        
        return {
            "success": True,
            "message": f"计数器 {counter_name} 增加 {value}",
            "current_value": monitoring_service.performance_counters.get(counter_name, 0)
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"增加计数器失败: {str(e)}")


@router.get("/counters")
async def get_performance_counters():
    """获取性能计数器"""
    try:
        return {
            "counters": monitoring_service.performance_counters,
            "timestamp": monitoring_service.get_system_metrics().get("timestamp")
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能计数器失败: {str(e)}")


@router.post("/counters/reset")
async def reset_performance_counters():
    """重置性能计数器"""
    try:
        for key in monitoring_service.performance_counters:
            monitoring_service.performance_counters[key] = 0
        
        return {
            "success": True,
            "message": "性能计数器已重置",
            "counters": monitoring_service.performance_counters
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"重置性能计数器失败: {str(e)}")


@router.get("/diagnostics")
async def run_diagnostics():
    """运行系统诊断"""
    try:
        diagnostics = {
            "timestamp": monitoring_service.get_system_metrics().get("timestamp"),
            "checks": []
        }
        
        # 系统资源检查
        system_metrics = monitoring_service.get_system_metrics()
        cpu_usage = system_metrics.get('cpu', {}).get('usage_percent', 0)
        memory_usage = system_metrics.get('memory', {}).get('usage_percent', 0)
        disk_usage = system_metrics.get('disk', {}).get('usage_percent', 0)
        
        diagnostics["checks"].append({
            "name": "CPU使用率检查",
            "status": "pass" if cpu_usage < 80 else "warning" if cpu_usage < 90 else "fail",
            "value": f"{cpu_usage:.1f}%",
            "message": "CPU使用率正常" if cpu_usage < 80 else "CPU使用率较高"
        })
        
        diagnostics["checks"].append({
            "name": "内存使用率检查",
            "status": "pass" if memory_usage < 85 else "warning" if memory_usage < 95 else "fail",
            "value": f"{memory_usage:.1f}%",
            "message": "内存使用率正常" if memory_usage < 85 else "内存使用率较高"
        })
        
        diagnostics["checks"].append({
            "name": "磁盘使用率检查",
            "status": "pass" if disk_usage < 90 else "warning" if disk_usage < 95 else "fail",
            "value": f"{disk_usage:.1f}%",
            "message": "磁盘使用率正常" if disk_usage < 90 else "磁盘空间不足"
        })
        
        # 应用程序检查
        app_metrics = monitoring_service.get_application_metrics()
        health_status = app_metrics.get("health_status", "unknown")
        
        diagnostics["checks"].append({
            "name": "应用程序健康检查",
            "status": "pass" if health_status == "healthy" else "warning" if health_status == "warning" else "fail",
            "value": health_status,
            "message": f"应用程序状态: {health_status}"
        })
        
        # 错误率检查
        total_requests = monitoring_service.performance_counters['api_requests']
        total_errors = monitoring_service.performance_counters['api_errors']
        error_rate = (total_errors / total_requests * 100) if total_requests > 0 else 0
        
        diagnostics["checks"].append({
            "name": "API错误率检查",
            "status": "pass" if error_rate < 5 else "warning" if error_rate < 10 else "fail",
            "value": f"{error_rate:.1f}%",
            "message": "API错误率正常" if error_rate < 5 else "API错误率较高"
        })
        
        # 计算总体状态
        statuses = [check["status"] for check in diagnostics["checks"]]
        if "fail" in statuses:
            overall_status = "fail"
        elif "warning" in statuses:
            overall_status = "warning"
        else:
            overall_status = "pass"
        
        diagnostics["overall_status"] = overall_status
        diagnostics["summary"] = {
            "total_checks": len(diagnostics["checks"]),
            "passed": len([s for s in statuses if s == "pass"]),
            "warnings": len([s for s in statuses if s == "warning"]),
            "failed": len([s for s in statuses if s == "fail"])
        }
        
        return diagnostics
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"运行系统诊断失败: {str(e)}")


@router.get("/status/dashboard")
async def get_dashboard_data():
    """获取监控仪表板数据"""
    try:
        system_metrics = monitoring_service.get_system_metrics()
        app_metrics = monitoring_service.get_application_metrics()
        alerts = monitoring_service.check_alerts()
        performance_summary = monitoring_service.get_performance_summary(hours=1)
        
        dashboard_data = {
            "timestamp": system_metrics.get("timestamp"),
            "overview": {
                "health_status": app_metrics.get("health_status", "unknown"),
                "cpu_usage": system_metrics.get('cpu', {}).get('usage_percent', 0),
                "memory_usage": system_metrics.get('memory', {}).get('usage_percent', 0),
                "disk_usage": system_metrics.get('disk', {}).get('usage_percent', 0),
                "active_alerts": len(alerts)
            },
            "performance": {
                "api_requests": monitoring_service.performance_counters['api_requests'],
                "api_errors": monitoring_service.performance_counters['api_errors'],
                "database_queries": monitoring_service.performance_counters['database_queries'],
                "file_uploads": monitoring_service.performance_counters['file_uploads'],
                "calculations": monitoring_service.performance_counters['calculations_performed']
            },
            "recent_alerts": alerts[:5],  # 最近5个告警
            "trends": {
                "cpu_average": performance_summary.get('cpu', {}).get('average', 0),
                "memory_average": performance_summary.get('memory', {}).get('average', 0),
                "disk_average": performance_summary.get('disk', {}).get('average', 0)
            }
        }
        
        return dashboard_data
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取仪表板数据失败: {str(e)}")


@router.get("/logs/recent")
async def get_recent_logs(
    lines: int = Query(100, ge=1, le=1000, description="日志行数"),
    level: str = Query("INFO", description="日志级别")
):
    """获取最近的日志"""
    try:
        # 这里应该实现实际的日志读取逻辑
        # 简化实现，返回模拟数据
        
        logs = [
            {
                "timestamp": "2024-01-01T12:00:00Z",
                "level": "INFO",
                "message": "系统启动完成",
                "module": "main"
            },
            {
                "timestamp": "2024-01-01T12:01:00Z",
                "level": "INFO",
                "message": "API服务器启动",
                "module": "api"
            }
        ]
        
        return {
            "logs": logs[:lines],
            "total_lines": len(logs),
            "level_filter": level
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取日志失败: {str(e)}")
