"""
冲突检测服务
"""
import os
import json
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import asyncio

from app.models.conflict_detection import Conflict, ConflictType as DBConflictType
from app.models.road import Road
from app.models.project import Project
from app.models.terrain import TerrainData
from app.core.config import settings
from algorithms.conflict_detection.conflict_detector import (
    ConflictDetector, RoadGeometry, ConflictResult, ConflictType, ConflictSeverity
)
import logging

logger = logging.getLogger(__name__)


class ConflictService:
    """冲突检测服务类"""
    
    def __init__(self):
        self.detector = ConflictDetector()
    
    async def detect_road_conflicts(self, 
                                   db: Session,
                                   road_id: int,
                                   detection_params: Dict = None) -> List[Conflict]:
        """检测道路冲突"""
        try:
            # 获取道路信息
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            logger.info(f"开始检测道路冲突: {road.name} (ID: {road_id})")
            
            # 构建道路几何数据
            road_geometry = await self._build_road_geometry(db, road)
            
            # 获取地形数据
            terrain_data = await self._get_terrain_data(db, road.project_id)
            
            # 获取基础设施数据
            infrastructure_data = await self._get_infrastructure_data(db, road.project_id)
            
            # 获取环境数据
            environmental_data = await self._get_environmental_data(db, road.project_id)
            
            # 执行冲突检测
            conflict_results = await asyncio.get_event_loop().run_in_executor(
                None,
                self.detector.detect_conflicts,
                road_geometry,
                terrain_data,
                infrastructure_data,
                environmental_data
            )
            
            # 保存冲突结果到数据库
            conflicts = []
            for result in conflict_results:
                conflict = await self._save_conflict_result(db, road, result)
                conflicts.append(conflict)
            
            logger.info(f"道路冲突检测完成，发现 {len(conflicts)} 个冲突")
            return conflicts
            
        except Exception as e:
            logger.error(f"道路冲突检测失败: {str(e)}")
            raise
    
    async def _build_road_geometry(self, db: Session, road: Road) -> RoadGeometry:
        """构建道路几何数据"""
        try:
            # 从道路点位构建几何数据
            from app.models.road import RoadPoint, RoadSegment
            
            points_query = db.query(RoadPoint).join(RoadSegment).filter(
                RoadSegment.road_id == road.id
            ).order_by(RoadPoint.chainage)
            
            road_points = points_query.all()
            
            if not road_points:
                # 如果没有详细点位，使用段数据
                segments = db.query(RoadSegment).filter(RoadSegment.road_id == road.id).all()
                points = []
                chainages = []
                widths = []
                gradients = []
                curvatures = []
                
                for segment in segments:
                    if segment.start_point:
                        points.append(tuple(segment.start_point))
                        chainages.append(segment.start_chainage)
                        widths.append(road.road_width)
                        gradients.append(segment.gradient or 0.0)
                        curvatures.append(1.0 / segment.radius if segment.radius else 0.0)
                    
                    if segment.end_point:
                        points.append(tuple(segment.end_point))
                        chainages.append(segment.end_chainage)
                        widths.append(road.road_width)
                        gradients.append(segment.gradient or 0.0)
                        curvatures.append(1.0 / segment.radius if segment.radius else 0.0)
            else:
                # 使用详细点位数据
                points = [(p.x, p.y, p.z) for p in road_points]
                chainages = [p.chainage for p in road_points]
                widths = [p.left_width + p.right_width for p in road_points]
                gradients = [0.0] * len(road_points)  # TODO: 计算实际坡度
                curvatures = [0.0] * len(road_points)  # TODO: 计算实际曲率
            
            return RoadGeometry(
                points=points,
                chainages=chainages,
                widths=widths,
                gradients=gradients,
                curvatures=curvatures
            )
            
        except Exception as e:
            logger.error(f"构建道路几何数据失败: {str(e)}")
            raise
    
    async def _get_terrain_data(self, db: Session, project_id: int) -> Optional[any]:
        """获取地形数据"""
        try:
            terrain_list = db.query(TerrainData).filter(
                TerrainData.project_id == project_id,
                TerrainData.processing_status == 'processed'
            ).first()
            
            if terrain_list:
                # TODO: 加载实际的DEM数据
                return None  # 返回numpy数组
            
            return None
            
        except Exception as e:
            logger.error(f"获取地形数据失败: {str(e)}")
            return None
    
    async def _get_infrastructure_data(self, db: Session, project_id: int) -> List[Dict]:
        """获取基础设施数据"""
        try:
            # TODO: 从数据库获取基础设施数据
            # 这里返回模拟数据
            infrastructure_data = [
                {
                    'type': 'power_line',
                    'location': {'x': 100500, 'y': 200500},
                    'buffer_distance': 20.0
                },
                {
                    'type': 'building',
                    'location': {'x': 100800, 'y': 200300},
                    'buffer_distance': 15.0
                }
            ]
            
            return infrastructure_data
            
        except Exception as e:
            logger.error(f"获取基础设施数据失败: {str(e)}")
            return []
    
    async def _get_environmental_data(self, db: Session, project_id: int) -> List[Dict]:
        """获取环境数据"""
        try:
            # TODO: 从数据库获取环境数据
            # 这里返回模拟数据
            environmental_data = [
                {
                    'type': 'water_body',
                    'location': {
                        'boundary': [(100200, 200200), (100400, 200200), 
                                   (100400, 200400), (100200, 200400)]
                    },
                    'protection_buffer': 30.0,
                    'protection_level': 'medium'
                }
            ]
            
            return environmental_data
            
        except Exception as e:
            logger.error(f"获取环境数据失败: {str(e)}")
            return []
    
    async def _save_conflict_result(self, 
                                  db: Session, 
                                  road: Road, 
                                  result: ConflictResult) -> Conflict:
        """保存冲突结果到数据库"""
        try:
            # 映射冲突类型
            conflict_type_mapping = {
                ConflictType.TERRAIN_CONFLICT: DBConflictType.TERRAIN_CONFLICT,
                ConflictType.INFRASTRUCTURE_CONFLICT: DBConflictType.INFRASTRUCTURE_CONFLICT,
                ConflictType.ENVIRONMENTAL_CONFLICT: DBConflictType.ENVIRONMENTAL_CONFLICT,
                ConflictType.SAFETY_CONFLICT: DBConflictType.SAFETY_CONFLICT,
                ConflictType.GEOMETRIC_CONFLICT: DBConflictType.GEOMETRIC_CONFLICT,
                ConflictType.DRAINAGE_CONFLICT: DBConflictType.DRAINAGE_CONFLICT
            }
            
            # 映射严重程度
            severity_mapping = {
                ConflictSeverity.LOW: 'low',
                ConflictSeverity.MEDIUM: 'medium', 
                ConflictSeverity.HIGH: 'high',
                ConflictSeverity.CRITICAL: 'critical'
            }
            
            conflict = Conflict(
                project_id=road.project_id,
                road_id=road.id,
                conflict_type_id=1,  # TODO: 关联到ConflictType表
                title=result.description,
                description=result.description,
                location=[result.location.x, result.location.y, result.location.z],
                chainage=result.location.chainage,
                affected_area=result.affected_area,
                severity=severity_mapping.get(result.severity, 'medium'),
                confidence_score=result.confidence_score,
                impact_assessment=result.impact_assessment,
                resolution_options=result.resolution_options,
                detection_method='automated',
                detected_at=datetime.utcnow()
            )
            
            db.add(conflict)
            db.commit()
            db.refresh(conflict)
            
            return conflict
            
        except Exception as e:
            logger.error(f"保存冲突结果失败: {str(e)}")
            raise
    
    def get_project_conflicts(self, 
                             db: Session,
                             project_id: int,
                             skip: int = 0,
                             limit: int = 100) -> List[Conflict]:
        """获取项目冲突列表"""
        return db.query(Conflict)\
                 .filter(Conflict.project_id == project_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_road_conflicts(self, 
                          db: Session,
                          road_id: int,
                          skip: int = 0,
                          limit: int = 100) -> List[Conflict]:
        """获取道路冲突列表"""
        return db.query(Conflict)\
                 .filter(Conflict.road_id == road_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_conflict_detail(self, db: Session, conflict_id: int) -> Optional[Conflict]:
        """获取冲突详情"""
        return db.query(Conflict).filter(Conflict.id == conflict_id).first()
    
    def resolve_conflict(self, 
                        db: Session,
                        conflict_id: int,
                        resolution_data: Dict,
                        resolved_by_user_id: int) -> bool:
        """解决冲突"""
        try:
            conflict = db.query(Conflict).filter(Conflict.id == conflict_id).first()
            if not conflict:
                return False
            
            conflict.mark_resolved(resolution_data, resolved_by_user_id)
            db.commit()
            
            logger.info(f"冲突已解决: {conflict_id}")
            return True
            
        except Exception as e:
            logger.error(f"解决冲突失败: {str(e)}")
            return False
    
    def get_conflict_statistics(self, db: Session, project_id: int) -> Dict:
        """获取冲突统计信息"""
        try:
            conflicts = db.query(Conflict).filter(Conflict.project_id == project_id).all()
            
            total_conflicts = len(conflicts)
            resolved_conflicts = len([c for c in conflicts if c.is_resolved])
            critical_conflicts = len([c for c in conflicts if c.severity == 'critical'])
            high_conflicts = len([c for c in conflicts if c.severity == 'high'])
            
            # 按类型统计
            type_stats = {}
            for conflict in conflicts:
                conflict_type = conflict.conflict_type.category.value if conflict.conflict_type else 'unknown'
                type_stats[conflict_type] = type_stats.get(conflict_type, 0) + 1
            
            return {
                'total_conflicts': total_conflicts,
                'resolved_conflicts': resolved_conflicts,
                'unresolved_conflicts': total_conflicts - resolved_conflicts,
                'critical_conflicts': critical_conflicts,
                'high_conflicts': high_conflicts,
                'resolution_rate': (resolved_conflicts / total_conflicts * 100) if total_conflicts > 0 else 0,
                'type_distribution': type_stats
            }
            
        except Exception as e:
            logger.error(f"获取冲突统计失败: {str(e)}")
            return {}
    
    def generate_conflict_report(self, db: Session, project_id: int) -> Dict:
        """生成冲突报告"""
        try:
            conflicts = db.query(Conflict).filter(Conflict.project_id == project_id).all()
            statistics = self.get_conflict_statistics(db, project_id)
            
            # 生成报告
            report = {
                'project_id': project_id,
                'generated_at': datetime.utcnow().isoformat(),
                'summary': statistics,
                'conflicts': [conflict.export_summary() for conflict in conflicts],
                'recommendations': self._generate_recommendations(conflicts)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成冲突报告失败: {str(e)}")
            return {}
    
    def _generate_recommendations(self, conflicts: List[Conflict]) -> List[str]:
        """生成建议"""
        recommendations = []
        
        critical_count = len([c for c in conflicts if c.severity == 'critical'])
        if critical_count > 0:
            recommendations.append(f"发现{critical_count}个严重冲突，建议优先处理")
        
        terrain_conflicts = len([c for c in conflicts if c.conflict_type and 
                               c.conflict_type.category.value == 'terrain_conflict'])
        if terrain_conflicts > 5:
            recommendations.append("地形冲突较多，建议重新优化道路线形")
        
        unresolved_count = len([c for c in conflicts if not c.is_resolved])
        if unresolved_count > 10:
            recommendations.append("未解决冲突较多，建议制定系统性解决方案")
        
        return recommendations
