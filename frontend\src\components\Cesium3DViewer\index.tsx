import React, { useEffect, useRef, useState } from 'react'
import { Card, Button, Space, Slider, Switch, Select, message, Tooltip } from 'antd'
import { 
  EyeOutlined, EyeInvisibleOutlined, SettingOutlined,
  FullscreenOutlined, ReloadOutlined, CameraOutlined,
  EnvironmentOutlined, LineChartOutlined
} from '@ant-design/icons'
import * as Cesium from 'cesium'
import './index.css'

// 设置Cesium的访问令牌
Cesium.Ion.defaultAccessToken = 'your-cesium-ion-access-token'

interface TerrainData {
  bounds: [number, number, number, number] // [west, south, east, north]
  elevationData: number[][]
  resolution: number
}

interface RoadData {
  id: string
  name: string
  coordinates: [number, number, number][]
  width: number
  type: string
}

interface Cesium3DViewerProps {
  terrainData?: TerrainData
  roadData?: RoadData[]
  projectBounds?: [number, number, number, number]
  onCameraChange?: (position: any) => void
  onEntityClick?: (entity: any) => void
}

const Cesium3DViewer: React.FC<Cesium3DViewerProps> = ({
  terrainData,
  roadData = [],
  projectBounds,
  onCameraChange,
  onEntityClick
}) => {
  const cesiumContainer = useRef<HTMLDivElement>(null)
  const viewerRef = useRef<Cesium.Viewer | null>(null)
  const [isLoaded, setIsLoaded] = useState(false)
  const [showTerrain, setShowTerrain] = useState(true)
  const [showRoads, setShowRoads] = useState(true)
  const [terrainExaggeration, setTerrainExaggeration] = useState(1.0)
  const [viewMode, setViewMode] = useState<'3d' | '2d'>('3d')
  const [loading, setLoading] = useState(true)

  // 初始化Cesium查看器
  useEffect(() => {
    if (!cesiumContainer.current) return

    try {
      // 创建Cesium查看器
      const viewer = new Cesium.Viewer(cesiumContainer.current, {
        terrainProvider: Cesium.createWorldTerrain(),
        imageryProvider: new Cesium.OpenStreetMapImageryProvider({
          url: 'https://a.tile.openstreetmap.org/'
        }),
        baseLayerPicker: false,
        geocoder: false,
        homeButton: false,
        sceneModePicker: false,
        navigationHelpButton: false,
        animation: false,
        timeline: false,
        fullscreenButton: false,
        vrButton: false,
        scene3DOnly: true,
        requestRenderMode: true,
        maximumRenderTimeChange: Infinity
      })

      // 设置初始相机位置
      if (projectBounds) {
        const [west, south, east, north] = projectBounds
        const centerLon = (west + east) / 2
        const centerLat = (south + north) / 2
        
        viewer.camera.setView({
          destination: Cesium.Cartesian3.fromDegrees(centerLon, centerLat, 5000),
          orientation: {
            heading: 0.0,
            pitch: -Cesium.Math.PI_OVER_FOUR,
            roll: 0.0
          }
        })
      }

      // 启用深度测试
      viewer.scene.globe.depthTestAgainstTerrain = true

      // 设置相机变化监听
      viewer.camera.changed.addEventListener(() => {
        if (onCameraChange) {
          const position = viewer.camera.position
          const cartographic = Cesium.Cartographic.fromCartesian(position)
          onCameraChange({
            longitude: Cesium.Math.toDegrees(cartographic.longitude),
            latitude: Cesium.Math.toDegrees(cartographic.latitude),
            height: cartographic.height
          })
        }
      })

      // 设置实体点击监听
      viewer.selectedEntityChanged.addEventListener((entity: any) => {
        if (entity && onEntityClick) {
          onEntityClick(entity)
        }
      })

      viewerRef.current = viewer
      setIsLoaded(true)
      setLoading(false)

    } catch (error) {
      console.error('Cesium初始化失败:', error)
      message.error('3D查看器初始化失败')
      setLoading(false)
    }

    // 清理函数
    return () => {
      if (viewerRef.current) {
        viewerRef.current.destroy()
        viewerRef.current = null
      }
    }
  }, [])

  // 加载地形数据
  useEffect(() => {
    if (!viewerRef.current || !terrainData || !isLoaded) return

    try {
      const viewer = viewerRef.current
      
      // 创建自定义地形提供者
      if (terrainData.elevationData && terrainData.elevationData.length > 0) {
        // 这里可以实现自定义地形数据的加载
        // 由于Cesium的地形数据格式比较复杂，这里使用简化的实现
        console.log('加载地形数据:', terrainData)
      }

      // 设置地形夸张系数
      viewer.scene.globe.terrainExaggeration = terrainExaggeration

    } catch (error) {
      console.error('地形数据加载失败:', error)
      message.error('地形数据加载失败')
    }
  }, [terrainData, isLoaded, terrainExaggeration])

  // 加载道路数据
  useEffect(() => {
    if (!viewerRef.current || !roadData.length || !isLoaded) return

    try {
      const viewer = viewerRef.current
      
      // 清除现有的道路实体
      const existingRoads = viewer.entities.values.filter((entity: any) => 
        entity.name && entity.name.startsWith('road_')
      )
      existingRoads.forEach(entity => viewer.entities.remove(entity))

      if (!showRoads) return

      // 添加道路实体
      roadData.forEach(road => {
        const positions = road.coordinates.map(coord => 
          Cesium.Cartesian3.fromDegrees(coord[0], coord[1], coord[2] || 0)
        )

        // 创建道路中心线
        viewer.entities.add({
          name: `road_${road.id}`,
          polyline: {
            positions: positions,
            width: 5,
            material: Cesium.Color.YELLOW,
            clampToGround: true,
            outline: true,
            outlineColor: Cesium.Color.BLACK
          },
          description: `道路: ${road.name}<br/>类型: ${road.type}<br/>宽度: ${road.width}m`
        })

        // 创建道路表面（如果有宽度信息）
        if (road.width > 0) {
          const corridor = viewer.entities.add({
            name: `road_surface_${road.id}`,
            corridor: {
              positions: positions,
              width: road.width,
              material: Cesium.Color.GRAY.withAlpha(0.7),
              outline: true,
              outlineColor: Cesium.Color.DARKGRAY,
              extrudedHeight: 0.5
            }
          })
        }
      })

    } catch (error) {
      console.error('道路数据加载失败:', error)
      message.error('道路数据加载失败')
    }
  }, [roadData, isLoaded, showRoads])

  // 切换视图模式
  const toggleViewMode = () => {
    if (!viewerRef.current) return

    const viewer = viewerRef.current
    if (viewMode === '3d') {
      viewer.scene.mode = Cesium.SceneMode.SCENE2D
      setViewMode('2d')
    } else {
      viewer.scene.mode = Cesium.SceneMode.SCENE3D
      setViewMode('3d')
    }
  }

  // 重置相机视角
  const resetCamera = () => {
    if (!viewerRef.current || !projectBounds) return

    const viewer = viewerRef.current
    const [west, south, east, north] = projectBounds
    const centerLon = (west + east) / 2
    const centerLat = (south + north) / 2
    
    viewer.camera.flyTo({
      destination: Cesium.Cartesian3.fromDegrees(centerLon, centerLat, 5000),
      orientation: {
        heading: 0.0,
        pitch: -Cesium.Math.PI_OVER_FOUR,
        roll: 0.0
      },
      duration: 2.0
    })
  }

  // 截图功能
  const takeScreenshot = () => {
    if (!viewerRef.current) return

    try {
      const viewer = viewerRef.current
      viewer.render()
      
      const canvas = viewer.scene.canvas
      const image = canvas.toDataURL('image/png')
      
      // 创建下载链接
      const link = document.createElement('a')
      link.download = `3d_view_${new Date().getTime()}.png`
      link.href = image
      link.click()
      
      message.success('截图已保存')
    } catch (error) {
      console.error('截图失败:', error)
      message.error('截图失败')
    }
  }

  // 全屏切换
  const toggleFullscreen = () => {
    if (!cesiumContainer.current) return

    if (!document.fullscreenElement) {
      cesiumContainer.current.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }

  if (loading) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <div>正在加载3D查看器...</div>
        </div>
      </Card>
    )
  }

  return (
    <div className="cesium-3d-viewer">
      {/* 控制面板 */}
      <Card 
        size="small" 
        className="cesium-controls"
        style={{ 
          position: 'absolute', 
          top: '10px', 
          right: '10px', 
          zIndex: 1000,
          width: '300px'
        }}
      >
        <Space direction="vertical" style={{ width: '100%' }}>
          {/* 视图控制 */}
          <div>
            <Space>
              <Tooltip title="重置视角">
                <Button 
                  icon={<EnvironmentOutlined />} 
                  size="small"
                  onClick={resetCamera}
                />
              </Tooltip>
              <Tooltip title="切换2D/3D">
                <Button 
                  icon={<LineChartOutlined />} 
                  size="small"
                  onClick={toggleViewMode}
                >
                  {viewMode.toUpperCase()}
                </Button>
              </Tooltip>
              <Tooltip title="截图">
                <Button 
                  icon={<CameraOutlined />} 
                  size="small"
                  onClick={takeScreenshot}
                />
              </Tooltip>
              <Tooltip title="全屏">
                <Button 
                  icon={<FullscreenOutlined />} 
                  size="small"
                  onClick={toggleFullscreen}
                />
              </Tooltip>
            </Space>
          </div>

          {/* 图层控制 */}
          <div>
            <Space direction="vertical" style={{ width: '100%' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>显示地形</span>
                <Switch 
                  checked={showTerrain} 
                  onChange={setShowTerrain}
                  size="small"
                />
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                <span>显示道路</span>
                <Switch 
                  checked={showRoads} 
                  onChange={setShowRoads}
                  size="small"
                />
              </div>
            </Space>
          </div>

          {/* 地形夸张 */}
          <div>
            <div style={{ marginBottom: '8px' }}>地形夸张: {terrainExaggeration.toFixed(1)}x</div>
            <Slider
              min={0.1}
              max={5.0}
              step={0.1}
              value={terrainExaggeration}
              onChange={setTerrainExaggeration}
            />
          </div>
        </Space>
      </Card>

      {/* Cesium容器 */}
      <div 
        ref={cesiumContainer} 
        className="cesium-container"
        style={{ 
          width: '100%', 
          height: '600px',
          position: 'relative'
        }}
      />
    </div>
  )
}

export default Cesium3DViewer
