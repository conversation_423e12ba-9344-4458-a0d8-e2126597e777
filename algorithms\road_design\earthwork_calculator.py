"""
土方量计算器
"""
import numpy as np
import math
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class EarthworkSection:
    """土方断面"""
    chainage: float
    cut_area: float
    fill_area: float
    center_height: float
    road_width: float


@dataclass
class EarthworkSummary:
    """土方量汇总"""
    total_cut_volume: float
    total_fill_volume: float
    net_volume: float  # 净土方量(挖-填)
    balance_ratio: float  # 平衡比例
    sections_count: int
    average_cut_area: float
    average_fill_area: float


class EarthworkCalculator:
    """土方量计算器"""
    
    def __init__(self):
        self.expansion_factor = 1.2  # 土方松散系数
        self.compaction_factor = 0.9  # 土方压实系数
    
    def calculate_cross_section_area(self, 
                                   center_elevation: float,
                                   ground_profile: List[Tuple[float, float]],
                                   road_width: float = 6.0,
                                   side_slope_cut: float = 1.0,
                                   side_slope_fill: float = 1.5,
                                   cross_slope: float = 2.0) -> Tuple[float, float]:
        """计算横断面挖填面积"""
        try:
            cut_area = 0.0
            fill_area = 0.0
            
            # 道路边缘高程
            half_width = road_width / 2
            left_edge_elev = center_elevation + half_width * (cross_slope / 100)
            right_edge_elev = center_elevation - half_width * (cross_slope / 100)
            
            # 处理左侧
            left_cut, left_fill = self._calculate_side_area(
                left_edge_elev, ground_profile, -half_width, 
                side_slope_cut, side_slope_fill, True
            )
            
            # 处理右侧
            right_cut, right_fill = self._calculate_side_area(
                right_edge_elev, ground_profile, half_width,
                side_slope_cut, side_slope_fill, False
            )
            
            cut_area = left_cut + right_cut
            fill_area = left_fill + right_fill
            
            return cut_area, fill_area
            
        except Exception as e:
            logger.error(f"计算横断面面积失败: {str(e)}")
            return 0.0, 0.0
    
    def _calculate_side_area(self, 
                           edge_elevation: float,
                           ground_profile: List[Tuple[float, float]],
                           start_offset: float,
                           side_slope_cut: float,
                           side_slope_fill: float,
                           is_left_side: bool) -> Tuple[float, float]:
        """计算单侧挖填面积"""
        cut_area = 0.0
        fill_area = 0.0
        
        try:
            # 按距离排序地面线点
            if is_left_side:
                # 左侧：从路边向外(负方向)
                profile_points = [(offset, elev) for offset, elev in ground_profile 
                                if offset <= start_offset]
                profile_points.sort(key=lambda x: x[0], reverse=True)
            else:
                # 右侧：从路边向外(正方向)
                profile_points = [(offset, elev) for offset, elev in ground_profile 
                                if offset >= start_offset]
                profile_points.sort(key=lambda x: x[0])
            
            if not profile_points:
                return 0.0, 0.0
            
            # 计算每个区间的面积
            prev_offset = start_offset
            prev_road_elev = edge_elevation
            
            for offset, ground_elev in profile_points:
                # 计算当前点的路面高程
                distance = abs(offset - start_offset)
                
                # 判断挖填类型
                if prev_road_elev > ground_elev:
                    # 挖方区域
                    slope_ratio = 1.0 / side_slope_cut  # 边坡比例
                    road_elev = edge_elevation + distance * slope_ratio
                    
                    if road_elev > ground_elev:
                        # 计算梯形面积
                        h1 = prev_road_elev - ground_elev
                        h2 = road_elev - ground_elev
                        width = abs(offset - prev_offset)
                        area = width * (h1 + h2) / 2
                        cut_area += area
                else:
                    # 填方区域
                    slope_ratio = 1.0 / side_slope_fill
                    road_elev = edge_elevation - distance * slope_ratio
                    
                    if road_elev < ground_elev:
                        # 计算梯形面积
                        h1 = ground_elev - prev_road_elev
                        h2 = ground_elev - road_elev
                        width = abs(offset - prev_offset)
                        area = width * (h1 + h2) / 2
                        fill_area += area
                
                prev_offset = offset
                prev_road_elev = road_elev
            
            return cut_area, fill_area
            
        except Exception as e:
            logger.error(f"计算单侧面积失败: {str(e)}")
            return 0.0, 0.0
    
    def calculate_earthwork_volume(self, 
                                 sections: List[EarthworkSection]) -> EarthworkSummary:
        """计算土方量"""
        try:
            if len(sections) < 2:
                raise ValueError("至少需要2个断面才能计算土方量")
            
            total_cut_volume = 0.0
            total_fill_volume = 0.0
            
            # 使用平均断面法计算体积
            for i in range(len(sections) - 1):
                section1 = sections[i]
                section2 = sections[i + 1]
                
                # 计算距离
                distance = section2.chainage - section1.chainage
                
                # 平均面积
                avg_cut_area = (section1.cut_area + section2.cut_area) / 2
                avg_fill_area = (section1.fill_area + section2.fill_area) / 2
                
                # 计算体积
                cut_volume = avg_cut_area * distance
                fill_volume = avg_fill_area * distance
                
                total_cut_volume += cut_volume
                total_fill_volume += fill_volume
            
            # 考虑土方系数
            adjusted_cut_volume = total_cut_volume * self.expansion_factor
            adjusted_fill_volume = total_fill_volume / self.compaction_factor
            
            # 计算净土方量
            net_volume = adjusted_cut_volume - adjusted_fill_volume
            
            # 计算平衡比例
            if adjusted_cut_volume > 0:
                balance_ratio = min(adjusted_fill_volume / adjusted_cut_volume, 1.0)
            else:
                balance_ratio = 0.0
            
            # 计算平均面积
            avg_cut_area = sum(s.cut_area for s in sections) / len(sections)
            avg_fill_area = sum(s.fill_area for s in sections) / len(sections)
            
            return EarthworkSummary(
                total_cut_volume=adjusted_cut_volume,
                total_fill_volume=adjusted_fill_volume,
                net_volume=net_volume,
                balance_ratio=balance_ratio,
                sections_count=len(sections),
                average_cut_area=avg_cut_area,
                average_fill_area=avg_fill_area
            )
            
        except Exception as e:
            logger.error(f"计算土方量失败: {str(e)}")
            raise
    
    def optimize_earthwork_balance(self, 
                                 sections: List[EarthworkSection],
                                 max_haul_distance: float = 500.0) -> Dict:
        """优化土方调配"""
        try:
            # 识别挖方和填方区域
            cut_sections = []
            fill_sections = []
            
            for i, section in enumerate(sections):
                if section.cut_area > section.fill_area:
                    surplus = (section.cut_area - section.fill_area) * \
                             (sections[i+1].chainage - section.chainage if i < len(sections)-1 else 10)
                    cut_sections.append({
                        'index': i,
                        'chainage': section.chainage,
                        'surplus': surplus
                    })
                elif section.fill_area > section.cut_area:
                    deficit = (section.fill_area - section.cut_area) * \
                             (sections[i+1].chainage - section.chainage if i < len(sections)-1 else 10)
                    fill_sections.append({
                        'index': i,
                        'chainage': section.chainage,
                        'deficit': deficit
                    })
            
            # 计算调配方案
            haul_plan = []
            total_haul_volume = 0.0
            total_haul_distance = 0.0
            
            for cut_section in cut_sections:
                remaining_surplus = cut_section['surplus']
                
                for fill_section in fill_sections:
                    if remaining_surplus <= 0 or fill_section['deficit'] <= 0:
                        continue
                    
                    # 计算运距
                    haul_distance = abs(fill_section['chainage'] - cut_section['chainage'])
                    
                    if haul_distance <= max_haul_distance:
                        # 计算调配量
                        haul_volume = min(remaining_surplus, fill_section['deficit'])
                        
                        haul_plan.append({
                            'from_chainage': cut_section['chainage'],
                            'to_chainage': fill_section['chainage'],
                            'volume': haul_volume,
                            'distance': haul_distance,
                            'cost': haul_volume * haul_distance  # 简化成本计算
                        })
                        
                        remaining_surplus -= haul_volume
                        fill_section['deficit'] -= haul_volume
                        total_haul_volume += haul_volume
                        total_haul_distance += haul_volume * haul_distance
            
            # 计算平均运距
            avg_haul_distance = total_haul_distance / total_haul_volume if total_haul_volume > 0 else 0
            
            return {
                'haul_plan': haul_plan,
                'total_haul_volume': total_haul_volume,
                'average_haul_distance': avg_haul_distance,
                'total_cost': sum(plan['cost'] for plan in haul_plan),
                'balance_efficiency': total_haul_volume / sum(s.cut_area for s in sections) if sections else 0
            }
            
        except Exception as e:
            logger.error(f"优化土方调配失败: {str(e)}")
            return {}
    
    def calculate_slope_stability(self, 
                                cut_height: float,
                                soil_type: str = "clay",
                                slope_angle: float = 45.0) -> Dict:
        """计算边坡稳定性"""
        try:
            # 土质参数(简化)
            soil_params = {
                "clay": {"cohesion": 20, "friction_angle": 15, "unit_weight": 18},
                "sand": {"cohesion": 0, "friction_angle": 30, "unit_weight": 16},
                "rock": {"cohesion": 100, "friction_angle": 35, "unit_weight": 22}
            }
            
            params = soil_params.get(soil_type, soil_params["clay"])
            
            # 简化的边坡稳定性分析
            # 使用无限边坡法计算安全系数
            slope_rad = math.radians(slope_angle)
            
            # 安全系数计算
            numerator = params["cohesion"] + params["unit_weight"] * cut_height * math.cos(slope_rad) * math.tan(math.radians(params["friction_angle"]))
            denominator = params["unit_weight"] * cut_height * math.sin(slope_rad)
            
            if denominator > 0:
                safety_factor = numerator / denominator
            else:
                safety_factor = float('inf')
            
            # 稳定性评估
            if safety_factor >= 1.5:
                stability = "stable"
            elif safety_factor >= 1.2:
                stability = "marginally_stable"
            else:
                stability = "unstable"
            
            # 推荐边坡角度
            if safety_factor < 1.2:
                recommended_angle = slope_angle * 0.8  # 减缓边坡
            else:
                recommended_angle = slope_angle
            
            return {
                'safety_factor': safety_factor,
                'stability': stability,
                'current_slope_angle': slope_angle,
                'recommended_slope_angle': recommended_angle,
                'soil_type': soil_type,
                'cut_height': cut_height
            }
            
        except Exception as e:
            logger.error(f"计算边坡稳定性失败: {str(e)}")
            return {}
    
    def generate_earthwork_report(self, 
                                summary: EarthworkSummary,
                                haul_optimization: Dict = None) -> Dict:
        """生成土方量报告"""
        try:
            report = {
                'summary': {
                    'total_cut_volume': round(summary.total_cut_volume, 2),
                    'total_fill_volume': round(summary.total_fill_volume, 2),
                    'net_volume': round(summary.net_volume, 2),
                    'balance_ratio': round(summary.balance_ratio * 100, 1),
                    'sections_count': summary.sections_count
                },
                'analysis': {
                    'volume_balance': 'surplus' if summary.net_volume > 0 else 'deficit' if summary.net_volume < 0 else 'balanced',
                    'balance_efficiency': round(summary.balance_ratio * 100, 1),
                    'average_cut_area': round(summary.average_cut_area, 2),
                    'average_fill_area': round(summary.average_fill_area, 2)
                }
            }
            
            if haul_optimization:
                report['haul_optimization'] = {
                    'total_haul_volume': round(haul_optimization.get('total_haul_volume', 0), 2),
                    'average_haul_distance': round(haul_optimization.get('average_haul_distance', 0), 1),
                    'total_cost': round(haul_optimization.get('total_cost', 0), 2),
                    'balance_efficiency': round(haul_optimization.get('balance_efficiency', 0) * 100, 1)
                }
            
            # 添加建议
            recommendations = []
            
            if summary.balance_ratio < 0.7:
                recommendations.append("土方不平衡较大，建议优化线形设计")
            
            if summary.net_volume > 1000:
                recommendations.append("挖方量较大，考虑降低路面标高")
            elif summary.net_volume < -1000:
                recommendations.append("填方量较大，考虑提高路面标高")
            
            if haul_optimization and haul_optimization.get('average_haul_distance', 0) > 300:
                recommendations.append("平均运距较大，建议优化调配方案")
            
            report['recommendations'] = recommendations
            
            return report
            
        except Exception as e:
            logger.error(f"生成土方量报告失败: {str(e)}")
            return {}
