"""
道路剖切分析核心算法
"""
import numpy as np
import math
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)


class ProfileType(Enum):
    """剖面类型枚举"""
    LONGITUDINAL = "longitudinal"
    CROSS_SECTION = "cross_section"
    CUSTOM = "custom"


@dataclass
class ProfilePoint:
    """剖面点"""
    chainage: float
    offset: float
    elevation: float
    ground_elevation: float
    cut_fill_height: float
    cut_fill_type: str  # 'cut' or 'fill'


@dataclass
class LongitudinalProfile:
    """纵断面数据"""
    start_chainage: float
    end_chainage: float
    total_length: float
    points: List[ProfilePoint]
    max_gradient: float
    min_gradient: float
    average_gradient: float
    total_cut_volume: float
    total_fill_volume: float


@dataclass
class CrossSection:
    """横断面数据"""
    chainage: float
    center_elevation: float
    road_width: float
    left_points: List[Tuple[float, float]]  # (offset, elevation)
    right_points: List[Tuple[float, float]]
    cut_area: float
    fill_area: float
    cut_volume_per_meter: float
    fill_volume_per_meter: float


@dataclass
class VolumeAnalysis:
    """工程量分析"""
    total_cut_volume: float
    total_fill_volume: float
    net_volume: float
    balance_ratio: float
    cut_sections: List[Dict]
    fill_sections: List[Dict]
    volume_distribution: Dict[str, float]


class ProfileAnalyzer:
    """剖切分析器主类"""
    
    def __init__(self):
        self.analysis_interval = 10.0  # 分析间隔(米)
        self.cross_section_interval = 20.0  # 横断面间隔(米)
        self.expansion_factor = 1.2  # 土方松散系数
        self.compaction_factor = 0.9  # 土方压实系数
        
    def analyze_road_profile(self, 
                           road_points: List[Tuple[float, float, float]],
                           chainages: List[float],
                           road_width: float = 6.0,
                           terrain_data: np.ndarray = None) -> Dict[str, Any]:
        """分析道路剖面"""
        try:
            logger.info("开始道路剖面分析")
            
            # 生成纵断面
            longitudinal_profile = self._generate_longitudinal_profile(
                road_points, chainages, terrain_data
            )
            
            # 生成横断面
            cross_sections = self._generate_cross_sections(
                road_points, chainages, road_width, terrain_data
            )
            
            # 计算工程量
            volume_analysis = self._calculate_volumes(
                longitudinal_profile, cross_sections
            )
            
            # 生成分析报告
            analysis_report = self._generate_analysis_report(
                longitudinal_profile, cross_sections, volume_analysis
            )
            
            result = {
                'longitudinal_profile': longitudinal_profile,
                'cross_sections': cross_sections,
                'volume_analysis': volume_analysis,
                'analysis_report': analysis_report
            }
            
            logger.info("道路剖面分析完成")
            return result
            
        except Exception as e:
            logger.error(f"道路剖面分析失败: {str(e)}")
            raise
    
    def _generate_longitudinal_profile(self, 
                                     road_points: List[Tuple[float, float, float]],
                                     chainages: List[float],
                                     terrain_data: np.ndarray = None) -> LongitudinalProfile:
        """生成纵断面"""
        try:
            profile_points = []
            gradients = []
            cut_volumes = []
            fill_volumes = []
            
            for i, (x, y, z) in enumerate(road_points):
                chainage = chainages[i] if i < len(chainages) else 0
                
                # 获取地面高程
                ground_elevation = self._get_ground_elevation(x, y, terrain_data)
                if ground_elevation is None:
                    ground_elevation = z  # 如果没有地形数据，使用道路高程
                
                # 计算挖填高度
                cut_fill_height = z - ground_elevation
                cut_fill_type = 'cut' if cut_fill_height > 0 else 'fill'
                
                profile_point = ProfilePoint(
                    chainage=chainage,
                    offset=0.0,  # 中心线偏移为0
                    elevation=z,
                    ground_elevation=ground_elevation,
                    cut_fill_height=abs(cut_fill_height),
                    cut_fill_type=cut_fill_type
                )
                
                profile_points.append(profile_point)
                
                # 计算坡度
                if i > 0:
                    prev_point = road_points[i-1]
                    distance = math.sqrt((x - prev_point[0])**2 + (y - prev_point[1])**2)
                    if distance > 0:
                        gradient = (z - prev_point[2]) / distance * 100  # 百分比坡度
                        gradients.append(gradient)
                
                # 估算单位长度土方量
                if cut_fill_type == 'cut':
                    cut_volumes.append(cut_fill_height * 6.0)  # 简化计算
                    fill_volumes.append(0.0)
                else:
                    cut_volumes.append(0.0)
                    fill_volumes.append(cut_fill_height * 6.0)
            
            # 计算统计值
            max_gradient = max(gradients) if gradients else 0.0
            min_gradient = min(gradients) if gradients else 0.0
            avg_gradient = sum(gradients) / len(gradients) if gradients else 0.0
            
            total_cut_volume = sum(cut_volumes) * self.analysis_interval
            total_fill_volume = sum(fill_volumes) * self.analysis_interval
            
            return LongitudinalProfile(
                start_chainage=chainages[0] if chainages else 0.0,
                end_chainage=chainages[-1] if chainages else 0.0,
                total_length=chainages[-1] - chainages[0] if len(chainages) > 1 else 0.0,
                points=profile_points,
                max_gradient=max_gradient,
                min_gradient=min_gradient,
                average_gradient=avg_gradient,
                total_cut_volume=total_cut_volume,
                total_fill_volume=total_fill_volume
            )
            
        except Exception as e:
            logger.error(f"生成纵断面失败: {str(e)}")
            raise
    
    def _generate_cross_sections(self, 
                               road_points: List[Tuple[float, float, float]],
                               chainages: List[float],
                               road_width: float,
                               terrain_data: np.ndarray = None) -> List[CrossSection]:
        """生成横断面"""
        try:
            cross_sections = []
            
            # 按间隔生成横断面
            for i in range(0, len(road_points), max(1, int(self.cross_section_interval / self.analysis_interval))):
                if i >= len(road_points):
                    break
                
                x, y, z = road_points[i]
                chainage = chainages[i] if i < len(chainages) else 0
                
                # 计算横断面方向
                direction = self._calculate_cross_direction(road_points, i)
                
                # 生成横断面点
                left_points, right_points = self._generate_cross_section_points(
                    x, y, z, direction, road_width, terrain_data
                )
                
                # 计算横断面面积
                cut_area, fill_area = self._calculate_cross_section_areas(
                    left_points, right_points, z, road_width
                )
                
                cross_section = CrossSection(
                    chainage=chainage,
                    center_elevation=z,
                    road_width=road_width,
                    left_points=left_points,
                    right_points=right_points,
                    cut_area=cut_area,
                    fill_area=fill_area,
                    cut_volume_per_meter=cut_area,
                    fill_volume_per_meter=fill_area
                )
                
                cross_sections.append(cross_section)
            
            return cross_sections
            
        except Exception as e:
            logger.error(f"生成横断面失败: {str(e)}")
            return []
    
    def _calculate_cross_direction(self, 
                                 road_points: List[Tuple[float, float, float]],
                                 index: int) -> Tuple[float, float]:
        """计算横断面方向"""
        try:
            if index == 0:
                # 第一个点，使用向前方向
                if len(road_points) > 1:
                    dx = road_points[1][0] - road_points[0][0]
                    dy = road_points[1][1] - road_points[0][1]
                else:
                    dx, dy = 1.0, 0.0
            elif index == len(road_points) - 1:
                # 最后一个点，使用向后方向
                dx = road_points[index][0] - road_points[index-1][0]
                dy = road_points[index][1] - road_points[index-1][1]
            else:
                # 中间点，使用前后点的平均方向
                dx1 = road_points[index][0] - road_points[index-1][0]
                dy1 = road_points[index][1] - road_points[index-1][1]
                dx2 = road_points[index+1][0] - road_points[index][0]
                dy2 = road_points[index+1][1] - road_points[index][1]
                dx = (dx1 + dx2) / 2
                dy = (dy1 + dy2) / 2
            
            # 归一化
            length = math.sqrt(dx**2 + dy**2)
            if length > 0:
                dx /= length
                dy /= length
            
            # 计算垂直方向（横断面方向）
            cross_dx = -dy
            cross_dy = dx
            
            return cross_dx, cross_dy
            
        except Exception as e:
            logger.error(f"计算横断面方向失败: {str(e)}")
            return 1.0, 0.0
    
    def _generate_cross_section_points(self, 
                                     center_x: float,
                                     center_y: float,
                                     center_z: float,
                                     direction: Tuple[float, float],
                                     road_width: float,
                                     terrain_data: np.ndarray = None) -> Tuple[List[Tuple[float, float]], List[Tuple[float, float]]]:
        """生成横断面点"""
        try:
            dx, dy = direction
            half_width = road_width / 2
            
            left_points = []
            right_points = []
            
            # 生成左侧点
            for offset in np.arange(0, 30, 2):  # 左侧30米范围，每2米一个点
                x = center_x - dx * offset
                y = center_y - dy * offset
                ground_elevation = self._get_ground_elevation(x, y, terrain_data)
                if ground_elevation is not None:
                    left_points.append((-offset, ground_elevation))
            
            # 生成右侧点
            for offset in np.arange(0, 30, 2):  # 右侧30米范围，每2米一个点
                x = center_x + dx * offset
                y = center_y + dy * offset
                ground_elevation = self._get_ground_elevation(x, y, terrain_data)
                if ground_elevation is not None:
                    right_points.append((offset, ground_elevation))
            
            return left_points, right_points
            
        except Exception as e:
            logger.error(f"生成横断面点失败: {str(e)}")
            return [], []
    
    def _calculate_cross_section_areas(self, 
                                     left_points: List[Tuple[float, float]],
                                     right_points: List[Tuple[float, float]],
                                     road_elevation: float,
                                     road_width: float) -> Tuple[float, float]:
        """计算横断面挖填面积"""
        try:
            cut_area = 0.0
            fill_area = 0.0
            half_width = road_width / 2
            
            # 计算左侧面积
            for i, (offset, ground_elev) in enumerate(left_points):
                if abs(offset) <= half_width:
                    # 路面范围内
                    road_elev = road_elevation
                else:
                    # 边坡范围
                    slope_ratio = 1.5  # 边坡比例 1:1.5
                    slope_height = abs(offset) - half_width
                    if ground_elev > road_elevation:
                        # 挖方边坡
                        road_elev = road_elevation + slope_height / slope_ratio
                    else:
                        # 填方边坡
                        road_elev = road_elevation - slope_height / slope_ratio
                
                # 计算挖填面积
                if i > 0:
                    prev_offset, prev_ground = left_points[i-1]
                    width = abs(offset - prev_offset)
                    
                    if road_elev > ground_elev:
                        # 挖方
                        h1 = road_elev - ground_elev
                        h2 = road_elevation - prev_ground if i == 1 else left_points[i-1][1]
                        cut_area += width * (h1 + h2) / 2
                    else:
                        # 填方
                        h1 = ground_elev - road_elev
                        h2 = prev_ground - road_elevation if i == 1 else left_points[i-1][1]
                        fill_area += width * (h1 + h2) / 2
            
            # 计算右侧面积（类似左侧）
            for i, (offset, ground_elev) in enumerate(right_points):
                if abs(offset) <= half_width:
                    road_elev = road_elevation
                else:
                    slope_ratio = 1.5
                    slope_height = abs(offset) - half_width
                    if ground_elev > road_elevation:
                        road_elev = road_elevation + slope_height / slope_ratio
                    else:
                        road_elev = road_elevation - slope_height / slope_ratio
                
                if i > 0:
                    prev_offset, prev_ground = right_points[i-1]
                    width = abs(offset - prev_offset)
                    
                    if road_elev > ground_elev:
                        h1 = road_elev - ground_elev
                        h2 = road_elevation - prev_ground if i == 1 else right_points[i-1][1]
                        cut_area += width * (h1 + h2) / 2
                    else:
                        h1 = ground_elev - road_elev
                        h2 = prev_ground - road_elevation if i == 1 else right_points[i-1][1]
                        fill_area += width * (h1 + h2) / 2
            
            return cut_area, fill_area
            
        except Exception as e:
            logger.error(f"计算横断面面积失败: {str(e)}")
            return 0.0, 0.0
    
    def _calculate_volumes(self, 
                         longitudinal_profile: LongitudinalProfile,
                         cross_sections: List[CrossSection]) -> VolumeAnalysis:
        """计算工程量"""
        try:
            total_cut_volume = 0.0
            total_fill_volume = 0.0
            cut_sections = []
            fill_sections = []
            
            # 使用平均断面法计算体积
            for i in range(len(cross_sections) - 1):
                section1 = cross_sections[i]
                section2 = cross_sections[i + 1]
                
                # 计算距离
                distance = section2.chainage - section1.chainage
                
                # 平均面积
                avg_cut_area = (section1.cut_area + section2.cut_area) / 2
                avg_fill_area = (section1.fill_area + section2.fill_area) / 2
                
                # 计算体积
                cut_volume = avg_cut_area * distance
                fill_volume = avg_fill_area * distance
                
                total_cut_volume += cut_volume
                total_fill_volume += fill_volume
                
                if cut_volume > 0:
                    cut_sections.append({
                        'start_chainage': section1.chainage,
                        'end_chainage': section2.chainage,
                        'volume': cut_volume,
                        'average_area': avg_cut_area
                    })
                
                if fill_volume > 0:
                    fill_sections.append({
                        'start_chainage': section1.chainage,
                        'end_chainage': section2.chainage,
                        'volume': fill_volume,
                        'average_area': avg_fill_area
                    })
            
            # 考虑土方系数
            adjusted_cut_volume = total_cut_volume * self.expansion_factor
            adjusted_fill_volume = total_fill_volume / self.compaction_factor
            
            # 计算净土方量和平衡比例
            net_volume = adjusted_cut_volume - adjusted_fill_volume
            balance_ratio = min(adjusted_fill_volume / adjusted_cut_volume, 1.0) if adjusted_cut_volume > 0 else 0.0
            
            # 工程量分布
            volume_distribution = {
                'cut_volume': adjusted_cut_volume,
                'fill_volume': adjusted_fill_volume,
                'net_volume': net_volume,
                'balance_ratio': balance_ratio
            }
            
            return VolumeAnalysis(
                total_cut_volume=adjusted_cut_volume,
                total_fill_volume=adjusted_fill_volume,
                net_volume=net_volume,
                balance_ratio=balance_ratio,
                cut_sections=cut_sections,
                fill_sections=fill_sections,
                volume_distribution=volume_distribution
            )
            
        except Exception as e:
            logger.error(f"计算工程量失败: {str(e)}")
            raise
    
    def _generate_analysis_report(self, 
                                longitudinal_profile: LongitudinalProfile,
                                cross_sections: List[CrossSection],
                                volume_analysis: VolumeAnalysis) -> Dict[str, Any]:
        """生成分析报告"""
        try:
            report = {
                'summary': {
                    'road_length': longitudinal_profile.total_length,
                    'cross_sections_count': len(cross_sections),
                    'max_gradient': longitudinal_profile.max_gradient,
                    'min_gradient': longitudinal_profile.min_gradient,
                    'average_gradient': longitudinal_profile.average_gradient
                },
                'volumes': {
                    'total_cut_volume': volume_analysis.total_cut_volume,
                    'total_fill_volume': volume_analysis.total_fill_volume,
                    'net_volume': volume_analysis.net_volume,
                    'balance_ratio': volume_analysis.balance_ratio * 100
                },
                'analysis': {
                    'cut_sections_count': len(volume_analysis.cut_sections),
                    'fill_sections_count': len(volume_analysis.fill_sections),
                    'max_cut_area': max((cs.cut_area for cs in cross_sections), default=0),
                    'max_fill_area': max((cs.fill_area for cs in cross_sections), default=0)
                },
                'recommendations': self._generate_recommendations(
                    longitudinal_profile, volume_analysis
                )
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成分析报告失败: {str(e)}")
            return {}
    
    def _generate_recommendations(self, 
                                longitudinal_profile: LongitudinalProfile,
                                volume_analysis: VolumeAnalysis) -> List[str]:
        """生成建议"""
        recommendations = []
        
        # 坡度建议
        if longitudinal_profile.max_gradient > 8.0:
            recommendations.append(f"最大坡度{longitudinal_profile.max_gradient:.1f}%超过建议值，建议优化线形")
        
        # 土方平衡建议
        if volume_analysis.balance_ratio < 0.7:
            recommendations.append("土方不平衡较大，建议调整路面标高或优化线形")
        
        if volume_analysis.net_volume > 10000:
            recommendations.append("挖方量较大，考虑降低路面标高或寻找就近填方区域")
        elif volume_analysis.net_volume < -10000:
            recommendations.append("填方量较大，考虑提高路面标高或寻找就近挖方区域")
        
        # 横断面建议
        if len(volume_analysis.cut_sections) > len(volume_analysis.fill_sections) * 2:
            recommendations.append("挖方段较多，注意边坡稳定性和排水设计")
        
        return recommendations
    
    def _get_ground_elevation(self, x: float, y: float, terrain_data: np.ndarray = None) -> Optional[float]:
        """获取地面高程"""
        if terrain_data is None:
            return None
        
        # 简化实现，实际应该进行插值
        return 100.0 + np.random.normal(0, 5)  # 模拟地形高程
