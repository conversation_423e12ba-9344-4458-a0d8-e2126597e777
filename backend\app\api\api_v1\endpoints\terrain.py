"""
地形数据API端点
"""
from fastapi import APIRouter, Depends, UploadFile, File
from sqlalchemy.orm import Session

from app.core.database import get_db

router = APIRouter()


@router.post("/upload")
async def upload_terrain_data(
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """上传地形数据文件"""
    return {"message": f"上传地形数据文件: {file.filename}"}


@router.get("/{project_id}")
async def get_terrain_data(project_id: int, db: Session = Depends(get_db)):
    """获取项目地形数据"""
    return {"message": f"获取项目{project_id}地形数据"}


@router.post("/{project_id}/process")
async def process_terrain_data(project_id: int, db: Session = Depends(get_db)):
    """处理地形数据"""
    return {"message": f"处理项目{project_id}地形数据"}


@router.get("/{project_id}/elevation/{x}/{y}")
async def get_elevation(project_id: int, x: float, y: float, db: Session = Depends(get_db)):
    """获取指定坐标的高程值"""
    return {"message": f"获取坐标({x}, {y})的高程值"}
