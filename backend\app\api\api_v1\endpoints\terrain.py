"""
地形数据API端点
"""
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from pydantic import BaseModel

from app.core.database import get_db
from app.services.terrain_service import TerrainService
from app.models.terrain import TerrainData

router = APIRouter()
terrain_service = TerrainService()


class TerrainProcessingParams(BaseModel):
    """地形处理参数"""
    target_resolution: Optional[float] = None
    interpolation_method: str = "linear"
    filter_noise: bool = True
    generate_contours: bool = True
    contour_interval: float = 5.0


class TerrainResponse(BaseModel):
    """地形数据响应"""
    id: int
    name: str
    description: Optional[str]
    data_type: str
    file_format: str
    file_size: int
    processing_status: str
    processing_progress: float
    bounds: Optional[List[float]]
    resolution: Optional[float]
    min_elevation: Optional[float]
    max_elevation: Optional[float]
    point_count: Optional[int]
    created_at: str

    class Config:
        from_attributes = True


@router.post("/upload", response_model=TerrainResponse)
async def upload_terrain_data(
    project_id: int = Query(..., description="项目ID"),
    description: Optional[str] = Query(None, description="数据描述"),
    file: UploadFile = File(..., description="地形数据文件"),
    db: Session = Depends(get_db)
):
    """上传地形数据文件"""
    try:
        terrain_data = await terrain_service.upload_terrain_file(
            db=db,
            project_id=project_id,
            file=file,
            description=description
        )

        return TerrainResponse(
            id=terrain_data.id,
            name=terrain_data.name,
            description=terrain_data.description,
            data_type=terrain_data.data_type,
            file_format=terrain_data.file_format,
            file_size=terrain_data.file_size,
            processing_status=terrain_data.processing_status,
            processing_progress=terrain_data.processing_progress,
            bounds=terrain_data.bounds,
            resolution=terrain_data.resolution,
            min_elevation=terrain_data.min_elevation,
            max_elevation=terrain_data.max_elevation,
            point_count=terrain_data.point_count,
            created_at=terrain_data.created_at.isoformat() if terrain_data.created_at else ""
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"上传失败: {str(e)}")


@router.get("/{project_id}", response_model=List[TerrainResponse])
async def get_terrain_data_list(
    project_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取项目地形数据列表"""
    try:
        terrain_list = terrain_service.get_terrain_data_list(
            db=db,
            project_id=project_id,
            skip=skip,
            limit=limit
        )

        return [
            TerrainResponse(
                id=terrain.id,
                name=terrain.name,
                description=terrain.description,
                data_type=terrain.data_type,
                file_format=terrain.file_format,
                file_size=terrain.file_size,
                processing_status=terrain.processing_status,
                processing_progress=terrain.processing_progress,
                bounds=terrain.bounds,
                resolution=terrain.resolution,
                min_elevation=terrain.min_elevation,
                max_elevation=terrain.max_elevation,
                point_count=terrain.point_count,
                created_at=terrain.created_at.isoformat() if terrain.created_at else ""
            )
            for terrain in terrain_list
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取地形数据列表失败: {str(e)}")


@router.post("/{terrain_id}/process")
async def process_terrain_data(
    terrain_id: int,
    params: TerrainProcessingParams,
    db: Session = Depends(get_db)
):
    """处理地形数据"""
    try:
        success = await terrain_service.process_terrain_data(
            db=db,
            terrain_id=terrain_id,
            processing_params=params.dict(exclude_none=True)
        )

        if success:
            return {"message": "地形数据处理已开始", "terrain_id": terrain_id}
        else:
            raise HTTPException(status_code=500, detail="地形数据处理失败")

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"处理失败: {str(e)}")


@router.get("/{terrain_id}/elevation")
async def get_elevation(
    terrain_id: int,
    x: float = Query(..., description="X坐标"),
    y: float = Query(..., description="Y坐标"),
    db: Session = Depends(get_db)
):
    """获取指定坐标的高程值"""
    try:
        elevation = terrain_service.get_elevation_at_point(
            db=db,
            terrain_id=terrain_id,
            x=x,
            y=y
        )

        if elevation is not None:
            return {
                "x": x,
                "y": y,
                "elevation": elevation,
                "terrain_id": terrain_id
            }
        else:
            raise HTTPException(status_code=404, detail="无法获取该位置的高程值")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取高程值失败: {str(e)}")


@router.get("/{terrain_id}/profile")
async def get_terrain_profile(
    terrain_id: int,
    start_x: float = Query(..., description="起点X坐标"),
    start_y: float = Query(..., description="起点Y坐标"),
    end_x: float = Query(..., description="终点X坐标"),
    end_y: float = Query(..., description="终点Y坐标"),
    sample_count: int = Query(100, ge=10, le=1000, description="采样点数"),
    db: Session = Depends(get_db)
):
    """获取地形剖面"""
    try:
        profile = terrain_service.get_terrain_profile(
            db=db,
            terrain_id=terrain_id,
            start_point=(start_x, start_y),
            end_point=(end_x, end_y),
            sample_count=sample_count
        )

        if profile:
            return {
                "terrain_id": terrain_id,
                "start_point": [start_x, start_y],
                "end_point": [end_x, end_y],
                "sample_count": len(profile),
                "profile_data": profile
            }
        else:
            raise HTTPException(status_code=404, detail="无法生成地形剖面")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取地形剖面失败: {str(e)}")


@router.post("/{terrain_id}/contours")
async def generate_contours(
    terrain_id: int,
    interval: float = Query(5.0, gt=0, description="等高线间距"),
    db: Session = Depends(get_db)
):
    """生成等高线"""
    try:
        success = terrain_service.generate_contours(
            db=db,
            terrain_id=terrain_id,
            interval=interval
        )

        if success:
            return {"message": "等高线生成成功", "terrain_id": terrain_id, "interval": interval}
        else:
            raise HTTPException(status_code=500, detail="等高线生成失败")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成等高线失败: {str(e)}")


@router.delete("/{terrain_id}")
async def delete_terrain_data(
    terrain_id: int,
    db: Session = Depends(get_db)
):
    """删除地形数据"""
    try:
        success = terrain_service.delete_terrain_data(db=db, terrain_id=terrain_id)

        if success:
            return {"message": "地形数据删除成功", "terrain_id": terrain_id}
        else:
            raise HTTPException(status_code=404, detail="地形数据不存在")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除失败: {str(e)}")


@router.get("/{terrain_id}/info", response_model=TerrainResponse)
async def get_terrain_info(
    terrain_id: int,
    db: Session = Depends(get_db)
):
    """获取地形数据详情"""
    try:
        terrain_data = terrain_service.get_terrain_data(db=db, terrain_id=terrain_id)

        if not terrain_data:
            raise HTTPException(status_code=404, detail="地形数据不存在")

        return TerrainResponse(
            id=terrain_data.id,
            name=terrain_data.name,
            description=terrain_data.description,
            data_type=terrain_data.data_type,
            file_format=terrain_data.file_format,
            file_size=terrain_data.file_size,
            processing_status=terrain_data.processing_status,
            processing_progress=terrain_data.processing_progress,
            bounds=terrain_data.bounds,
            resolution=terrain_data.resolution,
            min_elevation=terrain_data.min_elevation,
            max_elevation=terrain_data.max_elevation,
            point_count=terrain_data.point_count,
            created_at=terrain_data.created_at.isoformat() if terrain_data.created_at else ""
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取地形数据详情失败: {str(e)}")
