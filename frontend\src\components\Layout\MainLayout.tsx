import React, { useState } from 'react'
import { Layout, <PERSON>u, Avatar, Dropdown, <PERSON><PERSON>, Badge, Tooltip } from 'antd'
import { useNavigate, useLocation } from 'react-router-dom'
import {
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  DashboardOutlined,
  ProjectOutlined,
  GlobalOutlined,
  CarOutlined,
  WarningOutlined,
  SafetyOutlined,
  ThunderboltOutlined,
  FileTextOutlined,
  SettingOutlined,
  UserOutlined,
  LogoutOutlined,
  BellOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons'

const { Header, Sider, Content } = Layout

interface MainLayoutProps {
  children: React.ReactNode
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const [collapsed, setCollapsed] = useState(false)
  const navigate = useNavigate()
  const location = useLocation()

  // 菜单项配置
  const menuItems = [
    {
      key: '/dashboard',
      icon: <DashboardOutlined />,
      label: '仪表板',
    },
    {
      key: '/projects',
      icon: <ProjectOutlined />,
      label: '项目管理',
    },
    {
      key: '/terrain',
      icon: <GlobalOutlined />,
      label: '地形数据',
    },
    {
      key: '/road-design',
      icon: <CarOutlined />,
      label: '道路设计',
    },
    {
      key: '/conflict-detection',
      icon: <WarningOutlined />,
      label: '冲突检测',
    },
    {
      key: '/safety-analysis',
      icon: <SafetyOutlined />,
      label: '安全分析',
    },
    {
      key: '/route-optimization',
      icon: <OptimizationOutlined />,
      label: '路线优化',
    },
    {
      key: '/autocad',
      icon: <FileTextOutlined />,
      label: 'AutoCAD集成',
    },
    {
      key: '/settings',
      icon: <SettingOutlined />,
      label: '系统设置',
    },
  ]

  // 用户下拉菜单
  const userMenuItems = [
    {
      key: 'profile',
      icon: <UserOutlined />,
      label: '个人资料',
    },
    {
      key: 'settings',
      icon: <SettingOutlined />,
      label: '账户设置',
    },
    {
      type: 'divider' as const,
    },
    {
      key: 'logout',
      icon: <LogoutOutlined />,
      label: '退出登录',
    },
  ]

  const handleMenuClick = ({ key }: { key: string }) => {
    navigate(key)
  }

  const handleUserMenuClick = ({ key }: { key: string }) => {
    switch (key) {
      case 'profile':
        navigate('/profile')
        break
      case 'settings':
        navigate('/settings')
        break
      case 'logout':
        // TODO: 实现登出逻辑
        navigate('/login')
        break
    }
  }

  return (
    <Layout style={{ minHeight: '100vh' }} className="mining-interface">
      {/* 侧边栏 */}
      <Sider 
        trigger={null} 
        collapsible 
        collapsed={collapsed}
        theme="dark"
        width={240}
        style={{
          background: '#2d2d2d',
          borderRight: '1px solid #3d3d3d'
        }}
      >
        {/* Logo */}
        <div style={{
          height: '64px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: collapsed ? 'center' : 'flex-start',
          padding: collapsed ? '0' : '0 24px',
          borderBottom: '1px solid #3d3d3d',
          background: '#1a1a1a'
        }}>
          {collapsed ? (
            <div style={{
              width: '32px',
              height: '32px',
              background: '#faad14',
              borderRadius: '6px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: '#000',
              fontWeight: 'bold',
              fontSize: '16px'
            }}>
              矿
            </div>
          ) : (
            <div style={{
              display: 'flex',
              alignItems: 'center',
              color: '#fff'
            }}>
              <div style={{
                width: '32px',
                height: '32px',
                background: '#faad14',
                borderRadius: '6px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                color: '#000',
                fontWeight: 'bold',
                fontSize: '16px',
                marginRight: '12px'
              }}>
                矿
              </div>
              <span style={{ fontSize: '16px', fontWeight: 'bold' }}>
                道路设计系统
              </span>
            </div>
          )}
        </div>

        {/* 菜单 */}
        <Menu
          theme="dark"
          mode="inline"
          selectedKeys={[location.pathname]}
          items={menuItems}
          onClick={handleMenuClick}
          style={{
            background: 'transparent',
            border: 'none'
          }}
        />
      </Sider>

      <Layout>
        {/* 顶部导航 */}
        <Header style={{
          padding: '0 24px',
          background: '#1a1a1a',
          borderBottom: '1px solid #3d3d3d',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between'
        }}>
          {/* 左侧 */}
          <div style={{ display: 'flex', alignItems: 'center' }}>
            <Button
              type="text"
              icon={collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
              onClick={() => setCollapsed(!collapsed)}
              style={{
                fontSize: '16px',
                width: 64,
                height: 64,
                color: '#fff'
              }}
            />
          </div>

          {/* 右侧 */}
          <div style={{ display: 'flex', alignItems: 'center', gap: '16px' }}>
            {/* 通知 */}
            <Tooltip title="通知">
              <Badge count={3} size="small">
                <Button
                  type="text"
                  icon={<BellOutlined />}
                  style={{ color: '#fff' }}
                />
              </Badge>
            </Tooltip>

            {/* 帮助 */}
            <Tooltip title="帮助">
              <Button
                type="text"
                icon={<QuestionCircleOutlined />}
                style={{ color: '#fff' }}
              />
            </Tooltip>

            {/* 用户菜单 */}
            <Dropdown
              menu={{
                items: userMenuItems,
                onClick: handleUserMenuClick
              }}
              placement="bottomRight"
            >
              <div style={{
                display: 'flex',
                alignItems: 'center',
                cursor: 'pointer',
                padding: '8px',
                borderRadius: '6px',
                transition: 'background-color 0.3s'
              }}>
                <Avatar
                  size="small"
                  icon={<UserOutlined />}
                  style={{ marginRight: '8px' }}
                />
                <span style={{ color: '#fff' }}>管理员</span>
              </div>
            </Dropdown>
          </div>
        </Header>

        {/* 主内容区 */}
        <Content style={{
          margin: '24px',
          padding: '24px',
          background: '#fff',
          borderRadius: '8px',
          minHeight: 'calc(100vh - 112px)',
          overflow: 'auto'
        }}>
          {children}
        </Content>
      </Layout>
    </Layout>
  )
}

export default MainLayout
