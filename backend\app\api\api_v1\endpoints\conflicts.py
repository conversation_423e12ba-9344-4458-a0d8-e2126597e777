"""
冲突检测API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from pydantic import BaseModel

from app.core.database import get_db
from app.services.conflict_service import ConflictService
from app.models.conflict_detection import Conflict

router = APIRouter()
conflict_service = ConflictService()


class ConflictDetectionParams(BaseModel):
    """冲突检测参数"""
    road_ids: Optional[List[int]] = None
    detection_types: Optional[List[str]] = None
    sensitivity_level: str = "medium"
    include_resolved: bool = False


class ConflictResolutionData(BaseModel):
    """冲突解决数据"""
    resolution_type: str
    resolution_description: str
    estimated_cost: Optional[float] = None
    implementation_notes: Optional[str] = None


class ConflictResponse(BaseModel):
    """冲突响应模型"""
    id: int
    title: str
    description: str
    severity: str
    status: str
    location: List[float]
    chainage: Optional[float]
    confidence_score: Optional[float]
    detected_at: str
    resolved_at: Optional[str]
    
    class Config:
        from_attributes = True


@router.post("/{project_id}/detect")
async def detect_conflicts(
    project_id: int,
    detection_params: ConflictDetectionParams,
    db: Session = Depends(get_db)
):
    """检测项目冲突"""
    try:
        # 如果指定了道路ID，逐个检测
        if detection_params.road_ids:
            all_conflicts = []
            for road_id in detection_params.road_ids:
                conflicts = await conflict_service.detect_road_conflicts(
                    db=db,
                    road_id=road_id,
                    detection_params=detection_params.dict()
                )
                all_conflicts.extend(conflicts)
        else:
            # 检测项目下所有道路
            from app.models.road import Road
            roads = db.query(Road).filter(Road.project_id == project_id).all()
            
            all_conflicts = []
            for road in roads:
                conflicts = await conflict_service.detect_road_conflicts(
                    db=db,
                    road_id=road.id,
                    detection_params=detection_params.dict()
                )
                all_conflicts.extend(conflicts)
        
        return {
            "message": "冲突检测完成",
            "project_id": project_id,
            "total_conflicts": len(all_conflicts),
            "conflicts": [
                ConflictResponse(
                    id=conflict.id,
                    title=conflict.title,
                    description=conflict.description,
                    severity=conflict.severity.value if hasattr(conflict.severity, 'value') else conflict.severity,
                    status=conflict.status.value if hasattr(conflict.status, 'value') else conflict.status,
                    location=conflict.location,
                    chainage=conflict.chainage,
                    confidence_score=conflict.confidence_score,
                    detected_at=conflict.detected_at.isoformat() if conflict.detected_at else "",
                    resolved_at=conflict.resolved_at.isoformat() if conflict.resolved_at else None
                )
                for conflict in all_conflicts[:10]  # 只返回前10个
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"冲突检测失败: {str(e)}")


@router.get("/{project_id}/conflicts", response_model=List[ConflictResponse])
async def get_conflicts(
    project_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    severity: Optional[str] = Query(None, description="严重程度过滤"),
    status: Optional[str] = Query(None, description="状态过滤"),
    db: Session = Depends(get_db)
):
    """获取项目冲突列表"""
    try:
        conflicts = conflict_service.get_project_conflicts(
            db=db,
            project_id=project_id,
            skip=skip,
            limit=limit
        )
        
        # 应用过滤器
        if severity:
            conflicts = [c for c in conflicts if c.severity == severity]
        
        if status:
            conflicts = [c for c in conflicts if c.status == status]
        
        return [
            ConflictResponse(
                id=conflict.id,
                title=conflict.title,
                description=conflict.description,
                severity=conflict.severity.value if hasattr(conflict.severity, 'value') else conflict.severity,
                status=conflict.status.value if hasattr(conflict.status, 'value') else conflict.status,
                location=conflict.location,
                chainage=conflict.chainage,
                confidence_score=conflict.confidence_score,
                detected_at=conflict.detected_at.isoformat() if conflict.detected_at else "",
                resolved_at=conflict.resolved_at.isoformat() if conflict.resolved_at else None
            )
            for conflict in conflicts
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取冲突列表失败: {str(e)}")


@router.get("/{project_id}/conflicts/{conflict_id}", response_model=ConflictResponse)
async def get_conflict_detail(
    project_id: int,
    conflict_id: int,
    db: Session = Depends(get_db)
):
    """获取冲突详情"""
    try:
        conflict = conflict_service.get_conflict_detail(db=db, conflict_id=conflict_id)
        
        if not conflict:
            raise HTTPException(status_code=404, detail="冲突不存在")
        
        if conflict.project_id != project_id:
            raise HTTPException(status_code=403, detail="无权访问该冲突")
        
        return ConflictResponse(
            id=conflict.id,
            title=conflict.title,
            description=conflict.description,
            severity=conflict.severity.value if hasattr(conflict.severity, 'value') else conflict.severity,
            status=conflict.status.value if hasattr(conflict.status, 'value') else conflict.status,
            location=conflict.location,
            chainage=conflict.chainage,
            confidence_score=conflict.confidence_score,
            detected_at=conflict.detected_at.isoformat() if conflict.detected_at else "",
            resolved_at=conflict.resolved_at.isoformat() if conflict.resolved_at else None
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取冲突详情失败: {str(e)}")


@router.post("/{project_id}/conflicts/{conflict_id}/resolve")
async def resolve_conflict(
    project_id: int,
    conflict_id: int,
    resolution_data: ConflictResolutionData,
    db: Session = Depends(get_db)
):
    """解决冲突"""
    try:
        # TODO: 获取当前用户ID
        user_id = 1  # 临时使用固定用户ID
        
        success = conflict_service.resolve_conflict(
            db=db,
            conflict_id=conflict_id,
            resolution_data=resolution_data.dict(),
            resolved_by_user_id=user_id
        )
        
        if success:
            return {
                "message": "冲突解决成功",
                "conflict_id": conflict_id,
                "resolution_type": resolution_data.resolution_type
            }
        else:
            raise HTTPException(status_code=404, detail="冲突不存在")
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"解决冲突失败: {str(e)}")


@router.get("/{project_id}/statistics")
async def get_conflict_statistics(
    project_id: int,
    db: Session = Depends(get_db)
):
    """获取冲突统计"""
    try:
        statistics = conflict_service.get_conflict_statistics(db=db, project_id=project_id)
        
        return {
            "project_id": project_id,
            "statistics": statistics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取冲突统计失败: {str(e)}")


@router.get("/{project_id}/report")
async def generate_conflict_report(
    project_id: int,
    db: Session = Depends(get_db)
):
    """生成冲突报告"""
    try:
        report = conflict_service.generate_conflict_report(db=db, project_id=project_id)
        
        return report
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成冲突报告失败: {str(e)}")


@router.get("/roads/{road_id}/conflicts", response_model=List[ConflictResponse])
async def get_road_conflicts(
    road_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取道路冲突列表"""
    try:
        conflicts = conflict_service.get_road_conflicts(
            db=db,
            road_id=road_id,
            skip=skip,
            limit=limit
        )
        
        return [
            ConflictResponse(
                id=conflict.id,
                title=conflict.title,
                description=conflict.description,
                severity=conflict.severity.value if hasattr(conflict.severity, 'value') else conflict.severity,
                status=conflict.status.value if hasattr(conflict.status, 'value') else conflict.status,
                location=conflict.location,
                chainage=conflict.chainage,
                confidence_score=conflict.confidence_score,
                detected_at=conflict.detected_at.isoformat() if conflict.detected_at else "",
                resolved_at=conflict.resolved_at.isoformat() if conflict.resolved_at else None
            )
            for conflict in conflicts
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取道路冲突列表失败: {str(e)}")


@router.post("/roads/{road_id}/detect")
async def detect_road_conflicts(
    road_id: int,
    detection_params: Optional[Dict] = None,
    db: Session = Depends(get_db)
):
    """检测单条道路冲突"""
    try:
        conflicts = await conflict_service.detect_road_conflicts(
            db=db,
            road_id=road_id,
            detection_params=detection_params or {}
        )
        
        return {
            "message": "道路冲突检测完成",
            "road_id": road_id,
            "total_conflicts": len(conflicts),
            "conflicts": [
                ConflictResponse(
                    id=conflict.id,
                    title=conflict.title,
                    description=conflict.description,
                    severity=conflict.severity.value if hasattr(conflict.severity, 'value') else conflict.severity,
                    status=conflict.status.value if hasattr(conflict.status, 'value') else conflict.status,
                    location=conflict.location,
                    chainage=conflict.chainage,
                    confidence_score=conflict.confidence_score,
                    detected_at=conflict.detected_at.isoformat() if conflict.detected_at else "",
                    resolved_at=conflict.resolved_at.isoformat() if conflict.resolved_at else None
                )
                for conflict in conflicts
            ]
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"道路冲突检测失败: {str(e)}")
