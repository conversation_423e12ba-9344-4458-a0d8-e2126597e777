#!/usr/bin/env python3
"""
基础测试脚本
"""
print("=" * 50)
print("🏔️  露天矿山道路设计软件 - 基础测试")
print("=" * 50)

import sys
import os
import json
from http.server import HTTPServer, BaseHTTPRequestHandler

print(f"✅ Python版本: {sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}")
print(f"✅ 当前目录: {os.getcwd()}")
print(f"✅ 基础模块导入成功")

class SimpleHandler(BaseHTTPRequestHandler):
    def do_GET(self):
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response = {
            "message": "露天矿山道路设计软件",
            "version": "1.0.0",
            "status": "running",
            "path": self.path
        }
        
        self.wfile.write(json.dumps(response, ensure_ascii=False).encode('utf-8'))
    
    def log_message(self, format, *args):
        print(f"[请求] {format % args}")

def main():
    try:
        server = HTTPServer(('localhost', 8000), SimpleHandler)
        print("\n🚀 HTTP服务器启动成功!")
        print("🌐 访问地址: http://localhost:8000")
        print("按 Ctrl+C 停止服务器\n")
        
        server.serve_forever()
        
    except KeyboardInterrupt:
        print("\n✅ 服务器已停止")
        server.shutdown()
    except Exception as e:
        print(f"❌ 错误: {e}")

if __name__ == "__main__":
    main()
