"""
AutoCAD集成API端点
"""
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Query
from fastapi.responses import FileResponse
from sqlalchemy.orm import Session
from typing import List, Optional
from pydantic import BaseModel
import os
import shutil

from app.core.database import get_db
from app.services.autocad_service import AutoCADService
from app.models.cad_integration import CADFile

router = APIRouter()
autocad_service = AutoCADService()


class CADFileResponse(BaseModel):
    """CAD文件响应模型"""
    id: int
    project_id: int
    filename: str
    file_size: int
    file_type: str
    cad_version: Optional[str]
    units: Optional[str]
    bounds: Optional[List[float]]
    layer_count: int
    entity_count: int
    import_status: str
    imported_at: str

    class Config:
        from_attributes = True


class DrawingGenerationParams(BaseModel):
    """图纸生成参数"""
    template_name: str = "道路平面图-A1"
    include_contours: bool = True
    include_annotations: bool = True
    scale: Optional[float] = None


@router.post("/{project_id}/import")
async def import_cad_file(
    project_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(get_db)
):
    """导入CAD文件"""
    try:
        # 检查文件格式
        if not file.filename.lower().endswith(('.dwg', '.dxf')):
            raise HTTPException(status_code=400, detail="不支持的文件格式，仅支持DWG和DXF文件")

        # 保存上传文件
        upload_path = os.path.join(autocad_service.upload_dir, file.filename)

        with open(upload_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 导入CAD文件
        cad_file = await autocad_service.import_cad_file(
            db=db,
            project_id=project_id,
            file_path=upload_path,
            filename=file.filename
        )

        return CADFileResponse(
            id=cad_file.id,
            project_id=cad_file.project_id,
            filename=cad_file.filename,
            file_size=cad_file.file_size,
            file_type=cad_file.file_type,
            cad_version=cad_file.cad_version,
            units=cad_file.units,
            bounds=cad_file.bounds,
            layer_count=cad_file.layer_count,
            entity_count=cad_file.entity_count,
            import_status=cad_file.import_status,
            imported_at=cad_file.imported_at.isoformat()
        )

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"导入CAD文件失败: {str(e)}")


@router.get("/{project_id}/files", response_model=List[CADFileResponse])
async def get_project_cad_files(
    project_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取项目CAD文件列表"""
    try:
        cad_files = autocad_service.get_project_cad_files(
            db=db,
            project_id=project_id,
            skip=skip,
            limit=limit
        )

        return [
            CADFileResponse(
                id=file.id,
                project_id=file.project_id,
                filename=file.filename,
                file_size=file.file_size,
                file_type=file.file_type,
                cad_version=file.cad_version,
                units=file.units,
                bounds=file.bounds,
                layer_count=file.layer_count,
                entity_count=file.entity_count,
                import_status=file.import_status,
                imported_at=file.imported_at.isoformat()
            )
            for file in cad_files
        ]

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取CAD文件列表失败: {str(e)}")


@router.get("/files/{file_id}", response_model=CADFileResponse)
async def get_cad_file_detail(
    file_id: int,
    db: Session = Depends(get_db)
):
    """获取CAD文件详情"""
    try:
        cad_file = autocad_service.get_cad_file_detail(db=db, file_id=file_id)

        if not cad_file:
            raise HTTPException(status_code=404, detail="CAD文件不存在")

        return CADFileResponse(
            id=cad_file.id,
            project_id=cad_file.project_id,
            filename=cad_file.filename,
            file_size=cad_file.file_size,
            file_type=cad_file.file_type,
            cad_version=cad_file.cad_version,
            units=cad_file.units,
            bounds=cad_file.bounds,
            layer_count=cad_file.layer_count,
            entity_count=cad_file.entity_count,
            import_status=cad_file.import_status,
            imported_at=cad_file.imported_at.isoformat()
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取CAD文件详情失败: {str(e)}")


@router.post("/{project_id}/generate-plan")
async def generate_plan_drawing(
    project_id: int,
    params: DrawingGenerationParams,
    db: Session = Depends(get_db)
):
    """生成平面图"""
    try:
        output_path = await autocad_service.generate_plan_drawing(
            db=db,
            project_id=project_id,
            template_name=params.template_name
        )

        # 返回文件下载链接
        filename = os.path.basename(output_path)

        return {
            "success": True,
            "message": "平面图生成完成",
            "filename": filename,
            "download_url": f"/api/v1/autocad/download/{filename}"
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成平面图失败: {str(e)}")


@router.post("/roads/{road_id}/generate-profile")
async def generate_profile_drawing(
    road_id: int,
    params: DrawingGenerationParams,
    db: Session = Depends(get_db)
):
    """生成纵断面图"""
    try:
        output_path = await autocad_service.generate_profile_drawing(
            db=db,
            road_id=road_id,
            template_name=params.template_name
        )

        # 返回文件下载链接
        filename = os.path.basename(output_path)

        return {
            "success": True,
            "message": "纵断面图生成完成",
            "filename": filename,
            "download_url": f"/api/v1/autocad/download/{filename}"
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成纵断面图失败: {str(e)}")


@router.post("/roads/{road_id}/generate-cross-section")
async def generate_cross_section_drawing(
    road_id: int,
    params: DrawingGenerationParams,
    chainages: Optional[List[float]] = Query(None, description="指定里程列表"),
    db: Session = Depends(get_db)
):
    """生成横断面图"""
    try:
        output_path = await autocad_service.generate_cross_section_drawing(
            db=db,
            road_id=road_id,
            chainages=chainages,
            template_name=params.template_name
        )

        # 返回文件下载链接
        filename = os.path.basename(output_path)

        return {
            "success": True,
            "message": "横断面图生成完成",
            "filename": filename,
            "download_url": f"/api/v1/autocad/download/{filename}"
        }

    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成横断面图失败: {str(e)}")


@router.get("/download/{filename}")
async def download_drawing(filename: str):
    """下载生成的图纸"""
    try:
        file_path = os.path.join(autocad_service.output_dir, filename)

        if not os.path.exists(file_path):
            raise HTTPException(status_code=404, detail="文件不存在")

        return FileResponse(
            path=file_path,
            filename=filename,
            media_type='application/octet-stream'
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"下载文件失败: {str(e)}")


@router.get("/templates")
async def get_drawing_templates():
    """获取可用的图纸模板"""
    try:
        templates = autocad_service.get_drawing_templates()

        return {
            "templates": templates
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图纸模板失败: {str(e)}")


@router.get("/supported-formats")
async def get_supported_formats():
    """获取支持的CAD格式"""
    try:
        formats = autocad_service.get_supported_formats()

        return {
            "supported_formats": formats,
            "description": {
                "dwg": "AutoCAD原生格式，支持所有版本",
                "dxf": "AutoCAD交换格式，通用性更好"
            }
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取支持格式失败: {str(e)}")


@router.get("/standards/drawing-standards")
async def get_drawing_standards():
    """获取图纸绘制标准"""
    try:
        standards = {
            "layer_standards": {
                "ROAD_CENTERLINE": {
                    "description": "道路中心线",
                    "color": "红色(1)",
                    "linetype": "连续线",
                    "lineweight": "0.5mm"
                },
                "ROAD_EDGE": {
                    "description": "道路边线",
                    "color": "黄色(2)",
                    "linetype": "连续线",
                    "lineweight": "0.3mm"
                },
                "CONTOURS": {
                    "description": "等高线",
                    "color": "青色(3)",
                    "linetype": "连续线",
                    "lineweight": "0.2mm"
                },
                "TEXT": {
                    "description": "文字标注",
                    "color": "白色(7)",
                    "linetype": "连续线",
                    "lineweight": "0.1mm"
                }
            },
            "text_standards": {
                "title_height": "4.0mm",
                "annotation_height": "2.0mm",
                "dimension_height": "1.5mm",
                "note_height": "1.0mm"
            },
            "scale_standards": {
                "plan_view": "1:1000 - 1:2000",
                "profile_view": "水平1:1000, 垂直1:100",
                "cross_section": "1:200 - 1:500",
                "detail_view": "1:50 - 1:100"
            },
            "paper_sizes": {
                "A0": "1189×841mm",
                "A1": "841×594mm",
                "A2": "594×420mm",
                "A3": "420×297mm",
                "A4": "297×210mm"
            }
        }

        return standards

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取图纸标准失败: {str(e)}")
