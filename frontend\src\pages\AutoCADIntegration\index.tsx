import React, { useState, useEffect } from 'react'
import {
  Card, Button, Table, Tag, Space, Modal, Form, Select,
  Upload, message, Row, Col, Statistic, Tabs,
  Descriptions, Tooltip, Alert, List, Progress,
  Checkbox, Divider, Typography
} from 'antd'
import {
  UploadOutlined, DownloadOutlined, FileTextOutlined,
  EyeOutlined, SettingOutlined, ExportOutlined,
  FileImageOutlined, ReloadOutlined, FolderOpenOutlined,
  Check<PERSON><PERSON>cleOutlined, CloseCircleOutlined, SyncOutlined,
  ToolOutlined, LineChartOutlined, BarChartOutlined
} from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import axios from 'axios'

const { TabPane } = Tabs
const { Text, Title } = Typography

interface CADFile {
  id: number
  project_id: number
  filename: string
  file_size: number
  file_type: string
  cad_version?: string
  units?: string
  bounds?: number[]
  layer_count: number
  entity_count: number
  import_status: string
  imported_at: string
}

interface DrawingTemplate {
  name: string
  paper_size: string
  scale: number
  units: string
  title_block: boolean
}

const AutoCADIntegration: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [cadFiles, setCadFiles] = useState<CADFile[]>([])
  const [selectedFile, setSelectedFile] = useState<CADFile | null>(null)
  const [templates, setTemplates] = useState<DrawingTemplate[]>([])
  const [supportedFormats, setSupportedFormats] = useState<string[]>([])
  const [loading, setLoading] = useState(false)
  const [uploading, setUploading] = useState(false)
  const [generating, setGenerating] = useState(false)
  const [importModalVisible, setImportModalVisible] = useState(false)
  const [generateModalVisible, setGenerateModalVisible] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [standardsModalVisible, setStandardsModalVisible] = useState(false)
  const [drawingStandards, setDrawingStandards] = useState<any>(null)
  const [form] = Form.useForm()

  // 获取CAD文件列表
  const fetchCADFiles = async () => {
    if (!projectId) return

    setLoading(true)
    try {
      const response = await axios.get(`/api/v1/autocad/${projectId}/files`)
      setCadFiles(response.data)
    } catch (error) {
      message.error('获取CAD文件列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取图纸模板
  const fetchTemplates = async () => {
    try {
      const response = await axios.get('/api/v1/autocad/templates')
      setTemplates(response.data.templates)
    } catch (error) {
      message.error('获取图纸模板失败')
    }
  }

  // 获取支持格式
  const fetchSupportedFormats = async () => {
    try {
      const response = await axios.get('/api/v1/autocad/supported-formats')
      setSupportedFormats(response.data.supported_formats)
    } catch (error) {
      message.error('获取支持格式失败')
    }
  }

  // 获取图纸标准
  const fetchDrawingStandards = async () => {
    try {
      const response = await axios.get('/api/v1/autocad/standards/drawing-standards')
      setDrawingStandards(response.data)
    } catch (error) {
      message.error('获取图纸标准失败')
    }
  }

  useEffect(() => {
    fetchCADFiles()
    fetchTemplates()
    fetchSupportedFormats()
  }, [projectId])

  // 文件上传处理
  const handleFileUpload = async (file: File) => {
    if (!projectId) return false

    setUploading(true)
    try {
      const formData = new FormData()
      formData.append('file', file)

      const response = await axios.post(
        `/api/v1/autocad/${projectId}/import`,
        formData,
        {
          headers: {
            'Content-Type': 'multipart/form-data'
          }
        }
      )

      message.success('CAD文件导入成功')
      setImportModalVisible(false)
      fetchCADFiles()
      return true
    } catch (error: any) {
      message.error(error.response?.data?.detail || 'CAD文件导入失败')
      return false
    } finally {
      setUploading(false)
    }
  }

  // 生成图纸
  const handleGenerateDrawing = async () => {
    try {
      const values = await form.validateFields()
      setGenerating(true)

      const drawingTypes = values.drawing_types || []
      const template = values.template || '道路平面图-A1'

      let response

      if (drawingTypes.includes('plan')) {
        response = await axios.post(`/api/v1/autocad/${projectId}/generate-plan`, {
          template_name: template,
          include_contours: values.include_contours,
          include_annotations: values.include_annotations
        })

        if (response.data.success) {
          message.success('平面图生成完成')
        }
      }

      // 可以继续添加其他图纸类型的生成

      setGenerateModalVisible(false)
      form.resetFields()
    } catch (error: any) {
      message.error(error.response?.data?.detail || '图纸生成失败')
    } finally {
      setGenerating(false)
    }
  }

  // 查看文件详情
  const handleViewDetail = (file: CADFile) => {
    setSelectedFile(file)
    setDetailModalVisible(true)
  }

  // 下载文件
  const handleDownload = (filename: string) => {
    window.open(`/api/v1/autocad/download/${filename}`, '_blank')
  }

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      'completed': { color: 'success', text: '导入完成' },
      'processing': { color: 'processing', text: '处理中' },
      'failed': { color: 'error', text: '导入失败' }
    }

    const statusInfo = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={statusInfo.color}>{statusInfo.text}</Tag>
  }

  // 获取文件类型图标
  const getFileTypeIcon = (fileType: string) => {
    return fileType.toLowerCase() === 'dwg' ?
      <FileImageOutlined style={{ color: '#1890ff' }} /> :
      <FileTextOutlined style={{ color: '#52c41a' }} />
  }

  // CAD文件表格列定义
  const cadFileColumns = [
    {
      title: '文件名',
      dataIndex: 'filename',
      key: 'filename',
      render: (filename: string, record: CADFile) => (
        <Space>
          {getFileTypeIcon(record.file_type)}
          <span>{filename}</span>
        </Space>
      )
    },
    {
      title: '文件类型',
      dataIndex: 'file_type',
      key: 'file_type',
      render: (type: string) => (
        <Tag color={type.toLowerCase() === 'dwg' ? 'blue' : 'green'}>
          {type.toUpperCase()}
        </Tag>
      )
    },
    {
      title: '文件大小',
      dataIndex: 'file_size',
      key: 'file_size',
      render: (size: number) => {
        if (size < 1024) return `${size}B`
        if (size < 1024 * 1024) return `${(size / 1024).toFixed(1)}KB`
        return `${(size / (1024 * 1024)).toFixed(1)}MB`
      }
    },
    {
      title: 'CAD版本',
      dataIndex: 'cad_version',
      key: 'cad_version',
      render: (version: string) => version || '-'
    },
    {
      title: '单位',
      dataIndex: 'units',
      key: 'units',
      render: (units: string) => units || '-'
    },
    {
      title: '图层数',
      dataIndex: 'layer_count',
      key: 'layer_count'
    },
    {
      title: '实体数',
      dataIndex: 'entity_count',
      key: 'entity_count'
    },
    {
      title: '状态',
      dataIndex: 'import_status',
      key: 'import_status',
      render: (status: string) => getStatusTag(status)
    },
    {
      title: '导入时间',
      dataIndex: 'imported_at',
      key: 'imported_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: CADFile) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          AutoCAD集成
        </Title>
        <Text type="secondary">
          支持DWG/DXF文件导入导出，专业图纸生成，符合工程制图标准
        </Text>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="CAD文件数"
              value={cadFiles.length}
              prefix={<FolderOpenOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="成功导入"
              value={cadFiles.filter(f => f.import_status === 'completed').length}
              prefix={<CheckCircleOutlined />}
              suffix="个"
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总图层数"
              value={cadFiles.reduce((sum, file) => sum + file.layer_count, 0)}
              prefix={<LineChartOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总实体数"
              value={cadFiles.reduce((sum, file) => sum + file.entity_count, 0)}
              prefix={<BarChartOutlined />}
              suffix="个"
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card
        title="CAD文件管理"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={fetchCADFiles}
            >
              刷新
            </Button>
            <Button
              icon={<ToolOutlined />}
              onClick={() => {
                fetchDrawingStandards()
                setStandardsModalVisible(true)
              }}
            >
              图纸标准
            </Button>
            <Button
              icon={<ExportOutlined />}
              onClick={() => setGenerateModalVisible(true)}
            >
              生成图纸
            </Button>
            <Button
              type="primary"
              icon={<UploadOutlined />}
              onClick={() => setImportModalVisible(true)}
            >
              导入CAD文件
            </Button>
          </Space>
        }
      >
        <Table
          columns={cadFileColumns}
          dataSource={cadFiles}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* CAD文件导入模态框 */}
      <Modal
        title="导入CAD文件"
        open={importModalVisible}
        onCancel={() => setImportModalVisible(false)}
        footer={null}
        width={600}
      >
        <div style={{ marginBottom: '16px' }}>
          <Alert
            message="支持的文件格式"
            description={
              <div>
                <p><strong>DWG文件:</strong> AutoCAD原生格式，支持所有版本</p>
                <p><strong>DXF文件:</strong> AutoCAD交换格式，通用性更好</p>
                <p><strong>文件大小限制:</strong> 最大100MB</p>
              </div>
            }
            type="info"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        </div>

        <Upload.Dragger
          name="file"
          multiple={false}
          accept=".dwg,.dxf"
          beforeUpload={handleFileUpload}
          showUploadList={false}
        >
          <p className="ant-upload-drag-icon">
            <UploadOutlined />
          </p>
          <p className="ant-upload-text">点击或拖拽文件到此区域上传</p>
          <p className="ant-upload-hint">
            支持DWG和DXF格式文件，单个文件大小不超过100MB
          </p>
        </Upload.Dragger>

        {uploading && (
          <div style={{ marginTop: '16px', textAlign: 'center' }}>
            <Progress percent={50} status="active" />
            <p>正在导入CAD文件...</p>
          </div>
        )}
      </Modal>

      {/* 图纸生成模态框 */}
      <Modal
        title="生成图纸"
        open={generateModalVisible}
        onOk={handleGenerateDrawing}
        onCancel={() => {
          setGenerateModalVisible(false)
          form.resetFields()
        }}
        confirmLoading={generating}
        okText="生成图纸"
        cancelText="取消"
        width={700}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="drawing_types"
            label="图纸类型"
            rules={[{ required: true, message: '请选择图纸类型' }]}
          >
            <Checkbox.Group>
              <Row>
                <Col span={8}>
                  <Checkbox value="plan">平面图</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="profile">纵断面图</Checkbox>
                </Col>
                <Col span={8}>
                  <Checkbox value="cross_section">横断面图</Checkbox>
                </Col>
              </Row>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item
            name="template"
            label="图纸模板"
            initialValue="道路平面图-A1"
          >
            <Select>
              {templates.map(template => (
                <Select.Option key={template.name} value={template.name}>
                  {template.name} ({template.paper_size}, 1:{template.scale})
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="include_contours"
                valuePropName="checked"
                initialValue={true}
              >
                <Checkbox>包含等高线</Checkbox>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="include_annotations"
                valuePropName="checked"
                initialValue={true}
              >
                <Checkbox>包含注释</Checkbox>
              </Form.Item>
            </Col>
          </Row>

          <Alert
            message="图纸生成说明"
            description="系统将根据当前项目的道路设计数据生成专业的CAD图纸，符合工程制图标准。生成的图纸可直接用于工程施工和审查。"
            type="info"
            showIcon
          />
        </Form>
      </Modal>

      {/* CAD文件详情模态框 */}
      <Modal
        title={`CAD文件详情 - ${selectedFile?.filename}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedFile && (
          <Tabs defaultActiveKey="basic">
            <TabPane tab="基本信息" key="basic">
              <Descriptions bordered size="small">
                <Descriptions.Item label="文件名" span={2}>
                  {selectedFile.filename}
                </Descriptions.Item>
                <Descriptions.Item label="文件类型">
                  <Tag color={selectedFile.file_type.toLowerCase() === 'dwg' ? 'blue' : 'green'}>
                    {selectedFile.file_type.toUpperCase()}
                  </Tag>
                </Descriptions.Item>
                <Descriptions.Item label="文件大小">
                  {selectedFile.file_size < 1024 * 1024
                    ? `${(selectedFile.file_size / 1024).toFixed(1)}KB`
                    : `${(selectedFile.file_size / (1024 * 1024)).toFixed(1)}MB`}
                </Descriptions.Item>
                <Descriptions.Item label="CAD版本">
                  {selectedFile.cad_version || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="单位">
                  {selectedFile.units || '-'}
                </Descriptions.Item>
                <Descriptions.Item label="图层数量">
                  {selectedFile.layer_count}
                </Descriptions.Item>
                <Descriptions.Item label="实体数量">
                  {selectedFile.entity_count}
                </Descriptions.Item>
                <Descriptions.Item label="导入状态">
                  {getStatusTag(selectedFile.import_status)}
                </Descriptions.Item>
                <Descriptions.Item label="导入时间" span={2}>
                  {new Date(selectedFile.imported_at).toLocaleString()}
                </Descriptions.Item>
                {selectedFile.bounds && (
                  <Descriptions.Item label="图纸范围" span={3}>
                    X: {selectedFile.bounds[0]?.toFixed(2)} ~ {selectedFile.bounds[2]?.toFixed(2)},
                    Y: {selectedFile.bounds[1]?.toFixed(2)} ~ {selectedFile.bounds[3]?.toFixed(2)}
                  </Descriptions.Item>
                )}
              </Descriptions>
            </TabPane>

            <TabPane tab="处理结果" key="result">
              <div>
                <Alert
                  message="导入成功"
                  description="CAD文件已成功导入并解析，可以进行后续的道路设计和分析工作。"
                  type="success"
                  showIcon
                  style={{ marginBottom: '16px' }}
                />

                <List
                  header={<div><strong>处理统计</strong></div>}
                  bordered
                  dataSource={[
                    { label: '识别的道路数量', value: '待实现' },
                    { label: '提取的点位数量', value: '待实现' },
                    { label: '转换的注释数量', value: '待实现' },
                    { label: '处理时间', value: '待实现' }
                  ]}
                  renderItem={item => (
                    <List.Item>
                      <List.Item.Meta
                        title={item.label}
                        description={item.value}
                      />
                    </List.Item>
                  )}
                />
              </div>
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 图纸标准模态框 */}
      <Modal
        title="CAD图纸绘制标准"
        open={standardsModalVisible}
        onCancel={() => setStandardsModalVisible(false)}
        footer={null}
        width={900}
      >
        {drawingStandards && (
          <Tabs defaultActiveKey="layers">
            <TabPane tab="图层标准" key="layers">
              <Table
                dataSource={Object.entries(drawingStandards.layer_standards || {}).map(([key, value]: [string, any]) => ({
                  key,
                  layer: key,
                  description: value.description,
                  color: value.color,
                  linetype: value.linetype,
                  lineweight: value.lineweight
                }))}
                columns={[
                  { title: '图层名称', dataIndex: 'layer', key: 'layer' },
                  { title: '描述', dataIndex: 'description', key: 'description' },
                  { title: '颜色', dataIndex: 'color', key: 'color' },
                  { title: '线型', dataIndex: 'linetype', key: 'linetype' },
                  { title: '线宽', dataIndex: 'lineweight', key: 'lineweight' }
                ]}
                pagination={false}
                size="small"
              />
            </TabPane>

            <TabPane tab="文字标准" key="text">
              <Descriptions bordered size="small">
                {Object.entries(drawingStandards.text_standards || {}).map(([key, value]) => (
                  <Descriptions.Item key={key} label={key} span={2}>
                    {value as string}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            </TabPane>

            <TabPane tab="比例标准" key="scale">
              <Descriptions bordered size="small">
                {Object.entries(drawingStandards.scale_standards || {}).map(([key, value]) => (
                  <Descriptions.Item key={key} label={key} span={2}>
                    {value as string}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            </TabPane>

            <TabPane tab="图纸规格" key="paper">
              <Descriptions bordered size="small">
                {Object.entries(drawingStandards.paper_sizes || {}).map(([key, value]) => (
                  <Descriptions.Item key={key} label={key}>
                    {value as string}
                  </Descriptions.Item>
                ))}
              </Descriptions>
            </TabPane>
          </Tabs>
        )}
      </Modal>
    </div>
  )
}

export default AutoCADIntegration
