"""
设计标准API端点
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.core.database import get_db

router = APIRouter()


@router.get("/")
async def get_design_standards(db: Session = Depends(get_db)):
    """获取设计标准列表"""
    return {"message": "获取设计标准列表"}


@router.get("/{standard_id}")
async def get_design_standard(standard_id: int, db: Session = Depends(get_db)):
    """获取设计标准详情"""
    return {"message": f"获取设计标准{standard_id}详情"}


@router.post("/")
async def create_design_standard(db: Session = Depends(get_db)):
    """创建设计标准"""
    return {"message": "创建设计标准成功"}


@router.put("/{standard_id}")
async def update_design_standard(standard_id: int, db: Session = Depends(get_db)):
    """更新设计标准"""
    return {"message": f"更新设计标准{standard_id}成功"}
