<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>露天矿山道路设计软件</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            color: white;
            margin-bottom: 30px;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .card {
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            transition: transform 0.3s ease;
        }
        
        .card:hover {
            transform: translateY(-5px);
        }
        
        .card h2 {
            color: #4a5568;
            margin-bottom: 15px;
            border-bottom: 2px solid #e2e8f0;
            padding-bottom: 10px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .status-item {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status-item h3 {
            color: #2d3748;
            margin-bottom: 10px;
        }
        
        .status-value {
            font-size: 2em;
            font-weight: bold;
            color: #38a169;
        }
        
        .api-section {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        .endpoint-list {
            list-style: none;
        }
        
        .endpoint-list li {
            padding: 10px;
            margin: 5px 0;
            background: #f7fafc;
            border-radius: 5px;
            border-left: 4px solid #4299e1;
        }
        
        .method {
            font-weight: bold;
            color: #2b6cb0;
            margin-right: 10px;
        }
        
        .test-section {
            margin-top: 20px;
        }
        
        .test-button {
            background: #4299e1;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            transition: background 0.3s ease;
        }
        
        .test-button:hover {
            background: #3182ce;
        }
        
        .result-area {
            background: #1a202c;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 5px;
            margin-top: 10px;
            font-family: 'Courier New', monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        
        .loading {
            color: #f6ad55;
        }
        
        .success {
            color: #68d391;
        }
        
        .error {
            color: #fc8181;
        }
        
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .feature-item {
            background: #f7fafc;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #38a169;
        }
        
        .feature-item h4 {
            color: #2d3748;
            margin-bottom: 8px;
        }
        
        .footer {
            text-align: center;
            color: white;
            margin-top: 40px;
            opacity: 0.8;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🏔️ 露天矿山道路设计软件</h1>
            <p>专业的露天矿山采矿工程道路设计软件 v1.0.0</p>
        </div>
        
        <div class="status-grid">
            <div class="status-item">
                <h3>项目状态</h3>
                <div class="status-value" id="project-status">✅ 完成</div>
            </div>
            <div class="status-item">
                <h3>功能模块</h3>
                <div class="status-value">13个</div>
            </div>
            <div class="status-item">
                <h3>代码规模</h3>
                <div class="status-value">30K+行</div>
            </div>
            <div class="status-item">
                <h3>服务状态</h3>
                <div class="status-value" id="server-status">检查中...</div>
            </div>
        </div>
        
        <div class="card">
            <h2>🚀 核心功能</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h4>📋 项目管理</h4>
                    <p>完整的项目生命周期管理</p>
                </div>
                <div class="feature-item">
                    <h4>🗺️ 地形处理</h4>
                    <p>多格式地形数据处理和分析</p>
                </div>
                <div class="feature-item">
                    <h4>🛣️ 道路设计</h4>
                    <p>符合露天矿山标准的专业设计</p>
                </div>
                <div class="feature-item">
                    <h4>🛡️ 安全分析</h4>
                    <p>全面的道路安全检测</p>
                </div>
                <div class="feature-item">
                    <h4>⚠️ 冲突检测</h4>
                    <p>智能冲突识别和解决方案</p>
                </div>
                <div class="feature-item">
                    <h4>🎯 路线优化</h4>
                    <p>多目标智能运输路线规划</p>
                </div>
                <div class="feature-item">
                    <h4>📐 AutoCAD集成</h4>
                    <p>专业CAD文件处理</p>
                </div>
                <div class="feature-item">
                    <h4>🌍 三维可视化</h4>
                    <p>基于Cesium的3D展示</p>
                </div>
                <div class="feature-item">
                    <h4>📊 系统监控</h4>
                    <p>全面的性能监控和告警</p>
                </div>
            </div>
        </div>
        
        <div class="api-section">
            <div class="card">
                <h2>🌐 API端点</h2>
                <ul class="endpoint-list">
                    <li><span class="method">GET</span>/health - 健康检查</li>
                    <li><span class="method">GET</span>/api/v1/info - API信息</li>
                    <li><span class="method">GET</span>/api/v1/projects - 项目列表</li>
                    <li><span class="method">GET</span>/api/v1/terrain - 地形数据</li>
                    <li><span class="method">GET</span>/api/v1/roads - 道路列表</li>
                    <li><span class="method">POST</span>/api/v1/roads/design - 道路设计</li>
                    <li><span class="method">POST</span>/api/v1/safety/analyze - 安全分析</li>
                    <li><span class="method">GET</span>/api/v1/monitoring/status - 系统监控</li>
                </ul>
            </div>
            
            <div class="card">
                <h2>🧪 API测试</h2>
                <div class="test-section">
                    <button class="test-button" onclick="testAPI('/health')">健康检查</button>
                    <button class="test-button" onclick="testAPI('/api/v1/info')">API信息</button>
                    <button class="test-button" onclick="testAPI('/api/v1/projects')">项目列表</button>
                    <button class="test-button" onclick="testAPI('/api/v1/roads')">道路列表</button>
                    <button class="test-button" onclick="testAPI('/api/v1/monitoring/status')">系统监控</button>
                    <button class="test-button" onclick="testPOST()">创建项目</button>
                </div>
                <div class="result-area" id="test-result">点击上方按钮测试API功能...</div>
            </div>
        </div>
        
        <div class="footer">
            <p>© 2024 露天矿山道路设计软件 - 专业的采矿工程解决方案</p>
            <p>技术栈: Python + FastAPI + React + TypeScript</p>
        </div>
    </div>
    
    <script>
        const API_BASE = 'http://localhost:8000';
        
        // 检查服务器状态
        async function checkServerStatus() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                if (response.ok) {
                    document.getElementById('server-status').textContent = '🟢 运行中';
                    document.getElementById('server-status').style.color = '#38a169';
                } else {
                    document.getElementById('server-status').textContent = '🟡 异常';
                    document.getElementById('server-status').style.color = '#f6ad55';
                }
            } catch (error) {
                document.getElementById('server-status').textContent = '🔴 离线';
                document.getElementById('server-status').style.color = '#fc8181';
            }
        }
        
        // 测试API
        async function testAPI(endpoint) {
            const resultArea = document.getElementById('test-result');
            resultArea.innerHTML = '<span class="loading">正在请求...</span>';
            
            try {
                const response = await fetch(`${API_BASE}${endpoint}`);
                const data = await response.json();
                
                resultArea.innerHTML = `<span class="success">✅ 请求成功 (${response.status})</span>\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultArea.innerHTML = `<span class="error">❌ 请求失败</span>\n\n${error.message}`;
            }
        }
        
        // 测试POST请求
        async function testPOST() {
            const resultArea = document.getElementById('test-result');
            resultArea.innerHTML = '<span class="loading">正在创建项目...</span>';
            
            try {
                const response = await fetch(`${API_BASE}/api/v1/projects`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        name: '测试项目',
                        description: '通过Web界面创建的测试项目',
                        location: '测试矿区'
                    })
                });
                
                const data = await response.json();
                resultArea.innerHTML = `<span class="success">✅ 项目创建成功 (${response.status})</span>\n\n${JSON.stringify(data, null, 2)}`;
            } catch (error) {
                resultArea.innerHTML = `<span class="error">❌ 项目创建失败</span>\n\n${error.message}`;
            }
        }
        
        // 页面加载时检查服务器状态
        window.addEventListener('load', checkServerStatus);
        
        // 每30秒检查一次服务器状态
        setInterval(checkServerStatus, 30000);
    </script>
</body>
</html>
