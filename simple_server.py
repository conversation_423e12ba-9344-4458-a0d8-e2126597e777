#!/usr/bin/env python3
"""
露天矿山道路设计软件 - 简单HTTP服务器
不依赖任何外部包，使用Python标准库
"""
import json
import os
import sys
import time
import urllib.parse
from http.server import HTTPServer, BaseHTTPRequestHandler
from pathlib import Path

class MiningRoadHandler(BaseHTTPRequestHandler):
    """露天矿山道路设计软件HTTP处理器"""
    
    def do_GET(self):
        """处理GET请求"""
        path = urllib.parse.urlparse(self.path).path

        # 处理静态文件
        if path == '/' or path == '/index.html':
            self.serve_html_file()
            return
        elif path.endswith('.html'):
            self.serve_html_file(path[1:])  # 去掉开头的/
            return

        # 设置CORS头
        self.send_cors_headers()

        if path == '/api':
            self.handle_root()
        elif path == '/health':
            self.handle_health()
        elif path == '/api/v1/info':
            self.handle_api_info()
        elif path == '/api/v1/projects':
            self.handle_projects()
        elif path == '/api/v1/terrain':
            self.handle_terrain()
        elif path == '/api/v1/roads':
            self.handle_roads()
        elif path == '/api/v1/monitoring/status':
            self.handle_monitoring()
        else:
            self.handle_404()
    
    def do_POST(self):
        """处理POST请求"""
        path = urllib.parse.urlparse(self.path).path
        
        # 设置CORS头
        self.send_cors_headers()
        
        # 读取请求体
        content_length = int(self.headers.get('Content-Length', 0))
        post_data = self.rfile.read(content_length)
        
        try:
            data = json.loads(post_data.decode('utf-8')) if post_data else {}
        except json.JSONDecodeError:
            data = {}
        
        if path == '/api/v1/projects':
            self.handle_create_project(data)
        elif path == '/api/v1/roads/design':
            self.handle_design_road(data)
        elif path == '/api/v1/safety/analyze':
            self.handle_safety_analysis(data)
        elif path == '/api/v1/conflicts/detect':
            self.handle_conflict_detection(data)
        elif path == '/api/v1/optimization/optimize':
            self.handle_route_optimization(data)
        else:
            self.handle_404()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求（CORS预检）"""
        self.send_response(200)
        self.send_cors_headers()
        self.end_headers()
    
    def send_cors_headers(self):
        """发送CORS头"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization')
    
    def send_json_response(self, data, status_code=200):
        """发送JSON响应"""
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json; charset=utf-8')
        self.send_cors_headers()
        self.end_headers()

        json_str = json.dumps(data, ensure_ascii=False, indent=2)
        self.wfile.write(json_str.encode('utf-8'))

    def serve_html_file(self, filename='index.html'):
        """服务HTML文件"""
        try:
            with open(filename, 'r', encoding='utf-8') as f:
                content = f.read()

            self.send_response(200)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.send_cors_headers()
            self.end_headers()

            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_response(404)
            self.send_header('Content-Type', 'text/html; charset=utf-8')
            self.send_cors_headers()
            self.end_headers()

            error_html = """
            <!DOCTYPE html>
            <html>
            <head><title>404 - 文件未找到</title></head>
            <body>
                <h1>404 - 文件未找到</h1>
                <p>请求的文件不存在</p>
                <a href="/">返回首页</a>
            </body>
            </html>
            """
            self.wfile.write(error_html.encode('utf-8'))
    
    def handle_root(self):
        """处理根路径"""
        response = {
            "message": "欢迎使用露天矿山道路设计软件",
            "version": "1.0.0",
            "description": "专业的露天矿山采矿工程道路设计软件",
            "endpoints": {
                "health": "/health",
                "api_info": "/api/v1/info",
                "projects": "/api/v1/projects",
                "terrain": "/api/v1/terrain",
                "roads": "/api/v1/roads",
                "monitoring": "/api/v1/monitoring/status"
            },
            "timestamp": time.strftime("%Y-%m-%dT%H:%M:%SZ")
        }
        self.send_json_response(response)
    
    def handle_health(self):
        """处理健康检查"""
        response = {
            "status": "healthy",
            "version": "1.0.0",
            "timestamp": time.time(),
            "message": "露天矿山道路设计软件运行正常",
            "uptime": "运行中",
            "system": {
                "python_version": sys.version,
                "platform": sys.platform
            }
        }
        self.send_json_response(response)
    
    def handle_api_info(self):
        """处理API信息"""
        response = {
            "name": "露天矿山道路设计软件API",
            "version": "1.0.0",
            "description": "专业的露天矿山采矿工程道路设计软件API",
            "features": [
                "项目管理",
                "地形数据处理",
                "道路设计",
                "安全分析",
                "冲突检测",
                "路线优化"
            ],
            "endpoints": {
                "projects": "/api/v1/projects",
                "terrain": "/api/v1/terrain", 
                "roads": "/api/v1/roads",
                "safety": "/api/v1/safety/analyze",
                "conflicts": "/api/v1/conflicts/detect",
                "optimization": "/api/v1/optimization/optimize"
            }
        }
        self.send_json_response(response)
    
    def handle_projects(self):
        """处理项目列表"""
        response = {
            "projects": [
                {
                    "id": 1,
                    "name": "示例项目1",
                    "description": "露天矿山道路设计示例项目",
                    "location": "山西省某露天煤矿",
                    "created_at": "2024-01-01T00:00:00Z",
                    "status": "active",
                    "progress": 75
                },
                {
                    "id": 2,
                    "name": "示例项目2", 
                    "description": "大型露天铁矿道路设计",
                    "location": "内蒙古某铁矿",
                    "created_at": "2024-01-15T00:00:00Z",
                    "status": "planning",
                    "progress": 30
                }
            ],
            "total": 2,
            "page": 1,
            "page_size": 10
        }
        self.send_json_response(response)
    
    def handle_terrain(self):
        """处理地形数据"""
        response = {
            "terrain_data": [
                {
                    "id": 1,
                    "name": "主矿区地形数据",
                    "format": "TIF",
                    "size": "15.2MB",
                    "resolution": "1m",
                    "uploaded_at": "2024-01-01T00:00:00Z",
                    "status": "processed"
                },
                {
                    "id": 2,
                    "name": "排土场地形数据",
                    "format": "LAS",
                    "size": "8.7MB", 
                    "resolution": "0.5m",
                    "uploaded_at": "2024-01-10T00:00:00Z",
                    "status": "processing"
                }
            ],
            "total": 2,
            "statistics": {
                "total_area": "500 hectares",
                "elevation_range": "1200m - 1450m",
                "average_slope": "12.5°"
            }
        }
        self.send_json_response(response)
    
    def handle_roads(self):
        """处理道路列表"""
        response = {
            "roads": [
                {
                    "id": 1,
                    "name": "主运输道路",
                    "type": "primary",
                    "length": 1500.0,
                    "width": 8.0,
                    "gradient": 6.5,
                    "turning_radius": 25.0,
                    "status": "designed",
                    "safety_score": 85.5
                },
                {
                    "id": 2,
                    "name": "辅助道路A",
                    "type": "secondary", 
                    "length": 800.0,
                    "width": 6.0,
                    "gradient": 8.0,
                    "turning_radius": 20.0,
                    "status": "under_review",
                    "safety_score": 78.2
                }
            ],
            "total": 2,
            "summary": {
                "total_length": 2300.0,
                "average_safety_score": 81.85,
                "design_standards": "符合露天矿山道路设计规范"
            }
        }
        self.send_json_response(response)
    
    def handle_monitoring(self):
        """处理系统监控"""
        response = {
            "system_status": {
                "cpu_usage": 25.5,
                "memory_usage": 45.2,
                "disk_usage": 60.8,
                "active_users": 1,
                "running_tasks": 0
            },
            "service_status": {
                "api_server": "running",
                "database": "simulated",
                "cache": "not_available"
            },
            "performance": {
                "requests_per_minute": 12,
                "average_response_time": "150ms",
                "error_rate": "0%"
            }
        }
        self.send_json_response(response)
    
    def handle_create_project(self, data):
        """处理创建项目"""
        response = {
            "id": 3,
            "name": data.get("name", "新项目"),
            "description": data.get("description", ""),
            "location": data.get("location", ""),
            "created_at": time.strftime("%Y-%m-%dT%H:%M:%SZ"),
            "status": "active",
            "progress": 0,
            "message": "项目创建成功"
        }
        self.send_json_response(response, 201)
    
    def handle_design_road(self, data):
        """处理道路设计"""
        response = {
            "message": "道路设计完成",
            "road_id": 3,
            "design_result": {
                "length": data.get("length", 1000.0),
                "width": data.get("width", 6.0),
                "gradient": data.get("gradient", 5.0),
                "turning_radius": 25.0,
                "safety_score": 85.5,
                "estimated_cost": "¥2,500,000",
                "construction_time": "6个月"
            }
        }
        self.send_json_response(response)
    
    def handle_safety_analysis(self, data):
        """处理安全分析"""
        response = {
            "message": "安全分析完成",
            "analysis_result": {
                "overall_score": 82.5,
                "safety_level": "良好",
                "issues_found": 2,
                "critical_issues": 0,
                "warning_issues": 1,
                "caution_issues": 1,
                "recommendations": [
                    "建议在里程500m处增设警示标志",
                    "建议优化里程800m处的转弯半径"
                ],
                "detailed_analysis": {
                    "sight_distance": "符合标准",
                    "slope_stability": "需要关注",
                    "drainage": "良好"
                }
            }
        }
        self.send_json_response(response)
    
    def handle_conflict_detection(self, data):
        """处理冲突检测"""
        response = {
            "message": "冲突检测完成",
            "conflicts": [
                {
                    "id": 1,
                    "type": "terrain_conflict",
                    "severity": "medium",
                    "location": "里程300m",
                    "description": "道路与地形高程冲突",
                    "suggested_solution": "调整道路高程或增加填方"
                }
            ],
            "total_conflicts": 1,
            "summary": {
                "high_severity": 0,
                "medium_severity": 1,
                "low_severity": 0
            }
        }
        self.send_json_response(response)
    
    def handle_route_optimization(self, data):
        """处理路线优化"""
        response = {
            "message": "路线优化完成",
            "optimization_result": {
                "original_cost": 1000000,
                "optimized_cost": 850000,
                "cost_reduction": "15%",
                "optimized_route": {
                    "length": 1200.0,
                    "estimated_time": 45.5,
                    "fuel_consumption": 120.0
                },
                "improvements": [
                    "减少转弯次数",
                    "优化坡度分布",
                    "缩短运输距离"
                ]
            }
        }
        self.send_json_response(response)
    
    def handle_404(self):
        """处理404错误"""
        response = {
            "error": "Not Found",
            "message": "请求的资源不存在",
            "available_endpoints": [
                "/",
                "/health", 
                "/api/v1/info",
                "/api/v1/projects",
                "/api/v1/terrain",
                "/api/v1/roads"
            ]
        }
        self.send_json_response(response, 404)
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {self.address_string()} - {format % args}")

def setup_environment():
    """设置环境"""
    # 创建必要的目录
    directories = ['logs', 'uploads', 'data']
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
    
    print("✅ 环境设置完成")

def main():
    """主函数"""
    print("=" * 60)
    print("🏔️  露天矿山道路设计软件")
    print("   专业的露天矿山采矿工程道路设计软件")
    print("   版本: 1.0.0 (简化版)")
    print("=" * 60)
    
    # 设置环境
    setup_environment()
    
    # 启动服务器
    server_address = ('localhost', 8000)
    
    try:
        httpd = HTTPServer(server_address, MiningRoadHandler)
        print(f"\n🚀 服务器启动成功!")
        print(f"🌐 服务地址: http://localhost:8000")
        print(f"🔍 健康检查: http://localhost:8000/health")
        print(f"📚 API信息: http://localhost:8000/api/v1/info")
        print(f"📊 系统监控: http://localhost:8000/api/v1/monitoring/status")
        print(f"\n按 Ctrl+C 停止服务器")
        print("=" * 60)
        
        httpd.serve_forever()
        
    except KeyboardInterrupt:
        print("\n\n🛑 正在停止服务器...")
        httpd.shutdown()
        print("✅ 服务器已停止")
    except Exception as e:
        print(f"❌ 服务器启动失败: {e}")

if __name__ == "__main__":
    main()
