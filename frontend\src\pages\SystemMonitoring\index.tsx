import React, { useState, useEffect } from 'react'
import { 
  Card, Row, Col, Statistic, Progress, Alert, Table, 
  Tag, Space, Button, Tabs, List, Typography, 
  Descriptions, Modal, Form, InputNumber, message,
  Badge, Tooltip, Timeline
} from 'antd'
import { 
  DashboardOutlined, <PERSON><PERSON>Outlined, SettingOutlined,
  ReloadOutlined, Check<PERSON>ircleOutlined, Exclamation<PERSON>ircleOutlined,
  CloseCircleOutlined, InfoCircleOutlined, <PERSON><PERSON>Outlined,
  MemoryOutlined, HddOutlined, ApiOutlined, DatabaseOutlined,
  FileTextOutlined, BugOutlined
} from '@ant-design/icons'
import axios from 'axios'

const { TabPane } = Tabs
const { Text, Title } = Typography

interface SystemMetrics {
  timestamp: string
  cpu: {
    usage_percent: number
    count: number
    process_usage: number
  }
  memory: {
    total: number
    used: number
    usage_percent: number
  }
  disk: {
    total: number
    used: number
    usage_percent: number
  }
}

interface ApplicationMetrics {
  timestamp: string
  performance: {
    api_requests_total: number
    api_errors_total: number
    error_rate_percent: number
    average_response_time: number
    database_queries_total: number
    file_uploads_total: number
    calculations_performed: number
  }
  health_status: string
}

interface Alert {
  type: string
  severity: string
  message: string
  value: number
  threshold: number
  timestamp: string
}

interface DashboardData {
  timestamp: string
  overview: {
    health_status: string
    cpu_usage: number
    memory_usage: number
    disk_usage: number
    active_alerts: number
  }
  performance: {
    api_requests: number
    api_errors: number
    database_queries: number
    file_uploads: number
    calculations: number
  }
  recent_alerts: Alert[]
  trends: {
    cpu_average: number
    memory_average: number
    disk_average: number
  }
}

const SystemMonitoring: React.FC = () => {
  const [dashboardData, setDashboardData] = useState<DashboardData | null>(null)
  const [systemMetrics, setSystemMetrics] = useState<SystemMetrics | null>(null)
  const [appMetrics, setAppMetrics] = useState<ApplicationMetrics | null>(null)
  const [alerts, setAlerts] = useState<Alert[]>([])
  const [diagnostics, setDiagnostics] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [autoRefresh, setAutoRefresh] = useState(true)
  const [thresholdModalVisible, setThresholdModalVisible] = useState(false)
  const [form] = Form.useForm()

  // 获取仪表板数据
  const fetchDashboardData = async () => {
    try {
      const response = await axios.get('/api/v1/monitoring/status/dashboard')
      setDashboardData(response.data)
    } catch (error) {
      message.error('获取仪表板数据失败')
    }
  }

  // 获取系统指标
  const fetchSystemMetrics = async () => {
    try {
      const response = await axios.get('/api/v1/monitoring/metrics/system')
      setSystemMetrics(response.data)
    } catch (error) {
      message.error('获取系统指标失败')
    }
  }

  // 获取应用指标
  const fetchApplicationMetrics = async () => {
    try {
      const response = await axios.get('/api/v1/monitoring/metrics/application')
      setAppMetrics(response.data)
    } catch (error) {
      message.error('获取应用指标失败')
    }
  }

  // 获取告警信息
  const fetchAlerts = async () => {
    try {
      const response = await axios.get('/api/v1/monitoring/alerts')
      setAlerts(response.data.alerts)
    } catch (error) {
      message.error('获取告警信息失败')
    }
  }

  // 运行诊断
  const runDiagnostics = async () => {
    try {
      const response = await axios.get('/api/v1/monitoring/diagnostics')
      setDiagnostics(response.data)
    } catch (error) {
      message.error('运行诊断失败')
    }
  }

  // 刷新所有数据
  const refreshAllData = async () => {
    setLoading(true)
    try {
      await Promise.all([
        fetchDashboardData(),
        fetchSystemMetrics(),
        fetchApplicationMetrics(),
        fetchAlerts(),
        runDiagnostics()
      ])
    } finally {
      setLoading(false)
    }
  }

  // 自动刷新
  useEffect(() => {
    refreshAllData()
    
    let interval: NodeJS.Timeout
    if (autoRefresh) {
      interval = setInterval(refreshAllData, 30000) // 30秒刷新一次
    }
    
    return () => {
      if (interval) clearInterval(interval)
    }
  }, [autoRefresh])

  // 获取健康状态颜色和图标
  const getHealthStatusDisplay = (status: string) => {
    const statusMap = {
      'healthy': { color: 'success', icon: <CheckCircleOutlined />, text: '健康' },
      'warning': { color: 'warning', icon: <ExclamationCircleOutlined />, text: '警告' },
      'critical': { color: 'error', icon: <CloseCircleOutlined />, text: '严重' },
      'unknown': { color: 'default', icon: <InfoCircleOutlined />, text: '未知' }
    }
    
    return statusMap[status as keyof typeof statusMap] || statusMap.unknown
  }

  // 获取告警严重程度颜色
  const getAlertSeverityColor = (severity: string) => {
    const colorMap = {
      'info': 'blue',
      'warning': 'orange',
      'critical': 'red'
    }
    return colorMap[severity as keyof typeof colorMap] || 'default'
  }

  // 格式化字节数
  const formatBytes = (bytes: number) => {
    if (bytes === 0) return '0 B'
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  // 告警表格列定义
  const alertColumns = [
    {
      title: '类型',
      dataIndex: 'type',
      key: 'type',
      render: (type: string) => {
        const typeMap = {
          'cpu_high': 'CPU使用率高',
          'memory_high': '内存使用率高',
          'disk_high': '磁盘使用率高',
          'error_rate_high': 'API错误率高'
        }
        return typeMap[type as keyof typeof typeMap] || type
      }
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => (
        <Tag color={getAlertSeverityColor(severity)}>
          {severity.toUpperCase()}
        </Tag>
      )
    },
    {
      title: '消息',
      dataIndex: 'message',
      key: 'message'
    },
    {
      title: '当前值',
      dataIndex: 'value',
      key: 'value',
      render: (value: number) => `${value.toFixed(1)}`
    },
    {
      title: '阈值',
      dataIndex: 'threshold',
      key: 'threshold',
      render: (threshold: number) => `${threshold.toFixed(1)}`
    },
    {
      title: '时间',
      dataIndex: 'timestamp',
      key: 'timestamp',
      render: (time: string) => new Date(time).toLocaleString()
    }
  ]

  if (loading && !dashboardData) {
    return (
      <Card>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <ThunderboltOutlined style={{ fontSize: '48px', color: '#1890ff' }} />
          <div style={{ marginTop: '16px' }}>正在加载监控数据...</div>
        </div>
      </Card>
    )
  }

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <Title level={2} style={{ margin: 0 }}>
          系统监控
        </Title>
        <Text type="secondary">
          实时监控系统性能、资源使用情况和应用程序健康状态
        </Text>
      </div>

      {/* 控制按钮 */}
      <div style={{ marginBottom: '24px' }}>
        <Space>
          <Button 
            type="primary"
            icon={<ReloadOutlined />}
            onClick={refreshAllData}
            loading={loading}
          >
            刷新数据
          </Button>
          <Button 
            icon={<SettingOutlined />}
            onClick={() => setThresholdModalVisible(true)}
          >
            告警设置
          </Button>
          <Button 
            icon={<BugOutlined />}
            onClick={runDiagnostics}
          >
            运行诊断
          </Button>
          <span>
            自动刷新: 
            <Button 
              type="link" 
              size="small"
              onClick={() => setAutoRefresh(!autoRefresh)}
            >
              {autoRefresh ? '开启' : '关闭'}
            </Button>
          </span>
        </Space>
      </div>

      {/* 系统概览 */}
      {dashboardData && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="系统状态"
                value={getHealthStatusDisplay(dashboardData.overview.health_status).text}
                prefix={getHealthStatusDisplay(dashboardData.overview.health_status).icon}
                valueStyle={{ 
                  color: dashboardData.overview.health_status === 'healthy' ? '#52c41a' : 
                         dashboardData.overview.health_status === 'warning' ? '#faad14' : '#ff4d4f'
                }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="CPU使用率"
                value={dashboardData.overview.cpu_usage}
                suffix="%"
                prefix={<ThunderboltOutlined />}
                valueStyle={{ 
                  color: dashboardData.overview.cpu_usage > 80 ? '#ff4d4f' : 
                         dashboardData.overview.cpu_usage > 60 ? '#faad14' : '#52c41a'
                }}
              />
              <Progress 
                percent={dashboardData.overview.cpu_usage} 
                size="small" 
                status={dashboardData.overview.cpu_usage > 80 ? 'exception' : 'normal'}
                style={{ marginTop: '8px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="内存使用率"
                value={dashboardData.overview.memory_usage}
                suffix="%"
                prefix={<MemoryOutlined />}
                valueStyle={{ 
                  color: dashboardData.overview.memory_usage > 85 ? '#ff4d4f' : 
                         dashboardData.overview.memory_usage > 70 ? '#faad14' : '#52c41a'
                }}
              />
              <Progress 
                percent={dashboardData.overview.memory_usage} 
                size="small"
                status={dashboardData.overview.memory_usage > 85 ? 'exception' : 'normal'}
                style={{ marginTop: '8px' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="活跃告警"
                value={dashboardData.overview.active_alerts}
                prefix={<AlertOutlined />}
                suffix="个"
                valueStyle={{ 
                  color: dashboardData.overview.active_alerts > 0 ? '#ff4d4f' : '#52c41a'
                }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 主要内容 */}
      <Tabs defaultActiveKey="overview">
        <TabPane tab="系统概览" key="overview">
          <Row gutter={[16, 16]}>
            {/* 系统资源 */}
            <Col xs={24} lg={12}>
              <Card title="系统资源" size="small">
                {systemMetrics && (
                  <Space direction="vertical" style={{ width: '100%' }}>
                    <div>
                      <Text strong>CPU使用率</Text>
                      <Progress 
                        percent={systemMetrics.cpu.usage_percent} 
                        status={systemMetrics.cpu.usage_percent > 80 ? 'exception' : 'normal'}
                      />
                    </div>
                    <div>
                      <Text strong>内存使用率</Text>
                      <Progress 
                        percent={systemMetrics.memory.usage_percent}
                        status={systemMetrics.memory.usage_percent > 85 ? 'exception' : 'normal'}
                      />
                      <Text type="secondary">
                        {formatBytes(systemMetrics.memory.used)} / {formatBytes(systemMetrics.memory.total)}
                      </Text>
                    </div>
                    <div>
                      <Text strong>磁盘使用率</Text>
                      <Progress 
                        percent={systemMetrics.disk.usage_percent}
                        status={systemMetrics.disk.usage_percent > 90 ? 'exception' : 'normal'}
                      />
                      <Text type="secondary">
                        {formatBytes(systemMetrics.disk.used)} / {formatBytes(systemMetrics.disk.total)}
                      </Text>
                    </div>
                  </Space>
                )}
              </Card>
            </Col>

            {/* 应用性能 */}
            <Col xs={24} lg={12}>
              <Card title="应用性能" size="small">
                {dashboardData && (
                  <Row gutter={[16, 16]}>
                    <Col span={12}>
                      <Statistic
                        title="API请求"
                        value={dashboardData.performance.api_requests}
                        prefix={<ApiOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="API错误"
                        value={dashboardData.performance.api_errors}
                        prefix={<BugOutlined />}
                        valueStyle={{ color: dashboardData.performance.api_errors > 0 ? '#ff4d4f' : '#52c41a' }}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="数据库查询"
                        value={dashboardData.performance.database_queries}
                        prefix={<DatabaseOutlined />}
                      />
                    </Col>
                    <Col span={12}>
                      <Statistic
                        title="文件上传"
                        value={dashboardData.performance.file_uploads}
                        prefix={<FileTextOutlined />}
                      />
                    </Col>
                  </Row>
                )}
              </Card>
            </Col>
          </Row>
        </TabPane>

        <TabPane tab="告警管理" key="alerts">
          <Card 
            title={`当前告警 (${alerts.length})`}
            extra={
              <Badge 
                count={alerts.length} 
                status={alerts.length > 0 ? 'error' : 'success'}
              />
            }
          >
            {alerts.length > 0 ? (
              <Table
                columns={alertColumns}
                dataSource={alerts}
                rowKey="timestamp"
                size="small"
                pagination={false}
              />
            ) : (
              <Alert
                message="系统运行正常"
                description="当前没有活跃的告警"
                type="success"
                showIcon
              />
            )}
          </Card>
        </TabPane>

        <TabPane tab="系统诊断" key="diagnostics">
          <Card title="系统诊断">
            {diagnostics ? (
              <Space direction="vertical" style={{ width: '100%' }}>
                <Alert
                  message={`诊断完成 - 总体状态: ${diagnostics.overall_status}`}
                  description={`共检查 ${diagnostics.summary.total_checks} 项，通过 ${diagnostics.summary.passed} 项，警告 ${diagnostics.summary.warnings} 项，失败 ${diagnostics.summary.failed} 项`}
                  type={diagnostics.overall_status === 'pass' ? 'success' : 
                        diagnostics.overall_status === 'warning' ? 'warning' : 'error'}
                  showIcon
                />
                
                <Timeline>
                  {diagnostics.checks.map((check: any, index: number) => (
                    <Timeline.Item
                      key={index}
                      color={check.status === 'pass' ? 'green' : 
                             check.status === 'warning' ? 'orange' : 'red'}
                      dot={check.status === 'pass' ? <CheckCircleOutlined /> :
                           check.status === 'warning' ? <ExclamationCircleOutlined /> :
                           <CloseCircleOutlined />}
                    >
                      <div>
                        <Text strong>{check.name}</Text>
                        <Tag color={check.status === 'pass' ? 'success' : 
                                   check.status === 'warning' ? 'warning' : 'error'}>
                          {check.value}
                        </Tag>
                      </div>
                      <div>
                        <Text type="secondary">{check.message}</Text>
                      </div>
                    </Timeline.Item>
                  ))}
                </Timeline>
              </Space>
            ) : (
              <div style={{ textAlign: 'center', padding: '50px' }}>
                <Button 
                  type="primary" 
                  icon={<BugOutlined />}
                  onClick={runDiagnostics}
                >
                  运行系统诊断
                </Button>
              </div>
            )}
          </Card>
        </TabPane>
      </Tabs>

      {/* 告警阈值设置模态框 */}
      <Modal
        title="告警阈值设置"
        open={thresholdModalVisible}
        onCancel={() => setThresholdModalVisible(false)}
        footer={null}
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="cpu_usage"
            label="CPU使用率阈值 (%)"
            initialValue={80}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="memory_usage"
            label="内存使用率阈值 (%)"
            initialValue={85}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="disk_usage"
            label="磁盘使用率阈值 (%)"
            initialValue={90}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item
            name="error_rate"
            label="API错误率阈值 (%)"
            initialValue={5}
          >
            <InputNumber min={0} max={100} style={{ width: '100%' }} />
          </Form.Item>
          
          <Form.Item>
            <Space>
              <Button type="primary" htmlType="submit">
                保存设置
              </Button>
              <Button onClick={() => setThresholdModalVisible(false)}>
                取消
              </Button>
            </Space>
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default SystemMonitoring
