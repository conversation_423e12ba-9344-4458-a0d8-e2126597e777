"""
纵断面分析器
"""
import numpy as np
import math
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class GradePoint:
    """坡度点"""
    chainage: float
    elevation: float
    gradient: float
    curve_radius: Optional[float] = None
    curve_type: Optional[str] = None  # 'crest' or 'sag'


@dataclass
class VerticalCurve:
    """竖曲线"""
    start_chainage: float
    end_chainage: float
    length: float
    radius: float
    grade_in: float
    grade_out: float
    curve_type: str
    highest_point: Optional[float] = None
    lowest_point: Optional[float] = None


@dataclass
class LongitudinalAnalysis:
    """纵断面分析结果"""
    total_length: float
    grade_points: List[GradePoint]
    vertical_curves: List[VerticalCurve]
    max_gradient: float
    min_gradient: float
    average_gradient: float
    total_rise: float
    total_fall: float
    grade_statistics: Dict[str, float]
    compliance_check: Dict[str, bool]


class LongitudinalProfileAnalyzer:
    """纵断面分析器"""
    
    def __init__(self):
        self.max_gradient = 8.0  # 最大坡度(%)
        self.min_curve_radius = 200.0  # 最小竖曲线半径(米)
        self.analysis_interval = 10.0  # 分析间隔(米)
        
    def analyze_longitudinal_profile(self, 
                                   road_points: List[Tuple[float, float, float]],
                                   chainages: List[float],
                                   design_speed: float = 30.0) -> LongitudinalAnalysis:
        """分析纵断面"""
        try:
            logger.info("开始纵断面分析")
            
            # 计算坡度点
            grade_points = self._calculate_grade_points(road_points, chainages)
            
            # 识别竖曲线
            vertical_curves = self._identify_vertical_curves(grade_points)
            
            # 计算统计信息
            statistics = self._calculate_statistics(grade_points)
            
            # 合规性检查
            compliance = self._check_compliance(grade_points, vertical_curves, design_speed)
            
            analysis = LongitudinalAnalysis(
                total_length=chainages[-1] - chainages[0] if len(chainages) > 1 else 0,
                grade_points=grade_points,
                vertical_curves=vertical_curves,
                max_gradient=statistics['max_gradient'],
                min_gradient=statistics['min_gradient'],
                average_gradient=statistics['average_gradient'],
                total_rise=statistics['total_rise'],
                total_fall=statistics['total_fall'],
                grade_statistics=statistics,
                compliance_check=compliance
            )
            
            logger.info(f"纵断面分析完成，分析了 {len(grade_points)} 个坡度点")
            return analysis
            
        except Exception as e:
            logger.error(f"纵断面分析失败: {str(e)}")
            raise
    
    def _calculate_grade_points(self, 
                              road_points: List[Tuple[float, float, float]],
                              chainages: List[float]) -> List[GradePoint]:
        """计算坡度点"""
        try:
            grade_points = []
            
            for i in range(len(road_points)):
                chainage = chainages[i] if i < len(chainages) else 0
                elevation = road_points[i][2]
                
                # 计算坡度
                if i == 0:
                    gradient = 0.0
                else:
                    prev_point = road_points[i-1]
                    prev_chainage = chainages[i-1] if i-1 < len(chainages) else 0
                    
                    horizontal_distance = chainage - prev_chainage
                    vertical_distance = elevation - prev_point[2]
                    
                    if horizontal_distance > 0:
                        gradient = (vertical_distance / horizontal_distance) * 100
                    else:
                        gradient = 0.0
                
                grade_point = GradePoint(
                    chainage=chainage,
                    elevation=elevation,
                    gradient=gradient
                )
                
                grade_points.append(grade_point)
            
            return grade_points
            
        except Exception as e:
            logger.error(f"计算坡度点失败: {str(e)}")
            return []
    
    def _identify_vertical_curves(self, grade_points: List[GradePoint]) -> List[VerticalCurve]:
        """识别竖曲线"""
        try:
            vertical_curves = []
            
            # 寻找坡度变化点
            for i in range(1, len(grade_points) - 1):
                prev_gradient = grade_points[i-1].gradient
                curr_gradient = grade_points[i].gradient
                next_gradient = grade_points[i+1].gradient
                
                # 检查是否为坡度变化点
                gradient_change = abs(next_gradient - prev_gradient)
                
                if gradient_change > 2.0:  # 坡度变化超过2%
                    # 确定曲线类型
                    if prev_gradient < next_gradient:
                        curve_type = 'sag'  # 凹形曲线
                    else:
                        curve_type = 'crest'  # 凸形曲线
                    
                    # 估算曲线参数
                    curve_length = gradient_change * 10  # 简化计算
                    curve_radius = self._calculate_curve_radius(
                        prev_gradient, next_gradient, curve_length
                    )
                    
                    start_chainage = grade_points[i].chainage - curve_length / 2
                    end_chainage = grade_points[i].chainage + curve_length / 2
                    
                    vertical_curve = VerticalCurve(
                        start_chainage=start_chainage,
                        end_chainage=end_chainage,
                        length=curve_length,
                        radius=curve_radius,
                        grade_in=prev_gradient,
                        grade_out=next_gradient,
                        curve_type=curve_type
                    )
                    
                    # 计算最高/最低点
                    if curve_type == 'crest':
                        vertical_curve.highest_point = grade_points[i].elevation
                    else:
                        vertical_curve.lowest_point = grade_points[i].elevation
                    
                    vertical_curves.append(vertical_curve)
            
            return vertical_curves
            
        except Exception as e:
            logger.error(f"识别竖曲线失败: {str(e)}")
            return []
    
    def _calculate_curve_radius(self, 
                              grade_in: float, 
                              grade_out: float, 
                              curve_length: float) -> float:
        """计算竖曲线半径"""
        try:
            # 竖曲线半径公式: R = L / (|i1 - i2| / 100)
            grade_diff = abs(grade_in - grade_out) / 100
            
            if grade_diff > 0:
                radius = curve_length / grade_diff
            else:
                radius = float('inf')
            
            return max(radius, self.min_curve_radius)
            
        except Exception as e:
            logger.error(f"计算竖曲线半径失败: {str(e)}")
            return self.min_curve_radius
    
    def _calculate_statistics(self, grade_points: List[GradePoint]) -> Dict[str, float]:
        """计算统计信息"""
        try:
            if not grade_points:
                return {}
            
            gradients = [gp.gradient for gp in grade_points[1:]]  # 跳过第一个点
            
            max_gradient = max(gradients) if gradients else 0.0
            min_gradient = min(gradients) if gradients else 0.0
            avg_gradient = sum(gradients) / len(gradients) if gradients else 0.0
            
            # 计算总上升和下降
            total_rise = 0.0
            total_fall = 0.0
            
            for i in range(1, len(grade_points)):
                elevation_diff = grade_points[i].elevation - grade_points[i-1].elevation
                if elevation_diff > 0:
                    total_rise += elevation_diff
                else:
                    total_fall += abs(elevation_diff)
            
            # 坡度分布统计
            steep_sections = len([g for g in gradients if abs(g) > 6.0])
            moderate_sections = len([g for g in gradients if 3.0 < abs(g) <= 6.0])
            gentle_sections = len([g for g in gradients if abs(g) <= 3.0])
            
            return {
                'max_gradient': max_gradient,
                'min_gradient': min_gradient,
                'average_gradient': avg_gradient,
                'total_rise': total_rise,
                'total_fall': total_fall,
                'steep_sections': steep_sections,
                'moderate_sections': moderate_sections,
                'gentle_sections': gentle_sections,
                'gradient_variance': np.var(gradients) if gradients else 0.0
            }
            
        except Exception as e:
            logger.error(f"计算统计信息失败: {str(e)}")
            return {}
    
    def _check_compliance(self, 
                        grade_points: List[GradePoint],
                        vertical_curves: List[VerticalCurve],
                        design_speed: float) -> Dict[str, bool]:
        """合规性检查"""
        try:
            compliance = {}
            
            # 检查最大坡度
            max_gradient = max((abs(gp.gradient) for gp in grade_points[1:]), default=0)
            compliance['max_gradient_ok'] = max_gradient <= self.max_gradient
            
            # 检查竖曲线半径
            min_required_radius = self._calculate_min_curve_radius(design_speed)
            curve_radius_ok = all(vc.radius >= min_required_radius for vc in vertical_curves)
            compliance['curve_radius_ok'] = curve_radius_ok
            
            # 检查坡度变化率
            max_grade_change = 0.0
            for i in range(1, len(grade_points) - 1):
                grade_change = abs(grade_points[i+1].gradient - grade_points[i-1].gradient)
                max_grade_change = max(max_grade_change, grade_change)
            
            compliance['grade_change_ok'] = max_grade_change <= 10.0  # 最大坡度变化10%
            
            # 检查连续陡坡长度
            steep_length = 0.0
            max_steep_length = 0.0
            
            for i in range(1, len(grade_points)):
                if abs(grade_points[i].gradient) > 6.0:
                    if i > 0:
                        steep_length += grade_points[i].chainage - grade_points[i-1].chainage
                else:
                    max_steep_length = max(max_steep_length, steep_length)
                    steep_length = 0.0
            
            max_steep_length = max(max_steep_length, steep_length)
            compliance['steep_length_ok'] = max_steep_length <= 500.0  # 连续陡坡不超过500米
            
            return compliance
            
        except Exception as e:
            logger.error(f"合规性检查失败: {str(e)}")
            return {}
    
    def _calculate_min_curve_radius(self, design_speed: float) -> float:
        """计算最小竖曲线半径"""
        try:
            # 根据设计速度计算最小竖曲线半径
            # 简化公式: R_min = V² / (3.6² × a)
            # 其中 V 为设计速度(km/h)，a 为舒适度系数(0.3 m/s²)
            
            comfort_acceleration = 0.3  # m/s²
            min_radius = (design_speed / 3.6) ** 2 / comfort_acceleration
            
            return max(min_radius, self.min_curve_radius)
            
        except Exception as e:
            logger.error(f"计算最小竖曲线半径失败: {str(e)}")
            return self.min_curve_radius
    
    def generate_profile_chart_data(self, 
                                  grade_points: List[GradePoint],
                                  vertical_curves: List[VerticalCurve]) -> Dict:
        """生成纵断面图表数据"""
        try:
            # 高程数据
            elevation_data = [
                {'chainage': gp.chainage, 'elevation': gp.elevation}
                for gp in grade_points
            ]
            
            # 坡度数据
            gradient_data = [
                {'chainage': gp.chainage, 'gradient': gp.gradient}
                for gp in grade_points[1:]  # 跳过第一个点
            ]
            
            # 竖曲线数据
            curve_data = [
                {
                    'start_chainage': vc.start_chainage,
                    'end_chainage': vc.end_chainage,
                    'type': vc.curve_type,
                    'radius': vc.radius,
                    'grade_in': vc.grade_in,
                    'grade_out': vc.grade_out
                }
                for vc in vertical_curves
            ]
            
            return {
                'elevation_profile': elevation_data,
                'gradient_profile': gradient_data,
                'vertical_curves': curve_data,
                'chart_config': {
                    'x_axis': 'chainage',
                    'y_axis_elevation': 'elevation',
                    'y_axis_gradient': 'gradient',
                    'title': '道路纵断面图'
                }
            }
            
        except Exception as e:
            logger.error(f"生成纵断面图表数据失败: {str(e)}")
            return {}
    
    def generate_profile_report(self, analysis: LongitudinalAnalysis) -> Dict:
        """生成纵断面分析报告"""
        try:
            report = {
                'summary': {
                    'total_length': analysis.total_length,
                    'max_gradient': analysis.max_gradient,
                    'min_gradient': analysis.min_gradient,
                    'average_gradient': analysis.average_gradient,
                    'total_rise': analysis.total_rise,
                    'total_fall': analysis.total_fall,
                    'vertical_curves_count': len(analysis.vertical_curves)
                },
                'compliance': analysis.compliance_check,
                'statistics': analysis.grade_statistics,
                'recommendations': self._generate_profile_recommendations(analysis)
            }
            
            return report
            
        except Exception as e:
            logger.error(f"生成纵断面报告失败: {str(e)}")
            return {}
    
    def _generate_profile_recommendations(self, analysis: LongitudinalAnalysis) -> List[str]:
        """生成纵断面建议"""
        recommendations = []
        
        # 坡度建议
        if analysis.max_gradient > self.max_gradient:
            recommendations.append(f"最大坡度{analysis.max_gradient:.1f}%超过限值，建议优化线形")
        
        if analysis.grade_statistics.get('steep_sections', 0) > 5:
            recommendations.append("陡坡段较多，建议增加缓坡段或设置避险车道")
        
        # 竖曲线建议
        short_curves = [vc for vc in analysis.vertical_curves if vc.radius < self.min_curve_radius * 1.5]
        if short_curves:
            recommendations.append(f"发现{len(short_curves)}个半径较小的竖曲线，建议增大半径")
        
        # 合规性建议
        if not analysis.compliance_check.get('max_gradient_ok', True):
            recommendations.append("存在坡度超限问题，需要调整纵断面设计")
        
        if not analysis.compliance_check.get('curve_radius_ok', True):
            recommendations.append("竖曲线半径不满足要求，需要增大曲线半径")
        
        return recommendations
