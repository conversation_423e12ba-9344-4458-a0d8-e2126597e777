"""
地形数据处理算法模块
"""
import numpy as np
import os
import json
from typing import Dict, List, Tuple, Optional, Union
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


@dataclass
class TerrainBounds:
    """地形边界"""
    min_x: float
    min_y: float
    max_x: float
    max_y: float
    min_z: float
    max_z: float


@dataclass
class TerrainMetadata:
    """地形元数据"""
    bounds: TerrainBounds
    resolution: float
    point_count: int
    data_type: str
    coordinate_system: str
    file_format: str
    file_size: int


class TerrainProcessor:
    """地形数据处理器"""
    
    def __init__(self):
        self.supported_formats = [
            '.tif', '.tiff',  # GeoTIFF
            '.las', '.laz',   # LiDAR点云
            '.xyz', '.txt',   # ASCII点云
            '.asc',           # ESRI ASCII Grid
            '.dem',           # DEM文件
            '.shp',           # Shapefile等高线
            '.json', '.geojson'  # GeoJSON
        ]
        
    def validate_file(self, file_path: str) -> Tuple[bool, str]:
        """验证文件格式和完整性"""
        try:
            if not os.path.exists(file_path):
                return False, "文件不存在"
            
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                return False, f"不支持的文件格式: {file_ext}"
            
            file_size = os.path.getsize(file_path)
            if file_size == 0:
                return False, "文件为空"
            
            # 检查文件是否可读
            try:
                with open(file_path, 'rb') as f:
                    f.read(1024)  # 读取前1KB检查
            except Exception as e:
                return False, f"文件读取错误: {str(e)}"
            
            return True, "文件验证通过"
            
        except Exception as e:
            return False, f"验证过程出错: {str(e)}"
    
    def extract_metadata(self, file_path: str) -> Optional[TerrainMetadata]:
        """提取地形数据元数据"""
        try:
            file_ext = os.path.splitext(file_path)[1].lower()
            file_size = os.path.getsize(file_path)
            
            if file_ext in ['.tif', '.tiff']:
                return self._extract_geotiff_metadata(file_path, file_size)
            elif file_ext in ['.las', '.laz']:
                return self._extract_las_metadata(file_path, file_size)
            elif file_ext in ['.xyz', '.txt']:
                return self._extract_xyz_metadata(file_path, file_size)
            elif file_ext == '.asc':
                return self._extract_asc_metadata(file_path, file_size)
            else:
                # 默认元数据
                return TerrainMetadata(
                    bounds=TerrainBounds(0, 0, 1000, 1000, 0, 100),
                    resolution=1.0,
                    point_count=0,
                    data_type="unknown",
                    coordinate_system="EPSG:4326",
                    file_format=file_ext,
                    file_size=file_size
                )
                
        except Exception as e:
            logger.error(f"提取元数据失败: {str(e)}")
            return None
    
    def _extract_geotiff_metadata(self, file_path: str, file_size: int) -> TerrainMetadata:
        """提取GeoTIFF元数据"""
        try:
            # 这里应该使用GDAL库，但为了简化，我们使用模拟数据
            # import rasterio
            # with rasterio.open(file_path) as src:
            #     bounds = src.bounds
            #     transform = src.transform
            #     crs = src.crs
            
            # 模拟数据
            bounds = TerrainBounds(
                min_x=100000, min_y=200000, max_x=101000, max_y=201000,
                min_z=50, max_z=150
            )
            
            return TerrainMetadata(
                bounds=bounds,
                resolution=1.0,
                point_count=1000000,
                data_type="DEM",
                coordinate_system="EPSG:4326",
                file_format=".tif",
                file_size=file_size
            )
            
        except Exception as e:
            logger.error(f"提取GeoTIFF元数据失败: {str(e)}")
            raise
    
    def _extract_las_metadata(self, file_path: str, file_size: int) -> TerrainMetadata:
        """提取LAS点云元数据"""
        try:
            # 这里应该使用laspy库
            # import laspy
            # with laspy.open(file_path) as las:
            #     header = las.header
            #     points = las.points
            
            # 模拟数据
            bounds = TerrainBounds(
                min_x=100000, min_y=200000, max_x=101000, max_y=201000,
                min_z=45, max_z=155
            )
            
            return TerrainMetadata(
                bounds=bounds,
                resolution=0.1,
                point_count=5000000,
                data_type="point_cloud",
                coordinate_system="EPSG:4326",
                file_format=".las",
                file_size=file_size
            )
            
        except Exception as e:
            logger.error(f"提取LAS元数据失败: {str(e)}")
            raise
    
    def _extract_xyz_metadata(self, file_path: str, file_size: int) -> TerrainMetadata:
        """提取XYZ点云元数据"""
        try:
            points = []
            with open(file_path, 'r') as f:
                for i, line in enumerate(f):
                    if i >= 1000:  # 只读取前1000行来估算
                        break
                    try:
                        parts = line.strip().split()
                        if len(parts) >= 3:
                            x, y, z = float(parts[0]), float(parts[1]), float(parts[2])
                            points.append([x, y, z])
                    except:
                        continue
            
            if not points:
                raise ValueError("无法解析XYZ数据")
            
            points = np.array(points)
            bounds = TerrainBounds(
                min_x=float(np.min(points[:, 0])),
                min_y=float(np.min(points[:, 1])),
                max_x=float(np.max(points[:, 0])),
                max_y=float(np.max(points[:, 1])),
                min_z=float(np.min(points[:, 2])),
                max_z=float(np.max(points[:, 2]))
            )
            
            # 估算总点数
            estimated_points = len(points) * (file_size / (len(points) * 50))  # 假设每行约50字节
            
            return TerrainMetadata(
                bounds=bounds,
                resolution=1.0,
                point_count=int(estimated_points),
                data_type="point_cloud",
                coordinate_system="EPSG:4326",
                file_format=".xyz",
                file_size=file_size
            )
            
        except Exception as e:
            logger.error(f"提取XYZ元数据失败: {str(e)}")
            raise
    
    def _extract_asc_metadata(self, file_path: str, file_size: int) -> TerrainMetadata:
        """提取ASC格式元数据"""
        try:
            with open(file_path, 'r') as f:
                # 读取头部信息
                ncols = int(f.readline().split()[1])
                nrows = int(f.readline().split()[1])
                xllcorner = float(f.readline().split()[1])
                yllcorner = float(f.readline().split()[1])
                cellsize = float(f.readline().split()[1])
                nodata_value = float(f.readline().split()[1])
            
            bounds = TerrainBounds(
                min_x=xllcorner,
                min_y=yllcorner,
                max_x=xllcorner + ncols * cellsize,
                max_y=yllcorner + nrows * cellsize,
                min_z=0,  # 需要读取数据才能确定
                max_z=100  # 需要读取数据才能确定
            )
            
            return TerrainMetadata(
                bounds=bounds,
                resolution=cellsize,
                point_count=ncols * nrows,
                data_type="grid",
                coordinate_system="EPSG:4326",
                file_format=".asc",
                file_size=file_size
            )
            
        except Exception as e:
            logger.error(f"提取ASC元数据失败: {str(e)}")
            raise
    
    def process_terrain_data(self, file_path: str, output_dir: str, 
                           processing_params: Dict = None) -> Dict:
        """处理地形数据"""
        try:
            # 验证文件
            is_valid, message = self.validate_file(file_path)
            if not is_valid:
                raise ValueError(message)
            
            # 提取元数据
            metadata = self.extract_metadata(file_path)
            if not metadata:
                raise ValueError("无法提取文件元数据")
            
            # 创建输出目录
            os.makedirs(output_dir, exist_ok=True)
            
            # 处理参数
            params = processing_params or {}
            target_resolution = params.get('target_resolution', metadata.resolution)
            interpolation_method = params.get('interpolation_method', 'linear')
            filter_noise = params.get('filter_noise', True)
            
            # 执行处理
            result = {
                'status': 'success',
                'metadata': {
                    'bounds': {
                        'min_x': metadata.bounds.min_x,
                        'min_y': metadata.bounds.min_y,
                        'max_x': metadata.bounds.max_x,
                        'max_y': metadata.bounds.max_y,
                        'min_z': metadata.bounds.min_z,
                        'max_z': metadata.bounds.max_z
                    },
                    'resolution': metadata.resolution,
                    'point_count': metadata.point_count,
                    'data_type': metadata.data_type,
                    'coordinate_system': metadata.coordinate_system,
                    'file_format': metadata.file_format,
                    'file_size': metadata.file_size
                },
                'processing_params': {
                    'target_resolution': target_resolution,
                    'interpolation_method': interpolation_method,
                    'filter_noise': filter_noise
                },
                'output_files': {
                    'processed_dem': os.path.join(output_dir, 'processed_dem.tif'),
                    'contours': os.path.join(output_dir, 'contours.shp'),
                    'metadata': os.path.join(output_dir, 'metadata.json')
                }
            }
            
            # 保存元数据
            metadata_file = os.path.join(output_dir, 'metadata.json')
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(result['metadata'], f, indent=2, ensure_ascii=False)
            
            logger.info(f"地形数据处理完成: {file_path}")
            return result
            
        except Exception as e:
            logger.error(f"地形数据处理失败: {str(e)}")
            return {
                'status': 'error',
                'error_message': str(e)
            }
    
    def generate_contours(self, dem_path: str, output_path: str, 
                         interval: float = 5.0) -> bool:
        """生成等高线"""
        try:
            # 这里应该使用GDAL生成等高线
            # gdal.ContourGenerate(dem_band, interval, 0, [], 0, 0, output_layer, 0, 0)
            
            logger.info(f"等高线生成完成: {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"等高线生成失败: {str(e)}")
            return False
    
    def calculate_slope(self, dem_data: np.ndarray, resolution: float) -> np.ndarray:
        """计算坡度"""
        try:
            # 计算梯度
            dy, dx = np.gradient(dem_data, resolution)
            slope = np.arctan(np.sqrt(dx*dx + dy*dy)) * 180 / np.pi
            return slope
            
        except Exception as e:
            logger.error(f"坡度计算失败: {str(e)}")
            return np.zeros_like(dem_data)
    
    def calculate_aspect(self, dem_data: np.ndarray, resolution: float) -> np.ndarray:
        """计算坡向"""
        try:
            dy, dx = np.gradient(dem_data, resolution)
            aspect = np.arctan2(-dy, dx) * 180 / np.pi
            aspect = (aspect + 360) % 360  # 转换为0-360度
            return aspect
            
        except Exception as e:
            logger.error(f"坡向计算失败: {str(e)}")
            return np.zeros_like(dem_data)
