"""
三维可视化服务
"""
import os
import json
import numpy as np
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.orm import Session
from datetime import datetime
import asyncio

from app.models.terrain import TerrainData
from app.models.road import Road, RoadSegment
from app.models.project import Project
from app.core.config import settings
import logging

logger = logging.getLogger(__name__)


class VisualizationService:
    """三维可视化服务类"""
    
    def __init__(self):
        self.tile_cache_dir = os.path.join(settings.UPLOAD_DIR, 'terrain_tiles')
        os.makedirs(self.tile_cache_dir, exist_ok=True)
    
    async def get_project_3d_data(self, 
                                 db: Session,
                                 project_id: int) -> Dict[str, Any]:
        """获取项目三维可视化数据"""
        try:
            logger.info(f"获取项目三维数据: {project_id}")
            
            # 获取项目信息
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise ValueError("项目不存在")
            
            # 获取地形数据
            terrain_data = await self._get_terrain_visualization_data(db, project_id)
            
            # 获取道路数据
            road_data = await self._get_road_visualization_data(db, project_id)
            
            # 计算项目边界
            project_bounds = self._calculate_project_bounds(terrain_data, road_data)
            
            # 生成可视化配置
            visualization_config = self._generate_visualization_config(project)
            
            return {
                'project_id': project_id,
                'project_name': project.name,
                'terrain_data': terrain_data,
                'road_data': road_data,
                'project_bounds': project_bounds,
                'visualization_config': visualization_config,
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"获取项目三维数据失败: {str(e)}")
            raise
    
    async def _get_terrain_visualization_data(self, 
                                            db: Session,
                                            project_id: int) -> Dict[str, Any]:
        """获取地形可视化数据"""
        try:
            # 获取项目地形数据
            terrain_records = db.query(TerrainData).filter(
                TerrainData.project_id == project_id
            ).all()
            
            if not terrain_records:
                return {
                    'has_terrain': False,
                    'bounds': None,
                    'elevation_range': None,
                    'resolution': None
                }
            
            # 合并多个地形数据
            all_bounds = []
            all_elevations = []
            
            for terrain in terrain_records:
                if terrain.bounds:
                    all_bounds.append(terrain.bounds)
                
                if terrain.statistics and 'elevation_range' in terrain.statistics:
                    elevation_range = terrain.statistics['elevation_range']
                    all_elevations.extend([elevation_range['min'], elevation_range['max']])
            
            # 计算总体边界
            if all_bounds:
                min_x = min(bounds[0] for bounds in all_bounds)
                min_y = min(bounds[1] for bounds in all_bounds)
                max_x = max(bounds[2] for bounds in all_bounds)
                max_y = max(bounds[3] for bounds in all_bounds)
                combined_bounds = [min_x, min_y, max_x, max_y]
            else:
                combined_bounds = None
            
            # 计算高程范围
            elevation_range = None
            if all_elevations:
                elevation_range = {
                    'min': min(all_elevations),
                    'max': max(all_elevations)
                }
            
            # 获取最高分辨率
            resolution = None
            if terrain_records:
                resolutions = [t.resolution for t in terrain_records if t.resolution]
                if resolutions:
                    resolution = min(resolutions)  # 最高分辨率（最小值）
            
            return {
                'has_terrain': True,
                'bounds': combined_bounds,
                'elevation_range': elevation_range,
                'resolution': resolution,
                'terrain_count': len(terrain_records),
                'terrain_files': [
                    {
                        'id': t.id,
                        'filename': t.filename,
                        'bounds': t.bounds,
                        'resolution': t.resolution
                    }
                    for t in terrain_records
                ]
            }
            
        except Exception as e:
            logger.error(f"获取地形可视化数据失败: {str(e)}")
            return {
                'has_terrain': False,
                'bounds': None,
                'elevation_range': None,
                'resolution': None
            }
    
    async def _get_road_visualization_data(self, 
                                         db: Session,
                                         project_id: int) -> List[Dict[str, Any]]:
        """获取道路可视化数据"""
        try:
            # 获取项目道路
            roads = db.query(Road).filter(Road.project_id == project_id).all()
            
            road_data = []
            
            for road in roads:
                # 获取道路段
                segments = db.query(RoadSegment).filter(RoadSegment.road_id == road.id).all()
                
                # 构建道路坐标序列
                coordinates = []
                for segment in segments:
                    if segment.start_point:
                        coordinates.append(segment.start_point)
                    if segment.end_point:
                        coordinates.append(segment.end_point)
                
                # 去重相邻重复点
                unique_coordinates = []
                for i, coord in enumerate(coordinates):
                    if i == 0 or coord != coordinates[i-1]:
                        unique_coordinates.append(coord)
                
                if unique_coordinates:
                    road_item = {
                        'id': str(road.id),
                        'name': road.name,
                        'type': road.road_type,
                        'coordinates': unique_coordinates,
                        'width': road.road_width,
                        'design_speed': road.design_speed,
                        'total_length': road.total_length,
                        'segments': [
                            {
                                'id': seg.id,
                                'name': seg.segment_name,
                                'start_chainage': seg.start_chainage,
                                'end_chainage': seg.end_chainage,
                                'length': seg.length,
                                'gradient': seg.gradient
                            }
                            for seg in segments
                        ]
                    }
                    road_data.append(road_item)
            
            return road_data
            
        except Exception as e:
            logger.error(f"获取道路可视化数据失败: {str(e)}")
            return []
    
    def _calculate_project_bounds(self, 
                                terrain_data: Dict[str, Any],
                                road_data: List[Dict[str, Any]]) -> Optional[List[float]]:
        """计算项目边界"""
        try:
            bounds_list = []
            
            # 添加地形边界
            if terrain_data.get('bounds'):
                bounds_list.append(terrain_data['bounds'])
            
            # 添加道路边界
            for road in road_data:
                coordinates = road.get('coordinates', [])
                if coordinates:
                    lons = [coord[0] for coord in coordinates]
                    lats = [coord[1] for coord in coordinates]
                    
                    road_bounds = [min(lons), min(lats), max(lons), max(lats)]
                    bounds_list.append(road_bounds)
            
            if not bounds_list:
                return None
            
            # 计算总体边界
            min_x = min(bounds[0] for bounds in bounds_list)
            min_y = min(bounds[1] for bounds in bounds_list)
            max_x = max(bounds[2] for bounds in bounds_list)
            max_y = max(bounds[3] for bounds in bounds_list)
            
            # 添加一些边距
            margin_x = (max_x - min_x) * 0.1
            margin_y = (max_y - min_y) * 0.1
            
            return [
                min_x - margin_x,
                min_y - margin_y,
                max_x + margin_x,
                max_y + margin_y
            ]
            
        except Exception as e:
            logger.error(f"计算项目边界失败: {str(e)}")
            return None
    
    def _generate_visualization_config(self, project: Project) -> Dict[str, Any]:
        """生成可视化配置"""
        try:
            return {
                'camera': {
                    'default_height': 5000,
                    'min_height': 100,
                    'max_height': 50000,
                    'tilt_angle': -45
                },
                'terrain': {
                    'exaggeration': 1.0,
                    'lighting': True,
                    'fog': True,
                    'show_water': True
                },
                'roads': {
                    'show_centerline': True,
                    'show_surface': True,
                    'show_labels': True,
                    'line_width': 5,
                    'surface_opacity': 0.7
                },
                'rendering': {
                    'enable_lighting': True,
                    'enable_shadows': True,
                    'enable_atmosphere': True,
                    'enable_fog': True,
                    'msaa_samples': 4
                },
                'interaction': {
                    'enable_picking': True,
                    'enable_info_box': True,
                    'enable_selection': True
                }
            }
            
        except Exception as e:
            logger.error(f"生成可视化配置失败: {str(e)}")
            return {}
    
    async def generate_terrain_tiles(self, 
                                   db: Session,
                                   terrain_id: int,
                                   zoom_levels: List[int] = None) -> Dict[str, Any]:
        """生成地形瓦片"""
        try:
            if zoom_levels is None:
                zoom_levels = [8, 9, 10, 11, 12, 13, 14, 15]
            
            logger.info(f"生成地形瓦片: terrain_id={terrain_id}")
            
            # 获取地形数据
            terrain = db.query(TerrainData).filter(TerrainData.id == terrain_id).first()
            if not terrain:
                raise ValueError("地形数据不存在")
            
            # 创建瓦片目录
            tile_dir = os.path.join(self.tile_cache_dir, str(terrain_id))
            os.makedirs(tile_dir, exist_ok=True)
            
            # 这里应该实现实际的瓦片生成逻辑
            # 由于涉及复杂的地理数据处理，这里提供简化的实现
            
            generated_tiles = []
            for zoom in zoom_levels:
                zoom_dir = os.path.join(tile_dir, str(zoom))
                os.makedirs(zoom_dir, exist_ok=True)
                
                # 计算该缩放级别的瓦片数量
                if terrain.bounds:
                    tile_count = self._calculate_tile_count(terrain.bounds, zoom)
                    generated_tiles.append({
                        'zoom': zoom,
                        'tile_count': tile_count,
                        'directory': zoom_dir
                    })
            
            return {
                'terrain_id': terrain_id,
                'tile_directory': tile_dir,
                'zoom_levels': zoom_levels,
                'generated_tiles': generated_tiles,
                'total_tiles': sum(tile['tile_count'] for tile in generated_tiles),
                'generated_at': datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"生成地形瓦片失败: {str(e)}")
            raise
    
    def _calculate_tile_count(self, bounds: List[float], zoom: int) -> int:
        """计算指定缩放级别的瓦片数量"""
        try:
            # 简化的瓦片数量计算
            # 实际实现需要根据Web Mercator投影计算
            
            west, south, east, north = bounds
            
            # 计算经纬度范围
            lon_range = east - west
            lat_range = north - south
            
            # 根据缩放级别计算瓦片大小
            tile_size = 360.0 / (2 ** zoom)
            
            # 计算瓦片数量
            tiles_x = max(1, int(np.ceil(lon_range / tile_size)))
            tiles_y = max(1, int(np.ceil(lat_range / tile_size)))
            
            return tiles_x * tiles_y
            
        except Exception as e:
            logger.error(f"计算瓦片数量失败: {str(e)}")
            return 0
    
    async def get_visualization_statistics(self, 
                                         db: Session,
                                         project_id: int) -> Dict[str, Any]:
        """获取可视化统计信息"""
        try:
            # 获取地形统计
            terrain_count = db.query(TerrainData).filter(
                TerrainData.project_id == project_id
            ).count()
            
            # 获取道路统计
            road_count = db.query(Road).filter(
                Road.project_id == project_id
            ).count()
            
            # 获取道路段统计
            segment_count = db.query(RoadSegment).join(Road).filter(
                Road.project_id == project_id
            ).count()
            
            # 计算总长度
            roads = db.query(Road).filter(Road.project_id == project_id).all()
            total_length = sum(road.total_length or 0 for road in roads)
            
            return {
                'project_id': project_id,
                'terrain_files': terrain_count,
                'road_count': road_count,
                'segment_count': segment_count,
                'total_road_length': total_length,
                'has_3d_data': terrain_count > 0 and road_count > 0,
                'visualization_ready': terrain_count > 0 or road_count > 0
            }
            
        except Exception as e:
            logger.error(f"获取可视化统计失败: {str(e)}")
            return {}
    
    def get_supported_formats(self) -> Dict[str, List[str]]:
        """获取支持的三维格式"""
        return {
            'terrain_formats': ['tif', 'tiff', 'dem', 'asc', 'xyz'],
            'model_formats': ['gltf', 'glb', 'obj', 'dae'],
            'texture_formats': ['jpg', 'jpeg', 'png', 'tiff'],
            'output_formats': ['tiles', 'quantized_mesh', 'heightmap']
        }
    
    def get_rendering_presets(self) -> Dict[str, Dict[str, Any]]:
        """获取渲染预设"""
        return {
            'high_quality': {
                'name': '高质量',
                'description': '最佳视觉效果，适合演示和汇报',
                'settings': {
                    'terrain_exaggeration': 1.5,
                    'enable_shadows': True,
                    'enable_lighting': True,
                    'enable_atmosphere': True,
                    'msaa_samples': 8,
                    'fog_density': 0.0002
                }
            },
            'balanced': {
                'name': '平衡模式',
                'description': '性能和质量的平衡，适合日常使用',
                'settings': {
                    'terrain_exaggeration': 1.0,
                    'enable_shadows': True,
                    'enable_lighting': True,
                    'enable_atmosphere': True,
                    'msaa_samples': 4,
                    'fog_density': 0.0001
                }
            },
            'performance': {
                'name': '性能优先',
                'description': '最佳性能，适合低配置设备',
                'settings': {
                    'terrain_exaggeration': 1.0,
                    'enable_shadows': False,
                    'enable_lighting': False,
                    'enable_atmosphere': False,
                    'msaa_samples': 1,
                    'fog_density': 0
                }
            }
        }
