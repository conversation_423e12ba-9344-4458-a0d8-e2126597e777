.cesium-3d-viewer {
  position: relative;
  width: 100%;
  height: 100%;
}

.cesium-container {
  width: 100%;
  height: 100%;
  background-color: #000;
  border-radius: 6px;
  overflow: hidden;
}

.cesium-controls {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cesium-controls .ant-card-body {
  padding: 12px;
}

/* Cesium工具栏样式覆盖 */
.cesium-viewer-toolbar {
  background: rgba(42, 42, 42, 0.8);
  border-radius: 6px;
}

.cesium-button {
  background: rgba(48, 48, 48, 0.8);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
}

.cesium-button:hover {
  background: rgba(64, 64, 64, 0.9);
  border-color: rgba(255, 255, 255, 0.4);
}

/* 信息框样式 */
.cesium-infoBox {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.cesium-infoBox-title {
  background: #1890ff;
  color: white;
  font-weight: 500;
}

/* 选择指示器样式 */
.cesium-selection-wrapper {
  border: 2px solid #1890ff;
  border-radius: 4px;
}

/* 全屏模式样式 */
.cesium-3d-viewer:-webkit-full-screen {
  width: 100vw;
  height: 100vh;
}

.cesium-3d-viewer:-moz-full-screen {
  width: 100vw;
  height: 100vh;
}

.cesium-3d-viewer:fullscreen {
  width: 100vw;
  height: 100vh;
}

.cesium-3d-viewer:fullscreen .cesium-container {
  height: 100vh;
}

.cesium-3d-viewer:fullscreen .cesium-controls {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
}

/* 加载动画 */
.cesium-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  background: #f5f5f5;
  border-radius: 6px;
}

.cesium-loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #1890ff;
  border-radius: 50%;
  animation: cesium-spin 1s linear infinite;
}

@keyframes cesium-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .cesium-controls {
    width: 250px;
    top: 5px;
    right: 5px;
  }
  
  .cesium-controls .ant-card-body {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .cesium-controls {
    width: 200px;
    position: relative;
    top: 0;
    right: 0;
    margin-bottom: 10px;
  }
  
  .cesium-container {
    height: 400px;
  }
}
