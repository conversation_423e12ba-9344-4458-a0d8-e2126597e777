import React from 'react'
import { Button, Result } from 'antd'
import { ReloadOutlined, HomeOutlined } from '@ant-design/icons'

interface ErrorFallbackProps {
  error: Error
  resetErrorBoundary: () => void
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error }) => {
  const handleGoHome = () => {
    window.location.href = '/'
  }

  const handleReload = () => {
    window.location.reload()
  }

  return (
    <div style={{
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%)'
    }}>
      <Result
        status="error"
        title="应用出现错误"
        subTitle="很抱歉，应用遇到了一个意外错误。请尝试刷新页面或返回首页。"
        extra={[
          <Button 
            type="primary" 
            icon={<ReloadOutlined />} 
            onClick={handleReload}
            key="reload"
          >
            刷新页面
          </Button>,
          <Button 
            icon={<HomeOutlined />} 
            onClick={handleGoHome}
            key="home"
          >
            返回首页
          </Button>,
        ]}
        style={{
          background: '#fff',
          borderRadius: '8px',
          padding: '40px',
          margin: '20px',
          maxWidth: '600px'
        }}
      >
        {process.env.NODE_ENV === 'development' && (
          <details style={{ 
            marginTop: '20px', 
            textAlign: 'left',
            background: '#f5f5f5',
            padding: '16px',
            borderRadius: '4px'
          }}>
            <summary style={{ 
              cursor: 'pointer', 
              fontWeight: 'bold',
              marginBottom: '8px'
            }}>
              错误详情 (开发模式)
            </summary>
            <pre style={{
              background: '#000',
              color: '#fff',
              padding: '12px',
              borderRadius: '4px',
              overflow: 'auto',
              fontSize: '12px',
              lineHeight: '1.4'
            }}>
              {error.stack}
            </pre>
          </details>
        )}
      </Result>
    </div>
  )
}

export default ErrorFallback
