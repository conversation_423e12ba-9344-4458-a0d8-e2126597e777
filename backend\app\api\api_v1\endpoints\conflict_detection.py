"""
冲突检测API端点
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session

from app.core.database import get_db

router = APIRouter()


@router.post("/{project_id}/detect")
async def detect_conflicts(project_id: int, db: Session = Depends(get_db)):
    """检测道路冲突"""
    return {"message": f"检测项目{project_id}道路冲突"}


@router.get("/{project_id}/conflicts")
async def get_conflicts(project_id: int, db: Session = Depends(get_db)):
    """获取冲突列表"""
    return {"message": f"获取项目{project_id}冲突列表"}


@router.post("/{project_id}/conflicts/{conflict_id}/resolve")
async def resolve_conflict(project_id: int, conflict_id: int, db: Session = Depends(get_db)):
    """解决冲突"""
    return {"message": f"解决冲突{conflict_id}"}


@router.get("/{project_id}/conflicts/report")
async def generate_conflict_report(project_id: int, db: Session = Depends(get_db)):
    """生成冲突报告"""
    return {"message": f"生成项目{project_id}冲突报告"}
