"""
数据库连接和会话管理
"""
from sqlalchemy import create_engine, MetaData
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
import redis
from typing import Generator

from .config import settings, get_database_url, get_redis_url


# 数据库引擎配置
engine = create_engine(
    get_database_url(),
    pool_pre_ping=True,
    pool_recycle=300,
    pool_size=20,
    max_overflow=30,
    echo=settings.DEBUG,
)

# 会话工厂
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

# 基础模型类
Base = declarative_base()

# 元数据
metadata = MetaData()


def get_db() -> Generator[Session, None, None]:
    """
    获取数据库会话
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()


# Redis连接
redis_client = redis.Redis.from_url(
    get_redis_url(),
    decode_responses=True,
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True,
    health_check_interval=30,
)


def get_redis() -> redis.Redis:
    """
    获取Redis客户端
    """
    return redis_client


# 数据库初始化函数
def init_db() -> None:
    """
    初始化数据库表
    """
    # 导入所有模型以确保它们被注册到Base.metadata
    from app.models import (
        user, project, terrain, road, design_standard,
        conflict_detection, safety_analysis, optimization
    )
    
    # 创建所有表
    Base.metadata.create_all(bind=engine)


# 数据库健康检查
def check_db_health() -> bool:
    """
    检查数据库连接健康状态
    """
    try:
        db = SessionLocal()
        db.execute("SELECT 1")
        db.close()
        return True
    except Exception:
        return False


# Redis健康检查
def check_redis_health() -> bool:
    """
    检查Redis连接健康状态
    """
    try:
        redis_client.ping()
        return True
    except Exception:
        return False


# 缓存装饰器
def cache_result(key_prefix: str, ttl: int = settings.CACHE_TTL):
    """
    缓存函数结果的装饰器
    
    Args:
        key_prefix: 缓存键前缀
        ttl: 缓存过期时间(秒)
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 构建缓存键
            cache_key = f"{key_prefix}:{hash(str(args) + str(kwargs))}"
            
            # 尝试从缓存获取结果
            cached_result = redis_client.get(cache_key)
            if cached_result:
                import json
                return json.loads(cached_result)
            
            # 执行函数并缓存结果
            result = func(*args, **kwargs)
            import json
            redis_client.setex(cache_key, ttl, json.dumps(result, default=str))
            
            return result
        return wrapper
    return decorator


# 数据库事务装饰器
def db_transaction(func):
    """
    数据库事务装饰器
    """
    def wrapper(*args, **kwargs):
        db = SessionLocal()
        try:
            result = func(db, *args, **kwargs)
            db.commit()
            return result
        except Exception as e:
            db.rollback()
            raise e
        finally:
            db.close()
    return wrapper


# 批量操作支持
class BulkOperations:
    """批量数据库操作类"""
    
    def __init__(self, batch_size: int = 1000):
        self.batch_size = batch_size
        self.db = SessionLocal()
    
    def __enter__(self):
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.db.commit()
        else:
            self.db.rollback()
        self.db.close()
    
    def bulk_insert(self, model_class, data_list: list):
        """批量插入数据"""
        for i in range(0, len(data_list), self.batch_size):
            batch = data_list[i:i + self.batch_size]
            self.db.bulk_insert_mappings(model_class, batch)
    
    def bulk_update(self, model_class, data_list: list):
        """批量更新数据"""
        for i in range(0, len(data_list), self.batch_size):
            batch = data_list[i:i + self.batch_size]
            self.db.bulk_update_mappings(model_class, batch)


# 连接池监控
def get_connection_pool_status():
    """获取连接池状态"""
    pool = engine.pool
    return {
        "size": pool.size(),
        "checked_in": pool.checkedin(),
        "checked_out": pool.checkedout(),
        "overflow": pool.overflow(),
        "invalid": pool.invalid(),
    }
