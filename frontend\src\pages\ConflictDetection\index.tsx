import React, { useState, useEffect } from 'react'
import {
  Card, Button, Table, Tag, Space, Modal, Form, Select,
  Checkbox, message, Row, Col, Statistic, Progress,
  Descriptions, Tooltip, Popconfirm, Alert, Input, InputNumber
} from 'antd'
import {
  PlayCircleOutlined, EyeOutlined, CheckCircleOutlined,
  Exclamation<PERSON>ircleOutlined, WarningOutlined, CloseCircleOutlined,
  BugOutlined, EnvironmentOutlined, SafetyOutlined,
  Bar<PERSON>hartOutlined, FileTextOutlined, ReloadOutlined
} from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import axios from 'axios'

interface ConflictData {
  id: number
  title: string
  description: string
  severity: string
  status: string
  location: number[]
  chainage?: number
  confidence_score?: number
  detected_at: string
  resolved_at?: string
}

interface ConflictStatistics {
  total_conflicts: number
  resolved_conflicts: number
  unresolved_conflicts: number
  critical_conflicts: number
  high_conflicts: number
  resolution_rate: number
  type_distribution: Record<string, number>
}

const ConflictDetection: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [conflictList, setConflictList] = useState<ConflictData[]>([])
  const [statistics, setStatistics] = useState<ConflictStatistics | null>(null)
  const [loading, setLoading] = useState(false)
  const [detecting, setDetecting] = useState(false)
  const [detectionModalVisible, setDetectionModalVisible] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [resolveModalVisible, setResolveModalVisible] = useState(false)
  const [selectedConflict, setSelectedConflict] = useState<ConflictData | null>(null)
  const [form] = Form.useForm()
  const [resolveForm] = Form.useForm()

  // 获取冲突列表
  const fetchConflictList = async () => {
    if (!projectId) return

    setLoading(true)
    try {
      const response = await axios.get(`/api/v1/conflicts/${projectId}/conflicts`)
      setConflictList(response.data)
    } catch (error) {
      message.error('获取冲突列表失败')
    } finally {
      setLoading(false)
    }
  }

  // 获取统计信息
  const fetchStatistics = async () => {
    if (!projectId) return

    try {
      const response = await axios.get(`/api/v1/conflicts/${projectId}/statistics`)
      setStatistics(response.data.statistics)
    } catch (error) {
      console.error('获取统计信息失败:', error)
    }
  }

  useEffect(() => {
    fetchConflictList()
    fetchStatistics()
  }, [projectId])

  // 开始冲突检测
  const handleStartDetection = async () => {
    try {
      const values = await form.validateFields()
      setDetecting(true)

      const detectionParams = {
        road_ids: values.road_ids,
        detection_types: values.detection_types,
        sensitivity_level: values.sensitivity_level,
        include_resolved: values.include_resolved
      }

      await axios.post(`/api/v1/conflicts/${projectId}/detect`, detectionParams)
      message.success('冲突检测完成')
      setDetectionModalVisible(false)
      form.resetFields()
      fetchConflictList()
      fetchStatistics()
    } catch (error) {
      message.error('冲突检测失败')
    } finally {
      setDetecting(false)
    }
  }

  // 查看冲突详情
  const handleViewDetail = (conflict: ConflictData) => {
    setSelectedConflict(conflict)
    setDetailModalVisible(true)
  }

  // 解决冲突
  const handleResolveConflict = (conflict: ConflictData) => {
    setSelectedConflict(conflict)
    setResolveModalVisible(true)
  }

  // 提交解决方案
  const handleSubmitResolution = async () => {
    if (!selectedConflict) return

    try {
      const values = await resolveForm.validateFields()

      await axios.post(
        `/api/v1/conflicts/${projectId}/conflicts/${selectedConflict.id}/resolve`,
        values
      )

      message.success('冲突解决成功')
      setResolveModalVisible(false)
      resolveForm.resetFields()
      fetchConflictList()
      fetchStatistics()
    } catch (error) {
      message.error('解决冲突失败')
    }
  }

  // 获取严重程度标签
  const getSeverityTag = (severity: string) => {
    const severityMap = {
      low: { color: 'blue', text: '低', icon: <BugOutlined /> },
      medium: { color: 'orange', text: '中', icon: <ExclamationCircleOutlined /> },
      high: { color: 'red', text: '高', icon: <WarningOutlined /> },
      critical: { color: 'purple', text: '严重', icon: <CloseCircleOutlined /> }
    }
    const config = severityMap[severity as keyof typeof severityMap] || { color: 'default', text: severity, icon: null }
    return (
      <Tag color={config.color} icon={config.icon}>
        {config.text}
      </Tag>
    )
  }

  // 获取状态标签
  const getStatusTag = (status: string) => {
    const statusMap = {
      detected: { color: 'orange', text: '已检测' },
      analyzing: { color: 'blue', text: '分析中' },
      resolved: { color: 'green', text: '已解决' },
      ignored: { color: 'gray', text: '已忽略' }
    }
    const config = statusMap[status as keyof typeof statusMap] || { color: 'default', text: status }
    return <Tag color={config.color}>{config.text}</Tag>
  }

  // 表格列定义
  const columns = [
    {
      title: '冲突描述',
      dataIndex: 'title',
      key: 'title',
      render: (text: string, record: ConflictData) => (
        <Space direction="vertical" size="small">
          <span style={{ fontWeight: 'bold' }}>{text}</span>
          <span style={{ color: '#666', fontSize: '12px' }}>
            {record.description.length > 50
              ? `${record.description.substring(0, 50)}...`
              : record.description}
          </span>
        </Space>
      )
    },
    {
      title: '严重程度',
      dataIndex: 'severity',
      key: 'severity',
      render: (severity: string) => getSeverityTag(severity),
      filters: [
        { text: '严重', value: 'critical' },
        { text: '高', value: 'high' },
        { text: '中', value: 'medium' },
        { text: '低', value: 'low' }
      ],
      onFilter: (value: any, record: ConflictData) => record.severity === value
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      render: (status: string) => getStatusTag(status),
      filters: [
        { text: '已检测', value: 'detected' },
        { text: '分析中', value: 'analyzing' },
        { text: '已解决', value: 'resolved' },
        { text: '已忽略', value: 'ignored' }
      ],
      onFilter: (value: any, record: ConflictData) => record.status === value
    },
    {
      title: '位置信息',
      key: 'location',
      render: (record: ConflictData) => (
        <Space direction="vertical" size="small">
          <span>
            <EnvironmentOutlined />
            {record.location[0]?.toFixed(1)}, {record.location[1]?.toFixed(1)}
          </span>
          {record.chainage && (
            <span style={{ fontSize: '12px', color: '#666' }}>
              里程: {record.chainage.toFixed(1)}m
            </span>
          )}
        </Space>
      )
    },
    {
      title: '置信度',
      dataIndex: 'confidence_score',
      key: 'confidence_score',
      render: (score: number) => score ? (
        <Progress
          percent={Math.round(score * 100)}
          size="small"
          status={score > 0.8 ? 'success' : score > 0.6 ? 'normal' : 'exception'}
        />
      ) : '-'
    },
    {
      title: '检测时间',
      dataIndex: 'detected_at',
      key: 'detected_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: ConflictData) => (
        <Space>
          <Tooltip title="查看详情">
            <Button
              type="text"
              icon={<EyeOutlined />}
              size="small"
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>

          {record.status !== 'resolved' && (
            <Tooltip title="解决冲突">
              <Button
                type="text"
                icon={<CheckCircleOutlined />}
                size="small"
                onClick={() => handleResolveConflict(record)}
              />
            </Tooltip>
          )}
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          冲突检测
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          自动检测道路设计中的各类冲突，提供解决方案建议
        </p>
      </div>

      {/* 统计卡片 */}
      {statistics && (
        <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="总冲突数"
                value={statistics.total_conflicts}
                prefix={<BugOutlined />}
                suffix="个"
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="已解决"
                value={statistics.resolved_conflicts}
                prefix={<CheckCircleOutlined />}
                suffix="个"
                valueStyle={{ color: '#52c41a' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="严重冲突"
                value={statistics.critical_conflicts}
                prefix={<CloseCircleOutlined />}
                suffix="个"
                valueStyle={{ color: '#f5222d' }}
              />
            </Card>
          </Col>
          <Col xs={24} sm={6}>
            <Card>
              <Statistic
                title="解决率"
                value={statistics.resolution_rate}
                prefix={<BarChartOutlined />}
                suffix="%"
                precision={1}
                valueStyle={{ color: statistics.resolution_rate > 80 ? '#52c41a' : '#faad14' }}
              />
            </Card>
          </Col>
        </Row>
      )}

      {/* 冲突列表 */}
      <Card
        title="冲突列表"
        extra={
          <Space>
            <Button
              icon={<ReloadOutlined />}
              onClick={() => {
                fetchConflictList()
                fetchStatistics()
              }}
            >
              刷新
            </Button>
            <Button
              type="primary"
              icon={<PlayCircleOutlined />}
              onClick={() => setDetectionModalVisible(true)}
              loading={detecting}
            >
              开始检测
            </Button>
          </Space>
        }
      >
        {statistics && statistics.critical_conflicts > 0 && (
          <Alert
            message="发现严重冲突"
            description={`检测到 ${statistics.critical_conflicts} 个严重冲突，建议优先处理`}
            type="error"
            showIcon
            style={{ marginBottom: '16px' }}
          />
        )}

        <Table
          columns={columns}
          dataSource={conflictList}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 冲突检测参数模态框 */}
      <Modal
        title="冲突检测设置"
        open={detectionModalVisible}
        onOk={handleStartDetection}
        onCancel={() => {
          setDetectionModalVisible(false)
          form.resetFields()
        }}
        confirmLoading={detecting}
        okText="开始检测"
        cancelText="取消"
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="detection_types"
            label="检测类型"
            initialValue={['terrain_conflict', 'geometric_conflict', 'safety_conflict']}
          >
            <Checkbox.Group>
              <Space direction="vertical">
                <Checkbox value="terrain_conflict">地形冲突</Checkbox>
                <Checkbox value="infrastructure_conflict">基础设施冲突</Checkbox>
                <Checkbox value="geometric_conflict">几何冲突</Checkbox>
                <Checkbox value="safety_conflict">安全冲突</Checkbox>
                <Checkbox value="drainage_conflict">排水冲突</Checkbox>
                <Checkbox value="environmental_conflict">环境冲突</Checkbox>
              </Space>
            </Checkbox.Group>
          </Form.Item>

          <Form.Item
            name="sensitivity_level"
            label="检测敏感度"
            initialValue="medium"
          >
            <Select>
              <Select.Option value="low">低敏感度</Select.Option>
              <Select.Option value="medium">中等敏感度</Select.Option>
              <Select.Option value="high">高敏感度</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="include_resolved"
            valuePropName="checked"
            initialValue={false}
          >
            <Checkbox>包含已解决的冲突</Checkbox>
          </Form.Item>
        </Form>
      </Modal>

      {/* 冲突详情模态框 */}
      <Modal
        title="冲突详情"
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedConflict && (
          <div>
            <Descriptions bordered size="small" style={{ marginBottom: '16px' }}>
              <Descriptions.Item label="冲突标题" span={3}>
                {selectedConflict.title}
              </Descriptions.Item>
              <Descriptions.Item label="严重程度">
                {getSeverityTag(selectedConflict.severity)}
              </Descriptions.Item>
              <Descriptions.Item label="状态">
                {getStatusTag(selectedConflict.status)}
              </Descriptions.Item>
              <Descriptions.Item label="置信度">
                {selectedConflict.confidence_score
                  ? `${Math.round(selectedConflict.confidence_score * 100)}%`
                  : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="位置坐标" span={2}>
                {selectedConflict.location[0]?.toFixed(3)}, {selectedConflict.location[1]?.toFixed(3)}
              </Descriptions.Item>
              <Descriptions.Item label="里程">
                {selectedConflict.chainage ? `${selectedConflict.chainage.toFixed(1)}m` : '-'}
              </Descriptions.Item>
              <Descriptions.Item label="检测时间" span={3}>
                {new Date(selectedConflict.detected_at).toLocaleString()}
              </Descriptions.Item>
              <Descriptions.Item label="详细描述" span={3}>
                {selectedConflict.description}
              </Descriptions.Item>
            </Descriptions>
          </div>
        )}
      </Modal>

      {/* 解决冲突模态框 */}
      <Modal
        title="解决冲突"
        open={resolveModalVisible}
        onOk={handleSubmitResolution}
        onCancel={() => {
          setResolveModalVisible(false)
          resolveForm.resetFields()
        }}
        okText="提交解决方案"
        cancelText="取消"
      >
        <Form form={resolveForm} layout="vertical">
          <Form.Item
            name="resolution_type"
            label="解决方案类型"
            rules={[{ required: true, message: '请选择解决方案类型' }]}
          >
            <Select placeholder="选择解决方案类型">
              <Select.Option value="route_adjustment">路线调整</Select.Option>
              <Select.Option value="design_modification">设计修改</Select.Option>
              <Select.Option value="engineering_measures">工程措施</Select.Option>
              <Select.Option value="ignore">忽略处理</Select.Option>
            </Select>
          </Form.Item>

          <Form.Item
            name="resolution_description"
            label="解决方案描述"
            rules={[{ required: true, message: '请输入解决方案描述' }]}
          >
            <Input.TextArea
              rows={4}
              placeholder="详细描述解决方案的具体内容和实施步骤"
            />
          </Form.Item>

          <Form.Item
            name="estimated_cost"
            label="预估成本"
          >
            <InputNumber
              style={{ width: '100%' }}
              placeholder="预估解决成本(元)"
              min={0}
            />
          </Form.Item>

          <Form.Item
            name="implementation_notes"
            label="实施备注"
          >
            <Input.TextArea
              rows={2}
              placeholder="实施过程中的注意事项和备注"
            />
          </Form.Item>
        </Form>
      </Modal>
    </div>
  )
}

export default ConflictDetection
