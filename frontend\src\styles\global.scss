// 全局SCSS变量和样式

// 颜色变量
$primary-color: #faad14;
$secondary-color: #1890ff;
$success-color: #52c41a;
$warning-color: #faad14;
$error-color: #f5222d;
$info-color: #1890ff;

// 灰度色彩
$gray-1: #ffffff;
$gray-2: #fafafa;
$gray-3: #f5f5f5;
$gray-4: #f0f0f0;
$gray-5: #d9d9d9;
$gray-6: #bfbfbf;
$gray-7: #8c8c8c;
$gray-8: #595959;
$gray-9: #434343;
$gray-10: #262626;
$gray-11: #1f1f1f;
$gray-12: #141414;
$gray-13: #000000;

// 主题色彩
$dark-bg-1: #1a1a1a;
$dark-bg-2: #2d2d2d;
$dark-bg-3: #3d3d3d;
$dark-text-1: #ffffff;
$dark-text-2: #d9d9d9;
$dark-text-3: #bfbfbf;

// 字体大小
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-base: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;

// 间距
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-base: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

// 圆角
$border-radius-sm: 4px;
$border-radius-base: 6px;
$border-radius-lg: 8px;
$border-radius-xl: 12px;

// 阴影
$box-shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px rgba(0, 0, 0, 0.02);
$box-shadow-base: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
$box-shadow-lg: 0 6px 16px -8px rgba(0, 0, 0, 0.08), 0 9px 28px 0 rgba(0, 0, 0, 0.05), 0 12px 48px 16px rgba(0, 0, 0, 0.03);

// 过渡动画
$transition-base: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-fast: all 0.2s cubic-bezier(0.645, 0.045, 0.355, 1);
$transition-slow: all 0.5s cubic-bezier(0.645, 0.045, 0.355, 1);

// 断点
$breakpoint-xs: 480px;
$breakpoint-sm: 576px;
$breakpoint-md: 768px;
$breakpoint-lg: 992px;
$breakpoint-xl: 1200px;
$breakpoint-xxl: 1600px;

// 混合器
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

@mixin button-variant($color, $background, $border) {
  color: $color;
  background-color: $background;
  border-color: $border;
  
  &:hover {
    color: $color;
    background-color: lighten($background, 5%);
    border-color: lighten($border, 5%);
  }
  
  &:active {
    color: $color;
    background-color: darken($background, 5%);
    border-color: darken($border, 5%);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// 响应式混合器
@mixin respond-to($breakpoint) {
  @if $breakpoint == xs {
    @media (max-width: #{$breakpoint-xs - 1px}) {
      @content;
    }
  }
  @if $breakpoint == sm {
    @media (min-width: #{$breakpoint-sm}) {
      @content;
    }
  }
  @if $breakpoint == md {
    @media (min-width: #{$breakpoint-md}) {
      @content;
    }
  }
  @if $breakpoint == lg {
    @media (min-width: #{$breakpoint-lg}) {
      @content;
    }
  }
  @if $breakpoint == xl {
    @media (min-width: #{$breakpoint-xl}) {
      @content;
    }
  }
  @if $breakpoint == xxl {
    @media (min-width: #{$breakpoint-xxl}) {
      @content;
    }
  }
}

// 全局样式类
.mining-theme {
  // 主题色彩类
  .primary-color { color: $primary-color; }
  .success-color { color: $success-color; }
  .warning-color { color: $warning-color; }
  .error-color { color: $error-color; }
  .info-color { color: $info-color; }
  
  // 背景色类
  .primary-bg { background-color: $primary-color; }
  .dark-bg-1 { background-color: $dark-bg-1; }
  .dark-bg-2 { background-color: $dark-bg-2; }
  .dark-bg-3 { background-color: $dark-bg-3; }
  
  // 文本色类
  .dark-text-1 { color: $dark-text-1; }
  .dark-text-2 { color: $dark-text-2; }
  .dark-text-3 { color: $dark-text-3; }
  
  // 间距类
  .m-xs { margin: $spacing-xs; }
  .m-sm { margin: $spacing-sm; }
  .m-base { margin: $spacing-base; }
  .m-lg { margin: $spacing-lg; }
  .m-xl { margin: $spacing-xl; }
  
  .p-xs { padding: $spacing-xs; }
  .p-sm { padding: $spacing-sm; }
  .p-base { padding: $spacing-base; }
  .p-lg { padding: $spacing-lg; }
  .p-xl { padding: $spacing-xl; }
  
  // 圆角类
  .rounded-sm { border-radius: $border-radius-sm; }
  .rounded-base { border-radius: $border-radius-base; }
  .rounded-lg { border-radius: $border-radius-lg; }
  .rounded-xl { border-radius: $border-radius-xl; }
  
  // 阴影类
  .shadow-sm { box-shadow: $box-shadow-sm; }
  .shadow-base { box-shadow: $box-shadow-base; }
  .shadow-lg { box-shadow: $box-shadow-lg; }
  
  // 过渡类
  .transition-base { transition: $transition-base; }
  .transition-fast { transition: $transition-fast; }
  .transition-slow { transition: $transition-slow; }
}

// 专业矿山设计界面样式
.mining-interface {
  background: linear-gradient(135deg, $dark-bg-1 0%, $dark-bg-2 100%);
  color: $dark-text-1;
  min-height: 100vh;
  
  .header {
    background: $dark-bg-1;
    border-bottom: 1px solid $dark-bg-3;
    box-shadow: $box-shadow-base;
  }
  
  .sidebar {
    background: $dark-bg-2;
    border-right: 1px solid $dark-bg-3;
  }
  
  .content {
    background: $gray-3;
    padding: $spacing-lg;
  }
  
  .card {
    background: $gray-1;
    border-radius: $border-radius-lg;
    box-shadow: $box-shadow-base;
    padding: $spacing-lg;
    margin-bottom: $spacing-lg;
  }
  
  .toolbar {
    background: $gray-2;
    border: 1px solid $gray-4;
    border-radius: $border-radius-base;
    padding: $spacing-sm $spacing-base;
    margin-bottom: $spacing-base;
    @include flex-between;
  }
  
  .status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    display: inline-block;
    margin-right: $spacing-xs;
    
    &.success { background-color: $success-color; }
    &.warning { background-color: $warning-color; }
    &.error { background-color: $error-color; }
    &.info { background-color: $info-color; }
  }
}
