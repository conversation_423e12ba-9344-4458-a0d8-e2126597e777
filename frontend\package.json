{"name": "openpit-road-design-frontend", "private": true, "version": "1.0.0", "description": "露天矿山道路设计软件前端应用", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "antd": "^5.12.8", "@ant-design/icons": "^5.2.6", "cesium": "^1.111.0", "resium": "^1.17.3", "three": "^0.158.0", "@react-three/fiber": "^8.15.11", "@react-three/drei": "^9.88.13", "axios": "^1.6.2", "react-query": "^3.39.3", "zustand": "^4.4.7", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "classnames": "^2.3.2", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "yup": "^1.4.0", "react-beautiful-dnd": "^13.1.1", "react-virtualized": "^9.22.5", "react-window": "^1.8.8", "react-hotkeys-hook": "^4.4.1", "react-use": "^17.4.2", "ahooks": "^3.7.8", "react-error-boundary": "^4.0.11", "react-helmet-async": "^2.0.4", "i18next": "^23.7.6", "react-i18next": "^13.5.0", "recharts": "^2.8.0", "react-flow-renderer": "^10.3.17", "leaflet": "^1.9.4", "react-leaflet": "^4.2.1", "proj4": "^2.9.2", "turf": "^3.0.14", "@turf/turf": "^6.5.0", "file-saver": "^2.0.5", "jszip": "^3.10.1", "xlsx": "^0.18.5", "pdf-lib": "^1.17.1", "canvas": "^2.11.2", "fabric": "^5.3.0", "konva": "^9.2.0", "react-konva": "^18.2.10"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/node": "^20.9.0", "@types/lodash-es": "^4.17.12", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-virtualized": "^9.21.29", "@types/react-window": "^1.8.8", "@types/leaflet": "^1.9.8", "@types/file-saver": "^2.0.7", "@types/three": "^0.158.3", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "typescript": "^5.2.2", "vite": "^5.0.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "vitest": "^0.34.6", "@vitest/ui": "^0.34.6", "jsdom": "^22.1.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "tailwindcss": "^3.3.6", "sass": "^1.69.5", "less": "^4.2.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}