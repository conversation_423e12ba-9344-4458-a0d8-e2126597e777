"""
系统监控API端点
"""
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
import psutil
import time

from app.core.database import get_db, check_db_health, check_redis_health, get_connection_pool_status

router = APIRouter()


@router.get("/system")
async def get_system_status():
    """获取系统状态"""
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    return {
        "cpu_usage": cpu_percent,
        "memory": {
            "total": memory.total,
            "available": memory.available,
            "percent": memory.percent,
            "used": memory.used
        },
        "disk": {
            "total": disk.total,
            "used": disk.used,
            "free": disk.free,
            "percent": (disk.used / disk.total) * 100
        },
        "timestamp": time.time()
    }


@router.get("/database")
async def get_database_status(db: Session = Depends(get_db)):
    """获取数据库状态"""
    db_health = check_db_health()
    pool_status = get_connection_pool_status()
    
    return {
        "status": "healthy" if db_health else "unhealthy",
        "connection_pool": pool_status,
        "timestamp": time.time()
    }


@router.get("/redis")
async def get_redis_status():
    """获取Redis状态"""
    redis_health = check_redis_health()
    
    return {
        "status": "healthy" if redis_health else "unhealthy",
        "timestamp": time.time()
    }


@router.get("/performance")
async def get_performance_metrics():
    """获取性能指标"""
    # TODO: 实现详细的性能监控
    return {
        "response_time": "TODO",
        "throughput": "TODO",
        "error_rate": "TODO",
        "timestamp": time.time()
    }
