"""
FastAPI主应用程序
"""
from fastapi import FastAPI, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
import time
import logging
from contextlib import asynccontextmanager

from app.core.config import settings
from app.core.database import init_db, check_db_health, check_redis_health
from app.api.api_v1.api import api_router


# 配置日志
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler(settings.LOG_FILE),
        logging.StreamHandler()
    ]
)

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时执行
    logger.info("正在启动露天矿山道路设计软件...")
    
    # 初始化数据库
    try:
        init_db()
        logger.info("数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {e}")
        raise
    
    # 检查服务健康状态
    if not check_db_health():
        logger.error("数据库连接失败")
        raise Exception("数据库连接失败")
    
    if not check_redis_health():
        logger.warning("Redis连接失败，某些功能可能受限")
    
    logger.info("应用启动完成")
    
    yield
    
    # 关闭时执行
    logger.info("正在关闭应用...")


# 创建FastAPI应用实例
app = FastAPI(
    title=settings.PROJECT_NAME,
    version=settings.VERSION,
    description="专业的露天矿山采矿工程道路设计软件",
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    docs_url=None,  # 禁用默认文档
    redoc_url=None,  # 禁用ReDoc
    lifespan=lifespan,
)


# 添加CORS中间件
if settings.BACKEND_CORS_ORIGINS:
    app.add_middleware(
        CORSMiddleware,
        allow_origins=[str(origin) for origin in settings.BACKEND_CORS_ORIGINS],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )


# 添加受信任主机中间件
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=["localhost", "127.0.0.1", settings.SERVER_NAME]
)


# 请求处理时间中间件
@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """添加请求处理时间头"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


# 全局异常处理器
@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """全局异常处理"""
    logger.error(f"全局异常: {exc}", exc_info=True)
    return JSONResponse(
        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
        content={
            "message": "服务器内部错误",
            "detail": str(exc) if settings.DEBUG else "请联系系统管理员"
        }
    )


# 404错误处理器
@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """404错误处理"""
    return JSONResponse(
        status_code=status.HTTP_404_NOT_FOUND,
        content={"message": "请求的资源不存在"}
    )


# 健康检查端点
@app.get("/health")
async def health_check():
    """健康检查"""
    db_status = check_db_health()
    redis_status = check_redis_health()
    
    return {
        "status": "healthy" if db_status else "unhealthy",
        "database": "connected" if db_status else "disconnected",
        "redis": "connected" if redis_status else "disconnected",
        "version": settings.VERSION,
        "timestamp": time.time()
    }


# 根路径
@app.get("/")
async def root():
    """根路径"""
    return {
        "message": "欢迎使用露天矿山道路设计软件",
        "version": settings.VERSION,
        "docs": "/docs",
        "api": settings.API_V1_STR
    }


# 自定义API文档
@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    """自定义Swagger UI"""
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=f"{app.title} - API文档",
        swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
        swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css",
    )


# 自定义OpenAPI模式
def custom_openapi():
    """自定义OpenAPI模式"""
    if app.openapi_schema:
        return app.openapi_schema
    
    openapi_schema = get_openapi(
        title=settings.PROJECT_NAME,
        version=settings.VERSION,
        description="""
        ## 露天矿山道路设计软件API
        
        这是一款专业的露天矿山采矿工程道路设计软件的后端API。
        
        ### 主要功能模块：
        
        - **用户管理** - 用户注册、登录、权限管理
        - **项目管理** - 项目创建、编辑、分享
        - **地形数据** - 三维地形地质数据导入和处理
        - **道路设计** - 智能道路选线和设计
        - **冲突检测** - 道路与地形、设施的冲突检测
        - **安全分析** - 道路安全性评估和分析
        - **路线优化** - 运输路线多目标优化
        - **数据导出** - AutoCAD文件导出
        
        ### 技术特性：
        
        - RESTful API设计
        - JWT身份认证
        - 异步处理支持
        - 实时数据同步
        - 高性能计算
        """,
        routes=app.routes,
    )
    
    # 添加安全定义
    openapi_schema["components"]["securitySchemes"] = {
        "Bearer": {
            "type": "http",
            "scheme": "bearer",
            "bearerFormat": "JWT",
        }
    }
    
    app.openapi_schema = openapi_schema
    return app.openapi_schema


app.openapi = custom_openapi


# 挂载静态文件
app.mount("/static", StaticFiles(directory="static"), name="static")


# 包含API路由
app.include_router(api_router, prefix=settings.API_V1_STR)


# 启动信息
@app.on_event("startup")
async def startup_event():
    """启动事件"""
    logger.info(f"服务器启动在: {settings.SERVER_HOST}")
    logger.info(f"API文档地址: {settings.SERVER_HOST}/docs")
    logger.info(f"API根路径: {settings.SERVER_HOST}{settings.API_V1_STR}")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
