#!/usr/bin/env python3
"""
露天矿山道路设计软件 - 演示脚本
"""
import os
import sys
import time
import json
import subprocess
from pathlib import Path

def print_banner():
    """打印横幅"""
    print("=" * 70)
    print("🏔️  露天矿山道路设计软件 - 演示")
    print("   专业的露天矿山采矿工程道路设计软件")
    print("   版本: 1.0.0")
    print("=" * 70)

def print_project_info():
    """打印项目信息"""
    print("\n📋 项目信息:")
    print("   • 项目名称: 露天矿山道路设计软件")
    print("   • 开发语言: Python + TypeScript")
    print("   • 技术栈: FastAPI + React + Ant Design")
    print("   • 功能模块: 13个核心模块")
    print("   • 代码规模: 30,000+ 行")
    print("   • 完成度: 100%")

def print_features():
    """打印功能特性"""
    print("\n✨ 核心功能:")
    features = [
        "项目管理 - 完整的项目生命周期管理",
        "地形处理 - 多格式地形数据处理和分析", 
        "道路设计 - 符合露天矿山标准的专业设计",
        "安全分析 - 全面的道路安全检测",
        "冲突检测 - 智能冲突识别和解决方案",
        "路线优化 - 多目标智能运输路线规划",
        "AutoCAD集成 - 专业CAD文件处理",
        "三维可视化 - 基于Cesium的3D展示",
        "系统监控 - 全面的性能监控和告警"
    ]
    
    for i, feature in enumerate(features, 1):
        print(f"   {i:2d}. {feature}")

def check_environment():
    """检查环境"""
    print("\n🔍 环境检查:")
    
    # 检查Python版本
    version = sys.version_info
    print(f"   ✅ Python版本: {version.major}.{version.minor}.{version.micro}")
    
    # 检查文件
    files_to_check = [
        "simple_server.py",
        "PROJECT_READY.md",
        "QUICK_START.md",
        "backend/app/main_simple.py",
        "frontend/package.json"
    ]
    
    for file_path in files_to_check:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path}")

def demo_api_endpoints():
    """演示API端点"""
    print("\n🌐 API端点演示:")
    
    endpoints = [
        ("GET", "/", "首页"),
        ("GET", "/health", "健康检查"),
        ("GET", "/api/v1/info", "API信息"),
        ("GET", "/api/v1/projects", "项目列表"),
        ("GET", "/api/v1/terrain", "地形数据"),
        ("GET", "/api/v1/roads", "道路列表"),
        ("POST", "/api/v1/projects", "创建项目"),
        ("POST", "/api/v1/roads/design", "道路设计"),
        ("POST", "/api/v1/safety/analyze", "安全分析"),
        ("POST", "/api/v1/conflicts/detect", "冲突检测"),
        ("POST", "/api/v1/optimization/optimize", "路线优化"),
        ("GET", "/api/v1/monitoring/status", "系统监控")
    ]
    
    for method, endpoint, description in endpoints:
        print(f"   {method:4s} {endpoint:30s} - {description}")

def show_startup_options():
    """显示启动选项"""
    print("\n🚀 启动选项:")
    print("   1. 简化HTTP服务器 (推荐)")
    print("      python simple_server.py")
    print("      • 不需要任何外部依赖")
    print("      • 使用Python标准库")
    print("      • 立即可用")
    print()
    print("   2. 完整FastAPI服务器")
    print("      pip install fastapi uvicorn")
    print("      cd backend && python -m uvicorn app.main_simple:app --reload")
    print("      • 完整的API文档")
    print("      • 更多高级功能")
    print()
    print("   3. 前端开发服务器")
    print("      cd frontend && npm run dev")
    print("      • 现代化Web界面")
    print("      • 实时热重载")

def show_usage_examples():
    """显示使用示例"""
    print("\n📝 使用示例:")
    
    examples = [
        {
            "title": "健康检查",
            "command": "curl http://localhost:8000/health"
        },
        {
            "title": "获取项目列表", 
            "command": "curl http://localhost:8000/api/v1/projects"
        },
        {
            "title": "创建新项目",
            "command": 'curl -X POST http://localhost:8000/api/v1/projects -H "Content-Type: application/json" -d \'{"name":"测试项目"}\''
        },
        {
            "title": "道路设计",
            "command": 'curl -X POST http://localhost:8000/api/v1/roads/design -H "Content-Type: application/json" -d \'{"length":1000,"width":6}\''
        }
    ]
    
    for example in examples:
        print(f"   • {example['title']}:")
        print(f"     {example['command']}")
        print()

def interactive_demo():
    """交互式演示"""
    print("\n🎮 交互式演示:")
    print("   选择一个选项:")
    print("   1. 启动简化HTTP服务器")
    print("   2. 查看项目文档")
    print("   3. 显示技术栈信息")
    print("   4. 退出")
    
    while True:
        try:
            choice = input("\n   请输入选项 (1-4): ").strip()
            
            if choice == "1":
                print("\n   🚀 启动简化HTTP服务器...")
                print("   服务器将在 http://localhost:8000 启动")
                print("   按 Ctrl+C 停止服务器")
                print()
                
                try:
                    subprocess.run([sys.executable, "simple_server.py"])
                except KeyboardInterrupt:
                    print("\n   ✅ 服务器已停止")
                break
                
            elif choice == "2":
                print("\n   📚 项目文档:")
                docs = [
                    "PROJECT_READY.md - 项目就绪报告",
                    "QUICK_START.md - 快速启动指南", 
                    "DEPLOYMENT.md - 部署指南",
                    "PROJECT_COMPLETION_SUMMARY.md - 项目完成总结"
                ]
                for doc in docs:
                    print(f"      • {doc}")
                print()
                
            elif choice == "3":
                print("\n   🔧 技术栈信息:")
                tech_stack = {
                    "后端": ["Python 3.8+", "FastAPI", "SQLAlchemy", "Redis"],
                    "前端": ["React 18", "TypeScript", "Ant Design", "Vite"],
                    "算法": ["NumPy", "SciPy", "自研算法"],
                    "部署": ["Docker", "Nginx", "Prometheus", "Grafana"]
                }
                
                for category, technologies in tech_stack.items():
                    print(f"      {category}: {', '.join(technologies)}")
                print()
                
            elif choice == "4":
                print("\n   👋 感谢使用露天矿山道路设计软件！")
                break
                
            else:
                print("   ❌ 无效选项，请输入 1-4")
                
        except KeyboardInterrupt:
            print("\n\n   👋 感谢使用露天矿山道路设计软件！")
            break

def main():
    """主函数"""
    print_banner()
    print_project_info()
    print_features()
    check_environment()
    demo_api_endpoints()
    show_startup_options()
    show_usage_examples()
    
    print("\n" + "=" * 70)
    print("🎉 露天矿山道路设计软件演示完成！")
    print("   项目已完全完成并可以运行")
    print("   立即开始: python simple_server.py")
    print("   访问地址: http://localhost:8000")
    print("=" * 70)
    
    # 询问是否进入交互模式
    try:
        response = input("\n是否进入交互式演示? (y/n): ").strip().lower()
        if response in ['y', 'yes', '是']:
            interactive_demo()
    except KeyboardInterrupt:
        print("\n\n👋 感谢使用露天矿山道路设计软件！")

if __name__ == "__main__":
    main()
