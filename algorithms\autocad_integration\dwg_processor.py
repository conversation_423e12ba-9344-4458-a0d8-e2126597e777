"""
DWG文件处理器
"""
import os
import json
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)

try:
    import ezdxf
    from ezdxf import recover
    EZDXF_AVAILABLE = True
except ImportError:
    EZDXF_AVAILABLE = False
    logger.warning("ezdxf库未安装，DWG/DXF功能将受限")


@dataclass
class CADEntity:
    """CAD实体"""
    entity_type: str
    layer: str
    coordinates: List[Tuple[float, float, float]]
    properties: Dict[str, Any]
    entity_id: Optional[str] = None


@dataclass
class CADLayer:
    """CAD图层"""
    name: str
    color: int
    linetype: str
    lineweight: float
    visible: bool = True
    locked: bool = False


@dataclass
class CADDrawing:
    """CAD图纸"""
    filename: str
    version: str
    units: str
    bounds: Tuple[float, float, float, float]  # min_x, min_y, max_x, max_y
    layers: List[CADLayer]
    entities: List[CADEntity]
    metadata: Dict[str, Any]


class DWGProcessor:
    """DWG文件处理器"""
    
    def __init__(self):
        self.supported_entities = {
            'LINE', 'POLYLINE', 'LWPOLYLINE', 'CIRCLE', 'ARC', 
            'POINT', 'TEXT', 'MTEXT', 'INSERT', 'SPLINE'
        }
        
        if not EZDXF_AVAILABLE:
            logger.error("ezdxf库未安装，请安装: pip install ezdxf")
    
    def read_dwg_file(self, file_path: str) -> Optional[CADDrawing]:
        """读取DWG文件"""
        try:
            if not EZDXF_AVAILABLE:
                raise ImportError("ezdxf库未安装")
            
            logger.info(f"读取DWG文件: {file_path}")
            
            # 使用ezdxf的recover功能处理可能损坏的文件
            doc, auditor = recover.readfile(file_path)
            
            if auditor.has_errors:
                logger.warning(f"DWG文件存在错误: {len(auditor.errors)}个错误")
                for error in auditor.errors:
                    logger.warning(f"错误: {error}")
            
            # 提取图纸信息
            drawing = self._extract_drawing_info(doc, file_path)
            
            logger.info(f"DWG文件读取完成: {len(drawing.entities)}个实体, {len(drawing.layers)}个图层")
            return drawing
            
        except Exception as e:
            logger.error(f"读取DWG文件失败: {str(e)}")
            return None
    
    def _extract_drawing_info(self, doc, file_path: str) -> CADDrawing:
        """提取图纸信息"""
        try:
            # 获取基本信息
            filename = os.path.basename(file_path)
            version = doc.dxfversion
            units = self._get_drawing_units(doc)
            
            # 提取图层
            layers = self._extract_layers(doc)
            
            # 提取实体
            entities = self._extract_entities(doc)
            
            # 计算边界
            bounds = self._calculate_bounds(entities)
            
            # 提取元数据
            metadata = self._extract_metadata(doc)
            
            return CADDrawing(
                filename=filename,
                version=version,
                units=units,
                bounds=bounds,
                layers=layers,
                entities=entities,
                metadata=metadata
            )
            
        except Exception as e:
            logger.error(f"提取图纸信息失败: {str(e)}")
            raise
    
    def _get_drawing_units(self, doc) -> str:
        """获取图纸单位"""
        try:
            # 从头部变量获取单位
            header = doc.header
            units_code = header.get('$INSUNITS', 0)
            
            units_map = {
                0: 'Unitless',
                1: 'Inches',
                2: 'Feet',
                3: 'Miles',
                4: 'Millimeters',
                5: 'Centimeters',
                6: 'Meters',
                7: 'Kilometers',
                8: 'Microinches',
                9: 'Mils',
                10: 'Yards',
                11: 'Angstroms',
                12: 'Nanometers',
                13: 'Microns',
                14: 'Decimeters',
                15: 'Decameters',
                16: 'Hectometers',
                17: 'Gigameters',
                18: 'Astronomical Units',
                19: 'Light Years',
                20: 'Parsecs'
            }
            
            return units_map.get(units_code, 'Unknown')
            
        except Exception as e:
            logger.warning(f"获取图纸单位失败: {str(e)}")
            return 'Unknown'
    
    def _extract_layers(self, doc) -> List[CADLayer]:
        """提取图层"""
        try:
            layers = []
            
            for layer in doc.layers:
                cad_layer = CADLayer(
                    name=layer.dxf.name,
                    color=layer.dxf.color,
                    linetype=layer.dxf.linetype,
                    lineweight=getattr(layer.dxf, 'lineweight', 0.25),
                    visible=not layer.is_off(),
                    locked=layer.is_locked()
                )
                layers.append(cad_layer)
            
            return layers
            
        except Exception as e:
            logger.error(f"提取图层失败: {str(e)}")
            return []
    
    def _extract_entities(self, doc) -> List[CADEntity]:
        """提取实体"""
        try:
            entities = []
            
            # 遍历模型空间中的所有实体
            msp = doc.modelspace()
            
            for entity in msp:
                if entity.dxftype() in self.supported_entities:
                    cad_entity = self._process_entity(entity)
                    if cad_entity:
                        entities.append(cad_entity)
            
            return entities
            
        except Exception as e:
            logger.error(f"提取实体失败: {str(e)}")
            return []
    
    def _process_entity(self, entity) -> Optional[CADEntity]:
        """处理单个实体"""
        try:
            entity_type = entity.dxftype()
            layer = entity.dxf.layer
            
            # 提取坐标
            coordinates = self._extract_coordinates(entity)
            
            # 提取属性
            properties = self._extract_entity_properties(entity)
            
            return CADEntity(
                entity_type=entity_type,
                layer=layer,
                coordinates=coordinates,
                properties=properties,
                entity_id=str(entity.dxf.handle) if hasattr(entity.dxf, 'handle') else None
            )
            
        except Exception as e:
            logger.warning(f"处理实体失败: {str(e)}")
            return None
    
    def _extract_coordinates(self, entity) -> List[Tuple[float, float, float]]:
        """提取实体坐标"""
        try:
            entity_type = entity.dxftype()
            coordinates = []
            
            if entity_type == 'LINE':
                start = entity.dxf.start
                end = entity.dxf.end
                coordinates = [
                    (start.x, start.y, start.z),
                    (end.x, end.y, end.z)
                ]
                
            elif entity_type == 'POINT':
                location = entity.dxf.location
                coordinates = [(location.x, location.y, location.z)]
                
            elif entity_type in ['POLYLINE', 'LWPOLYLINE']:
                for vertex in entity.vertices:
                    if hasattr(vertex, 'dxf'):
                        loc = vertex.dxf.location
                        coordinates.append((loc.x, loc.y, getattr(loc, 'z', 0)))
                    else:
                        # LWPOLYLINE的顶点
                        coordinates.append((vertex[0], vertex[1], 0))
                        
            elif entity_type == 'CIRCLE':
                center = entity.dxf.center
                radius = entity.dxf.radius
                # 圆心坐标
                coordinates = [(center.x, center.y, center.z)]
                
            elif entity_type == 'ARC':
                center = entity.dxf.center
                coordinates = [(center.x, center.y, center.z)]
                
            elif entity_type in ['TEXT', 'MTEXT']:
                if hasattr(entity.dxf, 'insert'):
                    insert = entity.dxf.insert
                    coordinates = [(insert.x, insert.y, insert.z)]
                    
            elif entity_type == 'INSERT':
                insert = entity.dxf.insert
                coordinates = [(insert.x, insert.y, insert.z)]
                
            elif entity_type == 'SPLINE':
                # 样条曲线的控制点
                if hasattr(entity, 'control_points'):
                    for point in entity.control_points:
                        coordinates.append((point.x, point.y, getattr(point, 'z', 0)))
            
            return coordinates
            
        except Exception as e:
            logger.warning(f"提取坐标失败: {str(e)}")
            return []
    
    def _extract_entity_properties(self, entity) -> Dict[str, Any]:
        """提取实体属性"""
        try:
            properties = {}
            entity_type = entity.dxftype()
            
            # 通用属性
            properties['color'] = getattr(entity.dxf, 'color', 256)
            properties['linetype'] = getattr(entity.dxf, 'linetype', 'BYLAYER')
            properties['lineweight'] = getattr(entity.dxf, 'lineweight', -1)
            
            # 特定实体属性
            if entity_type == 'CIRCLE':
                properties['radius'] = entity.dxf.radius
                
            elif entity_type == 'ARC':
                properties['radius'] = entity.dxf.radius
                properties['start_angle'] = entity.dxf.start_angle
                properties['end_angle'] = entity.dxf.end_angle
                
            elif entity_type in ['TEXT', 'MTEXT']:
                properties['text'] = getattr(entity.dxf, 'text', '')
                properties['height'] = getattr(entity.dxf, 'height', 1.0)
                properties['rotation'] = getattr(entity.dxf, 'rotation', 0.0)
                
            elif entity_type == 'INSERT':
                properties['name'] = getattr(entity.dxf, 'name', '')
                properties['xscale'] = getattr(entity.dxf, 'xscale', 1.0)
                properties['yscale'] = getattr(entity.dxf, 'yscale', 1.0)
                properties['zscale'] = getattr(entity.dxf, 'zscale', 1.0)
                properties['rotation'] = getattr(entity.dxf, 'rotation', 0.0)
            
            return properties
            
        except Exception as e:
            logger.warning(f"提取实体属性失败: {str(e)}")
            return {}
    
    def _calculate_bounds(self, entities: List[CADEntity]) -> Tuple[float, float, float, float]:
        """计算图纸边界"""
        try:
            if not entities:
                return (0, 0, 0, 0)
            
            min_x = min_y = float('inf')
            max_x = max_y = float('-inf')
            
            for entity in entities:
                for coord in entity.coordinates:
                    x, y, z = coord
                    min_x = min(min_x, x)
                    min_y = min(min_y, y)
                    max_x = max(max_x, x)
                    max_y = max(max_y, y)
            
            return (min_x, min_y, max_x, max_y)
            
        except Exception as e:
            logger.warning(f"计算边界失败: {str(e)}")
            return (0, 0, 0, 0)
    
    def _extract_metadata(self, doc) -> Dict[str, Any]:
        """提取元数据"""
        try:
            metadata = {}
            
            # 从头部变量提取信息
            header = doc.header
            
            metadata['acadver'] = header.get('$ACADVER', 'Unknown')
            metadata['dwgcodepage'] = header.get('$DWGCODEPAGE', 'Unknown')
            metadata['lastsavedby'] = header.get('$LASTSAVEDBY', 'Unknown')
            metadata['tdcreate'] = str(header.get('$TDCREATE', ''))
            metadata['tdupdate'] = str(header.get('$TDUPDATE', ''))
            
            # 图纸大小信息
            metadata['limmin'] = str(header.get('$LIMMIN', ''))
            metadata['limmax'] = str(header.get('$LIMMAX', ''))
            metadata['extmin'] = str(header.get('$EXTMIN', ''))
            metadata['extmax'] = str(header.get('$EXTMAX', ''))
            
            return metadata
            
        except Exception as e:
            logger.warning(f"提取元数据失败: {str(e)}")
            return {}
    
    def convert_to_road_data(self, drawing: CADDrawing) -> Dict[str, Any]:
        """将CAD图纸转换为道路数据"""
        try:
            logger.info("开始转换CAD图纸为道路数据")
            
            road_data = {
                'roads': [],
                'points': [],
                'segments': [],
                'annotations': [],
                'metadata': drawing.metadata
            }
            
            # 按图层分类实体
            layer_entities = {}
            for entity in drawing.entities:
                layer = entity.layer
                if layer not in layer_entities:
                    layer_entities[layer] = []
                layer_entities[layer].append(entity)
            
            # 识别道路中心线（通常在特定图层）
            centerline_layers = self._identify_centerline_layers(layer_entities)
            
            for layer_name in centerline_layers:
                entities = layer_entities[layer_name]
                roads = self._extract_roads_from_entities(entities, layer_name)
                road_data['roads'].extend(roads)
            
            # 提取点位信息
            point_entities = [e for e in drawing.entities if e.entity_type == 'POINT']
            points = self._extract_points_from_entities(point_entities)
            road_data['points'].extend(points)
            
            # 提取注释信息
            text_entities = [e for e in drawing.entities if e.entity_type in ['TEXT', 'MTEXT']]
            annotations = self._extract_annotations_from_entities(text_entities)
            road_data['annotations'].extend(annotations)
            
            logger.info(f"CAD转换完成: {len(road_data['roads'])}条道路, {len(road_data['points'])}个点位")
            return road_data
            
        except Exception as e:
            logger.error(f"CAD转换失败: {str(e)}")
            return {}
    
    def _identify_centerline_layers(self, layer_entities: Dict[str, List[CADEntity]]) -> List[str]:
        """识别道路中心线图层"""
        try:
            centerline_keywords = [
                'centerline', 'center', 'road', 'highway', 'path', 'route',
                '中心线', '道路', '路径', '中线', 'cl', 'rd'
            ]
            
            centerline_layers = []
            
            for layer_name in layer_entities.keys():
                layer_lower = layer_name.lower()
                
                # 检查图层名是否包含关键词
                for keyword in centerline_keywords:
                    if keyword in layer_lower:
                        centerline_layers.append(layer_name)
                        break
                
                # 检查图层中是否主要包含线性实体
                entities = layer_entities[layer_name]
                line_entities = [e for e in entities if e.entity_type in ['LINE', 'POLYLINE', 'LWPOLYLINE']]
                
                if len(line_entities) > len(entities) * 0.8:  # 80%以上是线性实体
                    if layer_name not in centerline_layers:
                        centerline_layers.append(layer_name)
            
            return centerline_layers
            
        except Exception as e:
            logger.warning(f"识别中心线图层失败: {str(e)}")
            return []
    
    def _extract_roads_from_entities(self, entities: List[CADEntity], layer_name: str) -> List[Dict]:
        """从实体中提取道路"""
        try:
            roads = []
            
            # 合并连续的线段
            line_entities = [e for e in entities if e.entity_type in ['LINE', 'POLYLINE', 'LWPOLYLINE']]
            
            for i, entity in enumerate(line_entities):
                road = {
                    'road_id': f"{layer_name}_{i}",
                    'name': f"Road_{layer_name}_{i}",
                    'layer': layer_name,
                    'entity_type': entity.entity_type,
                    'coordinates': entity.coordinates,
                    'properties': entity.properties
                }
                
                # 计算道路长度
                road['length'] = self._calculate_polyline_length(entity.coordinates)
                
                roads.append(road)
            
            return roads
            
        except Exception as e:
            logger.warning(f"提取道路失败: {str(e)}")
            return []
    
    def _extract_points_from_entities(self, entities: List[CADEntity]) -> List[Dict]:
        """从实体中提取点位"""
        try:
            points = []
            
            for i, entity in enumerate(entities):
                if entity.coordinates:
                    coord = entity.coordinates[0]
                    point = {
                        'point_id': f"point_{i}",
                        'x': coord[0],
                        'y': coord[1],
                        'z': coord[2],
                        'layer': entity.layer,
                        'properties': entity.properties
                    }
                    points.append(point)
            
            return points
            
        except Exception as e:
            logger.warning(f"提取点位失败: {str(e)}")
            return []
    
    def _extract_annotations_from_entities(self, entities: List[CADEntity]) -> List[Dict]:
        """从实体中提取注释"""
        try:
            annotations = []
            
            for i, entity in enumerate(entities):
                if entity.coordinates:
                    coord = entity.coordinates[0]
                    annotation = {
                        'annotation_id': f"text_{i}",
                        'text': entity.properties.get('text', ''),
                        'x': coord[0],
                        'y': coord[1],
                        'z': coord[2],
                        'layer': entity.layer,
                        'height': entity.properties.get('height', 1.0),
                        'rotation': entity.properties.get('rotation', 0.0)
                    }
                    annotations.append(annotation)
            
            return annotations
            
        except Exception as e:
            logger.warning(f"提取注释失败: {str(e)}")
            return []
    
    def _calculate_polyline_length(self, coordinates: List[Tuple[float, float, float]]) -> float:
        """计算折线长度"""
        try:
            if len(coordinates) < 2:
                return 0.0
            
            total_length = 0.0
            
            for i in range(len(coordinates) - 1):
                p1 = coordinates[i]
                p2 = coordinates[i + 1]
                
                dx = p2[0] - p1[0]
                dy = p2[1] - p1[1]
                dz = p2[2] - p1[2]
                
                length = (dx**2 + dy**2 + dz**2)**0.5
                total_length += length
            
            return total_length
            
        except Exception as e:
            logger.warning(f"计算折线长度失败: {str(e)}")
            return 0.0
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的文件格式"""
        if EZDXF_AVAILABLE:
            return ['dwg', 'dxf']
        else:
            return []
    
    def validate_file(self, file_path: str) -> Dict[str, Any]:
        """验证文件"""
        try:
            if not os.path.exists(file_path):
                return {'valid': False, 'error': '文件不存在'}
            
            if not EZDXF_AVAILABLE:
                return {'valid': False, 'error': 'ezdxf库未安装'}
            
            # 尝试读取文件头
            try:
                doc, auditor = recover.readfile(file_path)
                
                return {
                    'valid': True,
                    'version': doc.dxfversion,
                    'errors': len(auditor.errors),
                    'warnings': len(auditor.warnings),
                    'file_size': os.path.getsize(file_path)
                }
                
            except Exception as e:
                return {'valid': False, 'error': f'文件格式错误: {str(e)}'}
                
        except Exception as e:
            logger.error(f"文件验证失败: {str(e)}")
            return {'valid': False, 'error': str(e)}
