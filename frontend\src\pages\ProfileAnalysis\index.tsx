import React, { useState, useEffect } from 'react'
import { 
  <PERSON>, Button, Table, Tag, Space, Modal, Form, Select, 
  InputNumber, message, Row, Col, Statistic, Tabs, 
  Descriptions, Tooltip, Alert, List, Progress, Checkbox
} from 'antd'
import { 
  PlayCircleOutlined, <PERSON>Outlined, <PERSON><PERSON><PERSON>Outlined,
  <PERSON><PERSON><PERSON><PERSON>utlined, EnvironmentOutlined, <PERSON>Outlined,
  FileTextOutlined, <PERSON>loadOutlined, <PERSON><PERSON>Outlined,
  AreaChartOutlined, <PERSON><PERSON>hartOutlined
} from '@ant-design/icons'
import { useParams } from 'react-router-dom'
import axios from 'axios'

const { TabPane } = Tabs

interface ProfileAnalysisData {
  id: number
  road_id: number
  analysis_type: string
  total_length: number
  max_gradient?: number
  min_gradient?: number
  average_gradient?: number
  total_cut_volume?: number
  total_fill_volume?: number
  net_volume?: number
  balance_ratio?: number
  cross_sections_count: number
  analyzed_at: string
}

interface LongitudinalData {
  road_id: number
  analysis: {
    total_length: number
    max_gradient: number
    min_gradient: number
    average_gradient: number
    total_rise: number
    total_fall: number
    vertical_curves_count: number
  }
  grade_points: Array<{
    chainage: number
    elevation: number
    gradient: number
  }>
  vertical_curves: Array<{
    start_chainage: number
    end_chainage: number
    length: number
    radius: number
    grade_in: number
    grade_out: number
    curve_type: string
  }>
}

interface CrossSectionData {
  road_id: number
  chainage: number
  center_point: {
    x: number
    y: number
    elevation: number
  }
  road_width: number
  left_points: Array<[number, number]>
  right_points: Array<[number, number]>
  cut_area: number
  fill_area: number
}

interface VolumeData {
  road_id: number
  analysis_range: {
    start_chainage: number
    end_chainage: number
    length: number
  }
  volumes: {
    total_cut_volume: number
    total_fill_volume: number
    net_volume: number
    balance_ratio: number
  }
  cut_sections: Array<any>
  fill_sections: Array<any>
}

const ProfileAnalysis: React.FC = () => {
  const { projectId } = useParams<{ projectId: string }>()
  const [analysisList, setAnalysisList] = useState<ProfileAnalysisData[]>([])
  const [selectedAnalysis, setSelectedAnalysis] = useState<ProfileAnalysisData | null>(null)
  const [longitudinalData, setLongitudinalData] = useState<LongitudinalData | null>(null)
  const [crossSectionData, setCrossSectionData] = useState<CrossSectionData | null>(null)
  const [volumeData, setVolumeData] = useState<VolumeData | null>(null)
  const [loading, setLoading] = useState(false)
  const [analyzing, setAnalyzing] = useState(false)
  const [analysisModalVisible, setAnalysisModalVisible] = useState(false)
  const [detailModalVisible, setDetailModalVisible] = useState(false)
  const [profileModalVisible, setProfileModalVisible] = useState(false)
  const [crossSectionModalVisible, setCrossSectionModalVisible] = useState(false)
  const [form] = Form.useForm()
  const [crossSectionForm] = Form.useForm()

  // 获取剖面分析列表
  const fetchAnalysisList = async () => {
    if (!projectId) return
    
    setLoading(true)
    try {
      const response = await axios.get(`/api/v1/profiles/${projectId}/profile-analyses`)
      setAnalysisList(response.data)
    } catch (error) {
      message.error('获取剖面分析列表失败')
    } finally {
      setLoading(false)
    }
  }

  useEffect(() => {
    fetchAnalysisList()
  }, [projectId])

  // 开始剖面分析
  const handleStartAnalysis = async () => {
    try {
      const values = await form.validateFields()
      setAnalyzing(true)
      
      const analysisParams = {
        analysis_type: values.analysis_type,
        include_longitudinal: values.include_longitudinal,
        include_cross_sections: values.include_cross_sections,
        include_volumes: values.include_volumes,
        cross_section_interval: values.cross_section_interval,
        analysis_interval: values.analysis_interval
      }

      if (values.road_ids && values.road_ids.length > 0) {
        // 单个道路分析
        for (const roadId of values.road_ids) {
          await axios.post(
            `/api/v1/profiles/${projectId}/roads/${roadId}/analyze`,
            analysisParams
          )
        }
      } else {
        // 批量分析
        await axios.post(
          `/api/v1/profiles/${projectId}/batch-analyze`,
          analysisParams
        )
      }
      
      message.success('剖面分析完成')
      setAnalysisModalVisible(false)
      form.resetFields()
      fetchAnalysisList()
    } catch (error) {
      message.error('剖面分析失败')
    } finally {
      setAnalyzing(false)
    }
  }

  // 查看分析详情
  const handleViewDetail = (analysis: ProfileAnalysisData) => {
    setSelectedAnalysis(analysis)
    setDetailModalVisible(true)
  }

  // 查看纵断面
  const handleViewLongitudinalProfile = async (roadId: number) => {
    try {
      const response = await axios.get(`/api/v1/profiles/${projectId}/roads/${roadId}/longitudinal-profile`)
      setLongitudinalData(response.data)
      setProfileModalVisible(true)
    } catch (error) {
      message.error('获取纵断面数据失败')
    }
  }

  // 查看横断面
  const handleViewCrossSection = async () => {
    try {
      const values = await crossSectionForm.validateFields()
      const response = await axios.get(
        `/api/v1/profiles/${projectId}/roads/${values.road_id}/cross-section?chainage=${values.chainage}`
      )
      setCrossSectionData(response.data)
      setCrossSectionModalVisible(true)
    } catch (error) {
      message.error('获取横断面数据失败')
    }
  }

  // 计算土方量
  const handleCalculateVolumes = async (roadId: number) => {
    try {
      const response = await axios.get(`/api/v1/profiles/${projectId}/roads/${roadId}/earthwork-volumes`)
      setVolumeData(response.data)
      message.success('土方量计算完成')
    } catch (error) {
      message.error('土方量计算失败')
    }
  }

  // 获取分析类型文本
  const getAnalysisTypeText = (type: string) => {
    const typeMap = {
      comprehensive: '综合分析',
      longitudinal: '纵断面分析',
      cross_section: '横断面分析',
      volume: '土方量分析'
    }
    return typeMap[type as keyof typeof typeMap] || type
  }

  // 分析表格列定义
  const analysisColumns = [
    {
      title: '道路ID',
      dataIndex: 'road_id',
      key: 'road_id',
      render: (roadId: number) => (
        <Space>
          <CarOutlined />
          <span>道路 {roadId}</span>
        </Space>
      )
    },
    {
      title: '分析类型',
      dataIndex: 'analysis_type',
      key: 'analysis_type',
      render: (type: string) => getAnalysisTypeText(type)
    },
    {
      title: '道路长度',
      dataIndex: 'total_length',
      key: 'total_length',
      render: (length: number) => `${length.toFixed(1)}m`
    },
    {
      title: '坡度信息',
      key: 'gradient',
      render: (record: ProfileAnalysisData) => (
        <Space direction="vertical" size="small">
          {record.max_gradient && <span>最大: {record.max_gradient.toFixed(1)}%</span>}
          {record.average_gradient && <span>平均: {record.average_gradient.toFixed(1)}%</span>}
        </Space>
      )
    },
    {
      title: '土方量',
      key: 'volumes',
      render: (record: ProfileAnalysisData) => (
        <Space direction="vertical" size="small">
          {record.total_cut_volume && <span>挖方: {record.total_cut_volume.toFixed(0)}m³</span>}
          {record.total_fill_volume && <span>填方: {record.total_fill_volume.toFixed(0)}m³</span>}
          {record.balance_ratio && (
            <span style={{ color: record.balance_ratio > 0.8 ? '#52c41a' : '#faad14' }}>
              平衡率: {(record.balance_ratio * 100).toFixed(1)}%
            </span>
          )}
        </Space>
      )
    },
    {
      title: '横断面数',
      dataIndex: 'cross_sections_count',
      key: 'cross_sections_count',
      render: (count: number) => `${count}个`
    },
    {
      title: '分析时间',
      dataIndex: 'analyzed_at',
      key: 'analyzed_at',
      render: (time: string) => new Date(time).toLocaleString()
    },
    {
      title: '操作',
      key: 'actions',
      render: (record: ProfileAnalysisData) => (
        <Space>
          <Tooltip title="查看详情">
            <Button 
              type="text" 
              icon={<EyeOutlined />} 
              size="small"
              onClick={() => handleViewDetail(record)}
            />
          </Tooltip>
          
          <Tooltip title="纵断面">
            <Button 
              type="text" 
              icon={<LineChartOutlined />} 
              size="small"
              onClick={() => handleViewLongitudinalProfile(record.road_id)}
            />
          </Tooltip>
          
          <Tooltip title="土方量">
            <Button 
              type="text" 
              icon={<BarChartOutlined />} 
              size="small"
              onClick={() => handleCalculateVolumes(record.road_id)}
            />
          </Tooltip>
        </Space>
      )
    }
  ]

  return (
    <div>
      {/* 页面标题 */}
      <div style={{ marginBottom: '24px' }}>
        <h1 style={{ fontSize: '24px', fontWeight: 'bold', margin: 0 }}>
          剖面分析
        </h1>
        <p style={{ color: '#666', margin: '8px 0 0 0' }}>
          道路纵横断面分析，土方量计算和工程量统计
        </p>
      </div>

      {/* 统计卡片 */}
      <Row gutter={[16, 16]} style={{ marginBottom: '24px' }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="分析总数"
              value={analysisList.length}
              prefix={<AreaChartOutlined />}
              suffix="次"
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总挖方量"
              value={analysisList.reduce((sum, a) => sum + (a.total_cut_volume || 0), 0)}
              prefix={<BarChartOutlined />}
              suffix="m³"
              precision={0}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总填方量"
              value={analysisList.reduce((sum, a) => sum + (a.total_fill_volume || 0), 0)}
              prefix={<BarChartOutlined />}
              suffix="m³"
              precision={0}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均平衡率"
              value={analysisList.length > 0 
                ? analysisList.reduce((sum, a) => sum + (a.balance_ratio || 0), 0) / analysisList.length * 100
                : 0}
              prefix={<PieChartOutlined />}
              suffix="%"
              precision={1}
              valueStyle={{ 
                color: analysisList.length > 0 && 
                       analysisList.reduce((sum, a) => sum + (a.balance_ratio || 0), 0) / analysisList.length > 0.8 
                       ? '#52c41a' : '#faad14' 
              }}
            />
          </Card>
        </Col>
      </Row>

      {/* 主要内容 */}
      <Card 
        title="剖面分析记录"
        extra={
          <Space>
            <Button 
              icon={<ReloadOutlined />}
              onClick={fetchAnalysisList}
            >
              刷新
            </Button>
            <Button 
              icon={<LineChartOutlined />}
              onClick={() => {
                crossSectionForm.resetFields()
                setCrossSectionModalVisible(true)
              }}
            >
              查看横断面
            </Button>
            <Button 
              type="primary" 
              icon={<PlayCircleOutlined />}
              onClick={() => setAnalysisModalVisible(true)}
              loading={analyzing}
            >
              开始分析
            </Button>
          </Space>
        }
      >
        <Table
          columns={analysisColumns}
          dataSource={analysisList}
          rowKey="id"
          loading={loading}
          pagination={{
            showSizeChanger: true,
            showQuickJumper: true,
            showTotal: (total) => `共 ${total} 条记录`
          }}
        />
      </Card>

      {/* 剖面分析参数模态框 */}
      <Modal
        title="剖面分析设置"
        open={analysisModalVisible}
        onOk={handleStartAnalysis}
        onCancel={() => {
          setAnalysisModalVisible(false)
          form.resetFields()
        }}
        confirmLoading={analyzing}
        okText="开始分析"
        cancelText="取消"
        width={600}
      >
        <Form form={form} layout="vertical">
          <Form.Item
            name="analysis_type"
            label="分析类型"
            initialValue="comprehensive"
          >
            <Select>
              <Select.Option value="comprehensive">综合分析</Select.Option>
              <Select.Option value="longitudinal">纵断面分析</Select.Option>
              <Select.Option value="cross_section">横断面分析</Select.Option>
              <Select.Option value="volume">土方量分析</Select.Option>
            </Select>
          </Form.Item>
          
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item
                name="cross_section_interval"
                label="横断面间隔 (米)"
                initialValue={20}
              >
                <InputNumber min={5} max={100} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item
                name="analysis_interval"
                label="分析间隔 (米)"
                initialValue={10}
              >
                <InputNumber min={1} max={50} style={{ width: '100%' }} />
              </Form.Item>
            </Col>
          </Row>
          
          <Form.Item label="分析内容">
            <Space direction="vertical">
              <Form.Item
                name="include_longitudinal"
                valuePropName="checked"
                initialValue={true}
                style={{ margin: 0 }}
              >
                <Checkbox>纵断面分析</Checkbox>
              </Form.Item>
              
              <Form.Item
                name="include_cross_sections"
                valuePropName="checked"
                initialValue={true}
                style={{ margin: 0 }}
              >
                <Checkbox>横断面分析</Checkbox>
              </Form.Item>
              
              <Form.Item
                name="include_volumes"
                valuePropName="checked"
                initialValue={true}
                style={{ margin: 0 }}
              >
                <Checkbox>土方量计算</Checkbox>
              </Form.Item>
            </Space>
          </Form.Item>
          
          <Form.Item
            name="road_ids"
            label="选择道路"
            help="留空则分析项目下所有道路"
          >
            <Select
              mode="multiple"
              placeholder="选择要分析的道路，留空分析全部"
              allowClear
            >
              {/* TODO: 从API获取道路列表 */}
              <Select.Option value={1}>道路 1</Select.Option>
              <Select.Option value={2}>道路 2</Select.Option>
              <Select.Option value={3}>道路 3</Select.Option>
            </Select>
          </Form.Item>
        </Form>
      </Modal>

      {/* 分析详情模态框 */}
      <Modal
        title={`剖面分析详情 - 道路 ${selectedAnalysis?.road_id}`}
        open={detailModalVisible}
        onCancel={() => setDetailModalVisible(false)}
        footer={null}
        width={800}
      >
        {selectedAnalysis && (
          <Descriptions bordered size="small">
            <Descriptions.Item label="分析类型" span={2}>
              {getAnalysisTypeText(selectedAnalysis.analysis_type)}
            </Descriptions.Item>
            <Descriptions.Item label="道路长度">
              {selectedAnalysis.total_length.toFixed(1)}m
            </Descriptions.Item>
            <Descriptions.Item label="最大坡度">
              {selectedAnalysis.max_gradient ? `${selectedAnalysis.max_gradient.toFixed(1)}%` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="最小坡度">
              {selectedAnalysis.min_gradient ? `${selectedAnalysis.min_gradient.toFixed(1)}%` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="平均坡度">
              {selectedAnalysis.average_gradient ? `${selectedAnalysis.average_gradient.toFixed(1)}%` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="挖方量">
              {selectedAnalysis.total_cut_volume ? `${selectedAnalysis.total_cut_volume.toFixed(0)}m³` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="填方量">
              {selectedAnalysis.total_fill_volume ? `${selectedAnalysis.total_fill_volume.toFixed(0)}m³` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="净土方量">
              {selectedAnalysis.net_volume ? `${selectedAnalysis.net_volume.toFixed(0)}m³` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="平衡率">
              {selectedAnalysis.balance_ratio ? `${(selectedAnalysis.balance_ratio * 100).toFixed(1)}%` : '-'}
            </Descriptions.Item>
            <Descriptions.Item label="横断面数">
              {selectedAnalysis.cross_sections_count}个
            </Descriptions.Item>
            <Descriptions.Item label="分析时间" span={2}>
              {new Date(selectedAnalysis.analyzed_at).toLocaleString()}
            </Descriptions.Item>
          </Descriptions>
        )}
      </Modal>

      {/* 纵断面模态框 */}
      <Modal
        title="纵断面图"
        open={profileModalVisible}
        onCancel={() => setProfileModalVisible(false)}
        footer={null}
        width={1200}
      >
        {longitudinalData && (
          <Tabs defaultActiveKey="profile">
            <TabPane tab="纵断面图" key="profile">
              <div style={{ 
                height: '400px', 
                background: '#f5f5f5', 
                display: 'flex', 
                alignItems: 'center', 
                justifyContent: 'center',
                border: '1px dashed #d9d9d9',
                borderRadius: '6px'
              }}>
                <div style={{ textAlign: 'center', color: '#999' }}>
                  <LineChartOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
                  <div>纵断面图表</div>
                  <div style={{ fontSize: '12px' }}>功能开发中...</div>
                </div>
              </div>
            </TabPane>
            
            <TabPane tab="坡度信息" key="gradient">
              <Descriptions bordered size="small" style={{ marginBottom: '16px' }}>
                <Descriptions.Item label="道路长度">{longitudinalData.analysis.total_length.toFixed(1)}m</Descriptions.Item>
                <Descriptions.Item label="最大坡度">{longitudinalData.analysis.max_gradient.toFixed(1)}%</Descriptions.Item>
                <Descriptions.Item label="最小坡度">{longitudinalData.analysis.min_gradient.toFixed(1)}%</Descriptions.Item>
                <Descriptions.Item label="平均坡度">{longitudinalData.analysis.average_gradient.toFixed(1)}%</Descriptions.Item>
                <Descriptions.Item label="总上升">{longitudinalData.analysis.total_rise.toFixed(1)}m</Descriptions.Item>
                <Descriptions.Item label="总下降">{longitudinalData.analysis.total_fall.toFixed(1)}m</Descriptions.Item>
                <Descriptions.Item label="竖曲线数" span={3}>{longitudinalData.analysis.vertical_curves_count}个</Descriptions.Item>
              </Descriptions>
            </TabPane>
            
            <TabPane tab="竖曲线" key="curves">
              <List
                dataSource={longitudinalData.vertical_curves}
                renderItem={(curve) => (
                  <List.Item>
                    <List.Item.Meta
                      title={`${curve.curve_type === 'crest' ? '凸形' : '凹形'}竖曲线`}
                      description={
                        <Space direction="vertical" size="small">
                          <span>里程: {curve.start_chainage.toFixed(1)}m - {curve.end_chainage.toFixed(1)}m</span>
                          <span>长度: {curve.length.toFixed(1)}m，半径: {curve.radius.toFixed(1)}m</span>
                          <span>进入坡度: {curve.grade_in.toFixed(1)}%，离开坡度: {curve.grade_out.toFixed(1)}%</span>
                        </Space>
                      }
                    />
                  </List.Item>
                )}
              />
            </TabPane>
          </Tabs>
        )}
      </Modal>

      {/* 横断面查看模态框 */}
      <Modal
        title="查看横断面"
        open={crossSectionModalVisible}
        onOk={handleViewCrossSection}
        onCancel={() => setCrossSectionModalVisible(false)}
        okText="查看"
        cancelText="取消"
      >
        <Form form={crossSectionForm} layout="vertical">
          <Form.Item
            name="road_id"
            label="选择道路"
            rules={[{ required: true, message: '请选择道路' }]}
          >
            <Select placeholder="选择要查看的道路">
              {/* TODO: 从API获取道路列表 */}
              <Select.Option value={1}>道路 1</Select.Option>
              <Select.Option value={2}>道路 2</Select.Option>
              <Select.Option value={3}>道路 3</Select.Option>
            </Select>
          </Form.Item>
          
          <Form.Item
            name="chainage"
            label="里程桩号 (米)"
            rules={[{ required: true, message: '请输入里程桩号' }]}
          >
            <InputNumber 
              style={{ width: '100%' }}
              placeholder="例如: 100.5"
              min={0}
              step={0.1}
            />
          </Form.Item>
        </Form>
        
        {crossSectionData && (
          <div style={{ marginTop: '16px' }}>
            <Alert
              message="横断面信息"
              description={
                <Space direction="vertical" size="small">
                  <span>里程: {crossSectionData.chainage.toFixed(1)}m</span>
                  <span>中心高程: {crossSectionData.center_point.elevation.toFixed(2)}m</span>
                  <span>道路宽度: {crossSectionData.road_width.toFixed(1)}m</span>
                  <span>挖方面积: {crossSectionData.cut_area.toFixed(2)}m²</span>
                  <span>填方面积: {crossSectionData.fill_area.toFixed(2)}m²</span>
                </Space>
              }
              type="info"
              showIcon
            />
            
            <div style={{ 
              height: '200px', 
              background: '#f5f5f5', 
              display: 'flex', 
              alignItems: 'center', 
              justifyContent: 'center',
              border: '1px dashed #d9d9d9',
              borderRadius: '6px',
              marginTop: '16px'
            }}>
              <div style={{ textAlign: 'center', color: '#999' }}>
                <AreaChartOutlined style={{ fontSize: '32px', marginBottom: '8px' }} />
                <div>横断面图</div>
                <div style={{ fontSize: '12px' }}>功能开发中...</div>
              </div>
            </div>
          </div>
        )}
      </Modal>

      {/* 土方量信息 */}
      {volumeData && (
        <Card 
          title="土方量计算结果" 
          style={{ marginTop: '24px' }}
          extra={
            <Button onClick={() => setVolumeData(null)}>
              关闭
            </Button>
          }
        >
          <Row gutter={[16, 16]}>
            <Col xs={24} sm={6}>
              <Statistic
                title="挖方量"
                value={volumeData.volumes.total_cut_volume}
                suffix="m³"
                precision={0}
              />
            </Col>
            <Col xs={24} sm={6}>
              <Statistic
                title="填方量"
                value={volumeData.volumes.total_fill_volume}
                suffix="m³"
                precision={0}
              />
            </Col>
            <Col xs={24} sm={6}>
              <Statistic
                title="净土方量"
                value={volumeData.volumes.net_volume}
                suffix="m³"
                precision={0}
                valueStyle={{ 
                  color: volumeData.volumes.net_volume > 0 ? '#f5222d' : '#52c41a' 
                }}
              />
            </Col>
            <Col xs={24} sm={6}>
              <Statistic
                title="平衡率"
                value={volumeData.volumes.balance_ratio * 100}
                suffix="%"
                precision={1}
                valueStyle={{ 
                  color: volumeData.volumes.balance_ratio > 0.8 ? '#52c41a' : '#faad14' 
                }}
              />
            </Col>
          </Row>
          
          <div style={{ marginTop: '16px' }}>
            <Descriptions bordered size="small">
              <Descriptions.Item label="分析范围" span={3}>
                里程 {volumeData.analysis_range.start_chainage.toFixed(1)}m - {volumeData.analysis_range.end_chainage.toFixed(1)}m
                (长度: {volumeData.analysis_range.length.toFixed(1)}m)
              </Descriptions.Item>
              <Descriptions.Item label="挖方段数">{volumeData.cut_sections.length}段</Descriptions.Item>
              <Descriptions.Item label="填方段数">{volumeData.fill_sections.length}段</Descriptions.Item>
              <Descriptions.Item label="土方类型">
                {volumeData.volumes.net_volume > 0 ? '挖方为主' : 
                 volumeData.volumes.net_volume < 0 ? '填方为主' : '基本平衡'}
              </Descriptions.Item>
            </Descriptions>
          </div>
        </Card>
      )}
    </div>
  )
}

export default ProfileAnalysis
