"""
安全检测API端点
"""
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict
from pydantic import BaseModel

from app.core.database import get_db
from app.services.safety_service import SafetyService
from app.models.safety_analysis import SafetyAnalysis, SafetyIssue

router = APIRouter()
safety_service = SafetyService()


class SafetyAnalysisParams(BaseModel):
    """安全分析参数"""
    analysis_type: str = "comprehensive"
    include_sight_distance: bool = True
    include_slope_stability: bool = True
    include_geometric_safety: bool = True
    include_drainage: bool = True
    include_traffic_safety: bool = True
    traffic_data: Optional[Dict] = None


class SafetyAnalysisResponse(BaseModel):
    """安全分析响应模型"""
    id: int
    road_id: int
    analysis_type: str
    overall_safety_score: float
    safety_level: str
    total_issues: int
    critical_issues: int
    warning_issues: int
    analyzed_at: str
    
    class Config:
        from_attributes = True


class SafetyIssueResponse(BaseModel):
    """安全问题响应模型"""
    id: int
    issue_type: str
    severity_level: str
    title: str
    description: str
    location: List[float]
    chainage: Optional[float]
    risk_score: Optional[float]
    urgency_level: Optional[int]
    
    class Config:
        from_attributes = True


@router.post("/{project_id}/roads/{road_id}/analyze")
async def analyze_road_safety(
    project_id: int,
    road_id: int,
    analysis_params: SafetyAnalysisParams,
    db: Session = Depends(get_db)
):
    """分析道路安全性"""
    try:
        safety_analysis = await safety_service.analyze_road_safety(
            db=db,
            road_id=road_id,
            analysis_params=analysis_params.dict()
        )
        
        return SafetyAnalysisResponse(
            id=safety_analysis.id,
            road_id=safety_analysis.road_id,
            analysis_type=safety_analysis.analysis_type,
            overall_safety_score=safety_analysis.overall_safety_score,
            safety_level=safety_analysis.safety_level,
            total_issues=safety_analysis.total_issues,
            critical_issues=safety_analysis.critical_issues,
            warning_issues=safety_analysis.warning_issues,
            analyzed_at=safety_analysis.analyzed_at.isoformat()
        )
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"安全分析失败: {str(e)}")


@router.get("/{project_id}/safety-analyses", response_model=List[SafetyAnalysisResponse])
async def get_project_safety_analyses(
    project_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取项目安全分析列表"""
    try:
        analyses = safety_service.get_project_safety_analyses(
            db=db,
            project_id=project_id,
            skip=skip,
            limit=limit
        )
        
        return [
            SafetyAnalysisResponse(
                id=analysis.id,
                road_id=analysis.road_id,
                analysis_type=analysis.analysis_type,
                overall_safety_score=analysis.overall_safety_score,
                safety_level=analysis.safety_level,
                total_issues=analysis.total_issues,
                critical_issues=analysis.critical_issues,
                warning_issues=analysis.warning_issues,
                analyzed_at=analysis.analyzed_at.isoformat()
            )
            for analysis in analyses
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取安全分析列表失败: {str(e)}")


@router.get("/roads/{road_id}/safety-analyses", response_model=List[SafetyAnalysisResponse])
async def get_road_safety_analyses(
    road_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    db: Session = Depends(get_db)
):
    """获取道路安全分析列表"""
    try:
        analyses = safety_service.get_road_safety_analyses(
            db=db,
            road_id=road_id,
            skip=skip,
            limit=limit
        )
        
        return [
            SafetyAnalysisResponse(
                id=analysis.id,
                road_id=analysis.road_id,
                analysis_type=analysis.analysis_type,
                overall_safety_score=analysis.overall_safety_score,
                safety_level=analysis.safety_level,
                total_issues=analysis.total_issues,
                critical_issues=analysis.critical_issues,
                warning_issues=analysis.warning_issues,
                analyzed_at=analysis.analyzed_at.isoformat()
            )
            for analysis in analyses
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取道路安全分析列表失败: {str(e)}")


@router.get("/analyses/{analysis_id}", response_model=SafetyAnalysisResponse)
async def get_safety_analysis_detail(
    analysis_id: int,
    db: Session = Depends(get_db)
):
    """获取安全分析详情"""
    try:
        analysis = safety_service.get_safety_analysis_detail(db=db, analysis_id=analysis_id)
        
        if not analysis:
            raise HTTPException(status_code=404, detail="安全分析不存在")
        
        return SafetyAnalysisResponse(
            id=analysis.id,
            road_id=analysis.road_id,
            analysis_type=analysis.analysis_type,
            overall_safety_score=analysis.overall_safety_score,
            safety_level=analysis.safety_level,
            total_issues=analysis.total_issues,
            critical_issues=analysis.critical_issues,
            warning_issues=analysis.warning_issues,
            analyzed_at=analysis.analyzed_at.isoformat()
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取安全分析详情失败: {str(e)}")


@router.get("/analyses/{analysis_id}/issues", response_model=List[SafetyIssueResponse])
async def get_safety_issues(
    analysis_id: int,
    skip: int = Query(0, ge=0, description="跳过记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回记录数"),
    severity: Optional[str] = Query(None, description="严重程度过滤"),
    issue_type: Optional[str] = Query(None, description="问题类型过滤"),
    db: Session = Depends(get_db)
):
    """获取安全问题列表"""
    try:
        issues = safety_service.get_safety_issues(
            db=db,
            analysis_id=analysis_id,
            skip=skip,
            limit=limit
        )
        
        # 应用过滤器
        if severity:
            issues = [i for i in issues if i.severity_level == severity]
        
        if issue_type:
            issues = [i for i in issues if i.issue_type == issue_type]
        
        return [
            SafetyIssueResponse(
                id=issue.id,
                issue_type=issue.issue_type,
                severity_level=issue.severity_level,
                title=issue.title,
                description=issue.description,
                location=issue.location,
                chainage=issue.chainage,
                risk_score=issue.risk_score,
                urgency_level=issue.urgency_level
            )
            for issue in issues
        ]
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取安全问题列表失败: {str(e)}")


@router.get("/{project_id}/roads/{road_id}/sight-distance")
async def analyze_sight_distance(
    project_id: int,
    road_id: int,
    db: Session = Depends(get_db)
):
    """分析道路视距"""
    try:
        sight_analysis = await safety_service.analyze_sight_distance(
            db=db,
            road_id=road_id
        )
        
        return sight_analysis
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"视距分析失败: {str(e)}")


@router.get("/{project_id}/safety-statistics")
async def get_safety_statistics(
    project_id: int,
    db: Session = Depends(get_db)
):
    """获取安全统计信息"""
    try:
        statistics = safety_service.get_safety_statistics(db=db, project_id=project_id)
        
        return {
            "project_id": project_id,
            "statistics": statistics
        }
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取安全统计失败: {str(e)}")


@router.get("/{project_id}/safety-report")
async def generate_safety_report(
    project_id: int,
    db: Session = Depends(get_db)
):
    """生成安全报告"""
    try:
        report = safety_service.generate_safety_report(db=db, project_id=project_id)
        
        return report
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"生成安全报告失败: {str(e)}")


@router.post("/{project_id}/batch-analyze")
async def batch_analyze_safety(
    project_id: int,
    analysis_params: SafetyAnalysisParams,
    road_ids: Optional[List[int]] = None,
    db: Session = Depends(get_db)
):
    """批量安全分析"""
    try:
        # 获取要分析的道路
        from app.models.road import Road
        
        if road_ids:
            roads = db.query(Road).filter(
                Road.project_id == project_id,
                Road.id.in_(road_ids)
            ).all()
        else:
            roads = db.query(Road).filter(Road.project_id == project_id).all()
        
        if not roads:
            raise HTTPException(status_code=404, detail="未找到要分析的道路")
        
        # 批量执行安全分析
        results = []
        for road in roads:
            try:
                safety_analysis = await safety_service.analyze_road_safety(
                    db=db,
                    road_id=road.id,
                    analysis_params=analysis_params.dict()
                )
                
                results.append({
                    'road_id': road.id,
                    'road_name': road.name,
                    'analysis_id': safety_analysis.id,
                    'safety_score': safety_analysis.overall_safety_score,
                    'safety_level': safety_analysis.safety_level,
                    'total_issues': safety_analysis.total_issues,
                    'status': 'success'
                })
                
            except Exception as e:
                results.append({
                    'road_id': road.id,
                    'road_name': road.name,
                    'status': 'failed',
                    'error': str(e)
                })
        
        return {
            'project_id': project_id,
            'total_roads': len(roads),
            'successful_analyses': len([r for r in results if r['status'] == 'success']),
            'failed_analyses': len([r for r in results if r['status'] == 'failed']),
            'results': results
        }
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"批量安全分析失败: {str(e)}")


@router.get("/standards/safety-criteria")
async def get_safety_criteria():
    """获取安全标准和评判标准"""
    try:
        criteria = {
            'sight_distance': {
                'description': '视距安全标准',
                'parameters': {
                    'min_stopping_sight_distance': '最小停车视距',
                    'reaction_time': '反应时间(2.5秒)',
                    'friction_coefficient': '摩擦系数(0.35)'
                },
                'evaluation': {
                    'safe': '实际视距 >= 要求视距',
                    'caution': '实际视距 >= 要求视距 * 0.9',
                    'warning': '实际视距 >= 要求视距 * 0.7',
                    'danger': '实际视距 < 要求视距 * 0.7'
                }
            },
            'slope_stability': {
                'description': '边坡稳定性标准',
                'parameters': {
                    'max_cut_height': '最大挖方高度(20米)',
                    'min_stability_factor': '最小稳定系数(1.5)',
                    'slope_angle': '边坡角度限制'
                },
                'evaluation': {
                    'safe': '稳定系数 >= 1.5',
                    'caution': '稳定系数 >= 1.3',
                    'warning': '稳定系数 >= 1.2',
                    'danger': '稳定系数 < 1.2'
                }
            },
            'geometric_safety': {
                'description': '几何设计安全标准',
                'parameters': {
                    'max_gradient': '最大坡度(8%)',
                    'min_radius': '最小转弯半径(15米)',
                    'min_lane_width': '最小车道宽度(3米)'
                },
                'evaluation': {
                    'safe': '所有参数满足标准',
                    'caution': '轻微超出标准',
                    'warning': '明显超出标准',
                    'danger': '严重超出标准'
                }
            }
        }
        
        return criteria
        
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取安全标准失败: {str(e)}")
