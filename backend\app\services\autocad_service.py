"""
AutoCAD集成服务
"""
import os
import json
import tempfile
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import asyncio

from app.models.cad_integration import CADFile, CADDrawing
from app.models.road import Road
from app.models.project import Project
from app.core.config import settings
from algorithms.autocad_integration.dwg_processor import DWGProcessor
from algorithms.autocad_integration.drawing_generator import DrawingGenerator, DrawingTemplate, RoadDrawingData
import logging

logger = logging.getLogger(__name__)


class AutoCADService:
    """AutoCAD集成服务类"""
    
    def __init__(self):
        self.dwg_processor = DWGProcessor()
        self.drawing_generator = DrawingGenerator()
        self.upload_dir = os.path.join(settings.UPLOAD_DIR, 'cad_files')
        self.output_dir = os.path.join(settings.UPLOAD_DIR, 'generated_drawings')
        
        # 确保目录存在
        os.makedirs(self.upload_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)
    
    async def import_cad_file(self, 
                            db: Session,
                            project_id: int,
                            file_path: str,
                            filename: str) -> CADFile:
        """导入CAD文件"""
        try:
            logger.info(f"导入CAD文件: {filename}")
            
            # 验证文件
            validation_result = self.dwg_processor.validate_file(file_path)
            if not validation_result['valid']:
                raise ValueError(f"文件验证失败: {validation_result['error']}")
            
            # 读取CAD文件
            cad_drawing = await asyncio.get_event_loop().run_in_executor(
                None,
                self.dwg_processor.read_dwg_file,
                file_path
            )
            
            if not cad_drawing:
                raise ValueError("无法读取CAD文件")
            
            # 转换为道路数据
            road_data = self.dwg_processor.convert_to_road_data(cad_drawing)
            
            # 保存到数据库
            cad_file = CADFile(
                project_id=project_id,
                filename=filename,
                file_path=file_path,
                file_size=os.path.getsize(file_path),
                file_type='dwg' if filename.lower().endswith('.dwg') else 'dxf',
                cad_version=cad_drawing.version,
                units=cad_drawing.units,
                bounds=list(cad_drawing.bounds),
                layer_count=len(cad_drawing.layers),
                entity_count=len(cad_drawing.entities),
                import_status='completed',
                imported_at=datetime.utcnow()
            )
            
            db.add(cad_file)
            db.commit()
            db.refresh(cad_file)
            
            # 保存CAD图纸数据
            cad_drawing_record = CADDrawing(
                cad_file_id=cad_file.id,
                drawing_data=road_data,
                layers_data=[
                    {
                        'name': layer.name,
                        'color': layer.color,
                        'linetype': layer.linetype,
                        'visible': layer.visible,
                        'locked': layer.locked
                    }
                    for layer in cad_drawing.layers
                ],
                entities_data=[
                    {
                        'type': entity.entity_type,
                        'layer': entity.layer,
                        'coordinates': entity.coordinates,
                        'properties': entity.properties
                    }
                    for entity in cad_drawing.entities
                ],
                metadata=cad_drawing.metadata
            )
            
            db.add(cad_drawing_record)
            db.commit()
            
            # 如果包含道路数据，创建道路记录
            if road_data.get('roads'):
                await self._create_roads_from_cad(db, project_id, road_data['roads'])
            
            logger.info(f"CAD文件导入完成: {filename}")
            return cad_file
            
        except Exception as e:
            logger.error(f"导入CAD文件失败: {str(e)}")
            raise
    
    async def generate_plan_drawing(self, 
                                  db: Session,
                                  project_id: int,
                                  template_name: str = "道路平面图-A1") -> str:
        """生成平面图"""
        try:
            logger.info(f"生成项目平面图: {project_id}")
            
            # 获取项目道路数据
            road_data = await self._get_project_road_data(db, project_id)
            
            # 获取图纸模板
            template = self._get_drawing_template(template_name)
            
            # 生成输出文件名
            project = db.query(Project).filter(Project.id == project_id).first()
            project_name = project.name if project else f"Project_{project_id}"
            
            output_filename = f"{project_name}_平面图_{datetime.now().strftime('%Y%m%d_%H%M%S')}.dxf"
            output_path = os.path.join(self.output_dir, output_filename)
            
            # 生成图纸
            success = await asyncio.get_event_loop().run_in_executor(
                None,
                self.drawing_generator.generate_plan_drawing,
                road_data,
                template,
                output_path
            )
            
            if not success:
                raise ValueError("图纸生成失败")
            
            logger.info(f"平面图生成完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"生成平面图失败: {str(e)}")
            raise
    
    async def generate_profile_drawing(self, 
                                     db: Session,
                                     road_id: int,
                                     template_name: str = "纵断面图-A1") -> str:
        """生成纵断面图"""
        try:
            logger.info(f"生成道路纵断面图: {road_id}")
            
            # 获取道路纵断面数据
            profile_data = await self._get_road_profile_data(db, road_id)
            
            # 获取图纸模板
            template = self._get_drawing_template(template_name)
            
            # 生成输出文件名
            road = db.query(Road).filter(Road.id == road_id).first()
            road_name = road.name if road else f"Road_{road_id}"
            
            output_filename = f"{road_name}_纵断面图_{datetime.now().strftime('%Y%m%d_%H%M%S')}.dxf"
            output_path = os.path.join(self.output_dir, output_filename)
            
            # 生成图纸
            success = await asyncio.get_event_loop().run_in_executor(
                None,
                self.drawing_generator.generate_profile_drawing,
                profile_data,
                template,
                output_path
            )
            
            if not success:
                raise ValueError("纵断面图生成失败")
            
            logger.info(f"纵断面图生成完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"生成纵断面图失败: {str(e)}")
            raise
    
    async def generate_cross_section_drawing(self, 
                                           db: Session,
                                           road_id: int,
                                           chainages: List[float] = None,
                                           template_name: str = "横断面图-A2") -> str:
        """生成横断面图"""
        try:
            logger.info(f"生成道路横断面图: {road_id}")
            
            # 获取道路横断面数据
            cross_section_data = await self._get_road_cross_section_data(db, road_id, chainages)
            
            # 获取图纸模板
            template = self._get_drawing_template(template_name)
            
            # 生成输出文件名
            road = db.query(Road).filter(Road.id == road_id).first()
            road_name = road.name if road else f"Road_{road_id}"
            
            output_filename = f"{road_name}_横断面图_{datetime.now().strftime('%Y%m%d_%H%M%S')}.dxf"
            output_path = os.path.join(self.output_dir, output_filename)
            
            # 生成图纸
            success = await asyncio.get_event_loop().run_in_executor(
                None,
                self.drawing_generator.generate_cross_section_drawing,
                cross_section_data,
                template,
                output_path
            )
            
            if not success:
                raise ValueError("横断面图生成失败")
            
            logger.info(f"横断面图生成完成: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"生成横断面图失败: {str(e)}")
            raise
    
    async def _get_project_road_data(self, db: Session, project_id: int) -> RoadDrawingData:
        """获取项目道路数据"""
        try:
            from app.models.road import RoadSegment, RoadPoint
            
            # 获取项目道路
            roads = db.query(Road).filter(Road.project_id == project_id).all()
            
            road_data = []
            points_data = []
            annotations_data = []
            
            for road in roads:
                # 获取道路段
                segments = db.query(RoadSegment).filter(RoadSegment.road_id == road.id).all()
                
                for segment in segments:
                    if segment.start_point and segment.end_point:
                        road_item = {
                            'road_id': f"road_{road.id}_seg_{segment.id}",
                            'name': road.name,
                            'coordinates': [
                                segment.start_point,
                                segment.end_point
                            ],
                            'width': road.road_width,
                            'properties': {
                                'design_speed': road.design_speed,
                                'gradient': segment.gradient
                            }
                        }
                        road_data.append(road_item)
                
                # 获取道路点位
                points = db.query(RoadPoint).join(RoadSegment).filter(
                    RoadSegment.road_id == road.id
                ).all()
                
                for point in points:
                    point_item = {
                        'point_id': f"point_{point.id}",
                        'x': point.x,
                        'y': point.y,
                        'z': point.z,
                        'chainage': point.chainage
                    }
                    points_data.append(point_item)
            
            return RoadDrawingData(
                roads=road_data,
                points=points_data,
                annotations=annotations_data
            )
            
        except Exception as e:
            logger.error(f"获取项目道路数据失败: {str(e)}")
            return RoadDrawingData(roads=[], points=[], annotations=[])
    
    async def _get_road_profile_data(self, db: Session, road_id: int) -> Dict:
        """获取道路纵断面数据"""
        try:
            from app.models.profile_analysis import ProfileAnalysis
            
            # 获取最新的剖面分析
            profile_analysis = db.query(ProfileAnalysis).filter(
                ProfileAnalysis.road_id == road_id
            ).order_by(ProfileAnalysis.analyzed_at.desc()).first()
            
            if profile_analysis and profile_analysis.analysis_data:
                longitudinal_profile = profile_analysis.analysis_data.get('longitudinal_profile')
                if longitudinal_profile and hasattr(longitudinal_profile, 'points'):
                    grade_points = [
                        {
                            'chainage': point.chainage,
                            'elevation': point.elevation,
                            'ground_elevation': point.ground_elevation,
                            'gradient': point.gradient
                        }
                        for point in longitudinal_profile.points
                    ]
                    
                    return {'grade_points': grade_points}
            
            # 如果没有剖面分析数据，使用道路基本数据
            road = db.query(Road).filter(Road.id == road_id).first()
            if road:
                from app.models.road import RoadSegment
                segments = db.query(RoadSegment).filter(RoadSegment.road_id == road_id).all()
                
                grade_points = []
                chainage = 0
                
                for segment in segments:
                    if segment.start_point:
                        grade_points.append({
                            'chainage': chainage,
                            'elevation': segment.start_point[2] if len(segment.start_point) > 2 else 0,
                            'ground_elevation': segment.start_point[2] if len(segment.start_point) > 2 else 0,
                            'gradient': segment.gradient or 0
                        })
                    
                    chainage += segment.length or 0
                    
                    if segment.end_point:
                        grade_points.append({
                            'chainage': chainage,
                            'elevation': segment.end_point[2] if len(segment.end_point) > 2 else 0,
                            'ground_elevation': segment.end_point[2] if len(segment.end_point) > 2 else 0,
                            'gradient': segment.gradient or 0
                        })
                
                return {'grade_points': grade_points}
            
            return {'grade_points': []}
            
        except Exception as e:
            logger.error(f"获取道路纵断面数据失败: {str(e)}")
            return {'grade_points': []}
    
    async def _get_road_cross_section_data(self, db: Session, road_id: int, chainages: List[float] = None) -> Dict:
        """获取道路横断面数据"""
        try:
            from app.models.profile_analysis import CrossSectionData
            
            # 如果没有指定里程，使用默认里程
            if not chainages:
                road = db.query(Road).filter(Road.id == road_id).first()
                if road:
                    # 每100米一个横断面
                    total_length = road.total_length or 1000
                    chainages = list(range(0, int(total_length), 100))
                else:
                    chainages = [0, 100, 200, 300, 400, 500]
            
            sections = []
            
            # 获取横断面数据
            for chainage in chainages:
                cross_section = db.query(CrossSectionData).filter(
                    CrossSectionData.chainage == chainage
                ).first()
                
                if cross_section:
                    section_data = {
                        'chainage': chainage,
                        'center_elevation': cross_section.center_elevation,
                        'left_points': cross_section.left_points,
                        'right_points': cross_section.right_points
                    }
                else:
                    # 生成默认横断面数据
                    section_data = {
                        'chainage': chainage,
                        'center_elevation': 100.0,  # 默认高程
                        'left_points': [(-20, 95), (-10, 98), (-3, 99.5)],
                        'right_points': [(3, 99.5), (10, 98), (20, 95)]
                    }
                
                sections.append(section_data)
            
            return {'sections': sections}
            
        except Exception as e:
            logger.error(f"获取道路横断面数据失败: {str(e)}")
            return {'sections': []}
    
    def _get_drawing_template(self, template_name: str) -> DrawingTemplate:
        """获取图纸模板"""
        try:
            templates = self.drawing_generator.get_standard_templates()
            
            for template in templates:
                if template.name == template_name:
                    return template
            
            # 如果没找到，返回默认模板
            return templates[0] if templates else DrawingTemplate(
                name="默认模板",
                paper_size="A1",
                scale=1000,
                units="m",
                title_block=True
            )
            
        except Exception as e:
            logger.warning(f"获取图纸模板失败: {str(e)}")
            return DrawingTemplate(
                name="默认模板",
                paper_size="A1",
                scale=1000,
                units="m",
                title_block=True
            )
    
    async def _create_roads_from_cad(self, db: Session, project_id: int, roads_data: List[Dict]) -> None:
        """从CAD数据创建道路记录"""
        try:
            from app.models.road import RoadSegment
            
            for road_data in roads_data:
                # 检查是否已存在同名道路
                existing_road = db.query(Road).filter(
                    Road.project_id == project_id,
                    Road.name == road_data.get('name', '')
                ).first()
                
                if existing_road:
                    continue
                
                # 创建新道路
                road = Road(
                    project_id=project_id,
                    name=road_data.get('name', f"CAD_Road_{len(roads_data)}"),
                    road_type='mining_road',
                    design_speed=road_data.get('properties', {}).get('design_speed', 30),
                    road_width=road_data.get('width', 6.0),
                    total_length=road_data.get('length', 0),
                    created_at=datetime.utcnow()
                )
                
                db.add(road)
                db.commit()
                db.refresh(road)
                
                # 创建道路段
                coordinates = road_data.get('coordinates', [])
                if len(coordinates) >= 2:
                    segment = RoadSegment(
                        road_id=road.id,
                        segment_name=f"{road.name}_Segment_1",
                        start_chainage=0,
                        end_chainage=road_data.get('length', 0),
                        length=road_data.get('length', 0),
                        start_point=coordinates[0],
                        end_point=coordinates[-1],
                        gradient=road_data.get('properties', {}).get('gradient', 0)
                    )
                    
                    db.add(segment)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"从CAD数据创建道路失败: {str(e)}")
    
    def get_project_cad_files(self, 
                            db: Session,
                            project_id: int,
                            skip: int = 0,
                            limit: int = 100) -> List[CADFile]:
        """获取项目CAD文件列表"""
        return db.query(CADFile)\
                 .filter(CADFile.project_id == project_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_cad_file_detail(self, db: Session, file_id: int) -> Optional[CADFile]:
        """获取CAD文件详情"""
        return db.query(CADFile).filter(CADFile.id == file_id).first()
    
    def get_supported_formats(self) -> List[str]:
        """获取支持的CAD格式"""
        return self.dwg_processor.get_supported_formats()
    
    def get_drawing_templates(self) -> List[Dict]:
        """获取可用的图纸模板"""
        templates = self.drawing_generator.get_standard_templates()
        
        return [
            {
                'name': template.name,
                'paper_size': template.paper_size,
                'scale': template.scale,
                'units': template.units,
                'title_block': template.title_block
            }
            for template in templates
        ]
