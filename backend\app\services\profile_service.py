"""
剖切分析服务
"""
import os
import json
from typing import Dict, List, Optional
from sqlalchemy.orm import Session
from datetime import datetime
import asyncio

from app.models.profile_analysis import ProfileAnalysis, CrossSectionData
from app.models.road import Road
from app.models.project import Project
from app.core.config import settings
from algorithms.profile_analysis.profile_analyzer import ProfileAnalyzer
from algorithms.profile_analysis.longitudinal_profile import LongitudinalProfileAnalyzer
import logging

logger = logging.getLogger(__name__)


class ProfileService:
    """剖切分析服务类"""
    
    def __init__(self):
        self.profile_analyzer = ProfileAnalyzer()
        self.longitudinal_analyzer = LongitudinalProfileAnalyzer()
    
    async def analyze_road_profile(self, 
                                 db: Session,
                                 road_id: int,
                                 analysis_params: Dict = None) -> ProfileAnalysis:
        """分析道路剖面"""
        try:
            # 获取道路信息
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            logger.info(f"开始道路剖面分析: {road.name} (ID: {road_id})")
            
            # 构建道路几何数据
            road_points, chainages = await self._build_road_geometry(db, road)
            
            # 获取地形数据
            terrain_data = await self._get_terrain_data(db, road.project_id)
            
            # 执行剖面分析
            analysis_result = await asyncio.get_event_loop().run_in_executor(
                None,
                self.profile_analyzer.analyze_road_profile,
                road_points,
                chainages,
                road.road_width,
                terrain_data
            )
            
            # 保存分析结果到数据库
            profile_analysis = await self._save_analysis_result(db, road, analysis_result)
            
            logger.info(f"道路剖面分析完成")
            return profile_analysis
            
        except Exception as e:
            logger.error(f"道路剖面分析失败: {str(e)}")
            raise
    
    async def analyze_longitudinal_profile(self, 
                                         db: Session,
                                         road_id: int) -> Dict:
        """分析纵断面"""
        try:
            # 获取道路信息
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            # 构建道路几何数据
            road_points, chainages = await self._build_road_geometry(db, road)
            
            # 执行纵断面分析
            longitudinal_analysis = await asyncio.get_event_loop().run_in_executor(
                None,
                self.longitudinal_analyzer.analyze_longitudinal_profile,
                road_points,
                chainages,
                road.design_speed
            )
            
            # 生成图表数据
            chart_data = self.longitudinal_analyzer.generate_profile_chart_data(
                longitudinal_analysis.grade_points,
                longitudinal_analysis.vertical_curves
            )
            
            # 生成报告
            report = self.longitudinal_analyzer.generate_profile_report(longitudinal_analysis)
            
            return {
                'road_id': road_id,
                'analysis': {
                    'total_length': longitudinal_analysis.total_length,
                    'max_gradient': longitudinal_analysis.max_gradient,
                    'min_gradient': longitudinal_analysis.min_gradient,
                    'average_gradient': longitudinal_analysis.average_gradient,
                    'total_rise': longitudinal_analysis.total_rise,
                    'total_fall': longitudinal_analysis.total_fall,
                    'vertical_curves_count': len(longitudinal_analysis.vertical_curves)
                },
                'grade_points': [
                    {
                        'chainage': gp.chainage,
                        'elevation': gp.elevation,
                        'gradient': gp.gradient
                    }
                    for gp in longitudinal_analysis.grade_points
                ],
                'vertical_curves': [
                    {
                        'start_chainage': vc.start_chainage,
                        'end_chainage': vc.end_chainage,
                        'length': vc.length,
                        'radius': vc.radius,
                        'grade_in': vc.grade_in,
                        'grade_out': vc.grade_out,
                        'curve_type': vc.curve_type
                    }
                    for vc in longitudinal_analysis.vertical_curves
                ],
                'chart_data': chart_data,
                'report': report
            }
            
        except Exception as e:
            logger.error(f"纵断面分析失败: {str(e)}")
            raise
    
    async def get_cross_section(self, 
                              db: Session,
                              road_id: int,
                              chainage: float) -> Dict:
        """获取指定里程的横断面"""
        try:
            # 获取道路信息
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            # 构建道路几何数据
            road_points, chainages = await self._build_road_geometry(db, road)
            
            # 找到最近的道路点
            closest_index = 0
            min_distance = float('inf')
            
            for i, ch in enumerate(chainages):
                distance = abs(ch - chainage)
                if distance < min_distance:
                    min_distance = distance
                    closest_index = i
            
            if closest_index >= len(road_points):
                raise ValueError(f"未找到里程{chainage}处的道路点")
            
            # 获取地形数据
            terrain_data = await self._get_terrain_data(db, road.project_id)
            
            # 生成横断面
            x, y, z = road_points[closest_index]
            direction = self.profile_analyzer._calculate_cross_direction(road_points, closest_index)
            
            left_points, right_points = self.profile_analyzer._generate_cross_section_points(
                x, y, z, direction, road.road_width, terrain_data
            )
            
            cut_area, fill_area = self.profile_analyzer._calculate_cross_section_areas(
                left_points, right_points, z, road.road_width
            )
            
            return {
                'road_id': road_id,
                'chainage': chainage,
                'actual_chainage': chainages[closest_index],
                'center_point': {'x': x, 'y': y, 'elevation': z},
                'road_width': road.road_width,
                'left_points': left_points,
                'right_points': right_points,
                'cut_area': cut_area,
                'fill_area': fill_area,
                'cross_section_data': {
                    'points': left_points + [(0, z)] + right_points,
                    'road_edges': [(-road.road_width/2, z), (road.road_width/2, z)],
                    'cut_fill_areas': {
                        'cut_area': cut_area,
                        'fill_area': fill_area
                    }
                }
            }
            
        except Exception as e:
            logger.error(f"获取横断面失败: {str(e)}")
            raise
    
    async def calculate_earthwork_volumes(self, 
                                        db: Session,
                                        road_id: int,
                                        start_chainage: float = None,
                                        end_chainage: float = None) -> Dict:
        """计算土方量"""
        try:
            # 获取道路信息
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            # 构建道路几何数据
            road_points, chainages = await self._build_road_geometry(db, road)
            
            # 过滤里程范围
            if start_chainage is not None or end_chainage is not None:
                filtered_points = []
                filtered_chainages = []
                
                for i, ch in enumerate(chainages):
                    if (start_chainage is None or ch >= start_chainage) and \
                       (end_chainage is None or ch <= end_chainage):
                        filtered_points.append(road_points[i])
                        filtered_chainages.append(ch)
                
                road_points = filtered_points
                chainages = filtered_chainages
            
            # 获取地形数据
            terrain_data = await self._get_terrain_data(db, road.project_id)
            
            # 执行剖面分析
            analysis_result = await asyncio.get_event_loop().run_in_executor(
                None,
                self.profile_analyzer.analyze_road_profile,
                road_points,
                chainages,
                road.road_width,
                terrain_data
            )
            
            volume_analysis = analysis_result['volume_analysis']
            
            return {
                'road_id': road_id,
                'analysis_range': {
                    'start_chainage': start_chainage or chainages[0],
                    'end_chainage': end_chainage or chainages[-1],
                    'length': (end_chainage or chainages[-1]) - (start_chainage or chainages[0])
                },
                'volumes': {
                    'total_cut_volume': volume_analysis.total_cut_volume,
                    'total_fill_volume': volume_analysis.total_fill_volume,
                    'net_volume': volume_analysis.net_volume,
                    'balance_ratio': volume_analysis.balance_ratio
                },
                'cut_sections': volume_analysis.cut_sections,
                'fill_sections': volume_analysis.fill_sections,
                'volume_distribution': volume_analysis.volume_distribution
            }
            
        except Exception as e:
            logger.error(f"计算土方量失败: {str(e)}")
            raise
    
    async def _build_road_geometry(self, db: Session, road: Road) -> Tuple[List[Tuple[float, float, float]], List[float]]:
        """构建道路几何数据"""
        try:
            from app.models.road import RoadPoint, RoadSegment
            
            points_query = db.query(RoadPoint).join(RoadSegment).filter(
                RoadSegment.road_id == road.id
            ).order_by(RoadPoint.chainage)
            
            road_points = points_query.all()
            
            if not road_points:
                # 如果没有详细点位，使用段数据
                segments = db.query(RoadSegment).filter(RoadSegment.road_id == road.id).all()
                points = []
                chainages = []
                
                for segment in segments:
                    if segment.start_point:
                        points.append(tuple(segment.start_point))
                        chainages.append(segment.start_chainage)
                    
                    if segment.end_point:
                        points.append(tuple(segment.end_point))
                        chainages.append(segment.end_chainage)
            else:
                # 使用详细点位数据
                points = [(p.x, p.y, p.z) for p in road_points]
                chainages = [p.chainage for p in road_points]
            
            return points, chainages
            
        except Exception as e:
            logger.error(f"构建道路几何数据失败: {str(e)}")
            raise
    
    async def _get_terrain_data(self, db: Session, project_id: int) -> Optional[any]:
        """获取地形数据"""
        try:
            from app.models.terrain import TerrainData
            
            terrain_list = db.query(TerrainData).filter(
                TerrainData.project_id == project_id,
                TerrainData.processing_status == 'processed'
            ).first()
            
            if terrain_list:
                # TODO: 加载实际的DEM数据
                return None  # 返回numpy数组
            
            return None
            
        except Exception as e:
            logger.error(f"获取地形数据失败: {str(e)}")
            return None
    
    async def _save_analysis_result(self, 
                                  db: Session, 
                                  road: Road, 
                                  result: Dict) -> ProfileAnalysis:
        """保存剖面分析结果到数据库"""
        try:
            longitudinal_profile = result['longitudinal_profile']
            cross_sections = result['cross_sections']
            volume_analysis = result['volume_analysis']
            
            # 创建剖面分析记录
            profile_analysis = ProfileAnalysis(
                project_id=road.project_id,
                road_id=road.id,
                analysis_type='comprehensive',
                total_length=longitudinal_profile.total_length,
                max_gradient=longitudinal_profile.max_gradient,
                min_gradient=longitudinal_profile.min_gradient,
                average_gradient=longitudinal_profile.average_gradient,
                total_cut_volume=volume_analysis.total_cut_volume,
                total_fill_volume=volume_analysis.total_fill_volume,
                net_volume=volume_analysis.net_volume,
                balance_ratio=volume_analysis.balance_ratio,
                cross_sections_count=len(cross_sections),
                analysis_data=result,
                analyzed_at=datetime.utcnow()
            )
            
            db.add(profile_analysis)
            db.commit()
            db.refresh(profile_analysis)
            
            # 保存横断面数据
            for cs in cross_sections:
                cross_section_data = CrossSectionData(
                    analysis_id=profile_analysis.id,
                    chainage=cs.chainage,
                    center_elevation=cs.center_elevation,
                    road_width=cs.road_width,
                    left_points=cs.left_points,
                    right_points=cs.right_points,
                    cut_area=cs.cut_area,
                    fill_area=cs.fill_area
                )
                
                db.add(cross_section_data)
            
            db.commit()
            
            return profile_analysis
            
        except Exception as e:
            logger.error(f"保存剖面分析结果失败: {str(e)}")
            raise
    
    def get_project_profile_analyses(self, 
                                   db: Session,
                                   project_id: int,
                                   skip: int = 0,
                                   limit: int = 100) -> List[ProfileAnalysis]:
        """获取项目剖面分析列表"""
        return db.query(ProfileAnalysis)\
                 .filter(ProfileAnalysis.project_id == project_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_profile_analysis_detail(self, db: Session, analysis_id: int) -> Optional[ProfileAnalysis]:
        """获取剖面分析详情"""
        return db.query(ProfileAnalysis).filter(ProfileAnalysis.id == analysis_id).first()
    
    def get_profile_statistics(self, db: Session, project_id: int) -> Dict:
        """获取剖面统计信息"""
        try:
            analyses = db.query(ProfileAnalysis).filter(ProfileAnalysis.project_id == project_id).all()
            
            if not analyses:
                return {
                    'total_analyses': 0,
                    'total_cut_volume': 0,
                    'total_fill_volume': 0,
                    'average_balance_ratio': 0
                }
            
            total_analyses = len(analyses)
            total_cut_volume = sum(a.total_cut_volume for a in analyses)
            total_fill_volume = sum(a.total_fill_volume for a in analyses)
            avg_balance_ratio = sum(a.balance_ratio for a in analyses) / total_analyses
            
            return {
                'total_analyses': total_analyses,
                'total_cut_volume': total_cut_volume,
                'total_fill_volume': total_fill_volume,
                'total_net_volume': total_cut_volume - total_fill_volume,
                'average_balance_ratio': avg_balance_ratio,
                'latest_analysis_date': analyses[-1].analyzed_at.isoformat() if analyses else None
            }
            
        except Exception as e:
            logger.error(f"获取剖面统计失败: {str(e)}")
            return {}
