"""
系统集成测试
"""
import pytest
import asyncio
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
import tempfile
import os
import json

from app.main import app
from app.core.database import get_db
from app.models.project import Project
from app.models.terrain import TerrainData
from app.models.road import Road, RoadSegment
from app.services.terrain_service import TerrainService
from app.services.road_service import RoadService
from app.services.conflict_service import ConflictService
from app.services.safety_service import SafetyService
from app.services.profile_service import ProfileService
from app.services.route_service import RouteService
from app.services.autocad_service import AutoCADService
from app.services.visualization_service import VisualizationService

client = TestClient(app)


class TestSystemIntegration:
    """系统集成测试类"""
    
    @pytest.fixture
    def db_session(self):
        """数据库会话fixture"""
        # 这里应该使用测试数据库
        # 简化实现，实际应该配置独立的测试数据库
        pass
    
    @pytest.fixture
    def test_project(self, db_session):
        """测试项目fixture"""
        project = Project(
            name="集成测试项目",
            description="用于系统集成测试的项目",
            location="测试地点",
            created_by="test_user"
        )
        # db_session.add(project)
        # db_session.commit()
        return project
    
    def test_complete_workflow(self, test_project):
        """测试完整工作流程"""
        """
        测试从项目创建到最终输出的完整流程：
        1. 创建项目
        2. 上传地形数据
        3. 设计道路
        4. 冲突检测
        5. 安全分析
        6. 剖面分析
        7. 路线优化
        8. CAD导出
        9. 三维可视化
        """
        
        # 1. 创建项目
        project_data = {
            "name": "集成测试项目",
            "description": "完整工作流程测试",
            "location": "测试地点"
        }
        
        response = client.post("/api/v1/projects/", json=project_data)
        assert response.status_code == 200
        project = response.json()
        project_id = project["id"]
        
        # 2. 上传地形数据（模拟）
        terrain_response = self._test_terrain_upload(project_id)
        assert terrain_response["success"]
        
        # 3. 设计道路
        road_response = self._test_road_design(project_id)
        assert road_response["success"]
        road_id = road_response["road_id"]
        
        # 4. 冲突检测
        conflict_response = self._test_conflict_detection(project_id)
        assert conflict_response["success"]
        
        # 5. 安全分析
        safety_response = self._test_safety_analysis(road_id)
        assert safety_response["success"]
        
        # 6. 剖面分析
        profile_response = self._test_profile_analysis(road_id)
        assert profile_response["success"]
        
        # 7. 路线优化
        route_response = self._test_route_optimization(project_id)
        assert route_response["success"]
        
        # 8. CAD导出
        cad_response = self._test_cad_export(project_id)
        assert cad_response["success"]
        
        # 9. 三维可视化
        viz_response = self._test_3d_visualization(project_id)
        assert viz_response["success"]
        
        print("✅ 完整工作流程测试通过")
    
    def _test_terrain_upload(self, project_id: int) -> dict:
        """测试地形数据上传"""
        try:
            # 创建模拟地形文件
            with tempfile.NamedTemporaryFile(suffix='.tif', delete=False) as temp_file:
                # 写入模拟数据
                temp_file.write(b"mock terrain data")
                temp_file_path = temp_file.name
            
            # 上传地形文件
            with open(temp_file_path, 'rb') as f:
                files = {"file": ("test_terrain.tif", f, "image/tiff")}
                response = client.post(
                    f"/api/v1/terrain/{project_id}/upload",
                    files=files
                )
            
            # 清理临时文件
            os.unlink(temp_file_path)
            
            return {"success": response.status_code == 200}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_road_design(self, project_id: int) -> dict:
        """测试道路设计"""
        try:
            road_data = {
                "name": "测试道路",
                "road_type": "mining_road",
                "design_speed": 30,
                "road_width": 6.0,
                "segments": [
                    {
                        "segment_name": "段1",
                        "start_point": [116.0, 39.0, 100.0],
                        "end_point": [116.01, 39.01, 105.0],
                        "length": 1000,
                        "gradient": 5.0
                    }
                ]
            }
            
            response = client.post(
                f"/api/v1/roads/{project_id}/create",
                json=road_data
            )
            
            if response.status_code == 200:
                road = response.json()
                return {"success": True, "road_id": road["id"]}
            else:
                return {"success": False, "error": "道路创建失败"}
                
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_conflict_detection(self, project_id: int) -> dict:
        """测试冲突检测"""
        try:
            response = client.post(f"/api/v1/conflicts/{project_id}/detect")
            return {"success": response.status_code == 200}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_safety_analysis(self, road_id: int) -> dict:
        """测试安全分析"""
        try:
            response = client.post(f"/api/v1/safety/roads/{road_id}/analyze")
            return {"success": response.status_code == 200}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_profile_analysis(self, road_id: int) -> dict:
        """测试剖面分析"""
        try:
            response = client.post(f"/api/v1/profiles/roads/{road_id}/analyze")
            return {"success": response.status_code == 200}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_route_optimization(self, project_id: int) -> dict:
        """测试路线优化"""
        try:
            optimization_params = {
                "tasks": [
                    {
                        "origin": "A",
                        "destination": "B",
                        "cargo_volume": 50,
                        "cargo_weight": 1000,
                        "priority": 3
                    }
                ],
                "vehicles": [
                    {
                        "vehicle_type": "dump_truck",
                        "capacity": 50,
                        "max_speed": 30,
                        "fuel_consumption": 25
                    }
                ],
                "objectives": ["minimize_distance", "minimize_time"]
            }
            
            response = client.post(
                f"/api/v1/routes/{project_id}/optimize",
                json=optimization_params
            )
            
            return {"success": response.status_code == 200}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_cad_export(self, project_id: int) -> dict:
        """测试CAD导出"""
        try:
            params = {
                "template_name": "道路平面图-A1",
                "include_contours": True,
                "include_annotations": True
            }
            
            response = client.post(
                f"/api/v1/autocad/{project_id}/generate-plan",
                json=params
            )
            
            return {"success": response.status_code == 200}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def _test_3d_visualization(self, project_id: int) -> dict:
        """测试三维可视化"""
        try:
            response = client.get(f"/api/v1/visualization/{project_id}/3d-data")
            return {"success": response.status_code == 200}
            
        except Exception as e:
            return {"success": False, "error": str(e)}
    
    def test_api_endpoints(self):
        """测试所有API端点"""
        """
        测试所有主要API端点的可用性和基本功能
        """
        
        endpoints = [
            # 项目管理
            {"method": "GET", "url": "/api/v1/projects/", "expected": 200},
            
            # 地形数据
            {"method": "GET", "url": "/api/v1/terrain/supported-formats", "expected": 200},
            
            # 道路设计
            {"method": "GET", "url": "/api/v1/roads/standards/design-standards", "expected": 200},
            
            # 冲突检测
            {"method": "GET", "url": "/api/v1/conflicts/detection-types", "expected": 200},
            
            # 安全分析
            {"method": "GET", "url": "/api/v1/safety/standards/safety-criteria", "expected": 200},
            
            # 剖面分析
            {"method": "GET", "url": "/api/v1/profiles/standards/analysis-standards", "expected": 200},
            
            # 路线优化
            {"method": "GET", "url": "/api/v1/routes/algorithms/available", "expected": 200},
            
            # AutoCAD集成
            {"method": "GET", "url": "/api/v1/autocad/supported-formats", "expected": 200},
            
            # 三维可视化
            {"method": "GET", "url": "/api/v1/visualization/supported-formats", "expected": 200},
        ]
        
        failed_endpoints = []
        
        for endpoint in endpoints:
            try:
                if endpoint["method"] == "GET":
                    response = client.get(endpoint["url"])
                elif endpoint["method"] == "POST":
                    response = client.post(endpoint["url"], json={})
                
                if response.status_code != endpoint["expected"]:
                    failed_endpoints.append({
                        "url": endpoint["url"],
                        "expected": endpoint["expected"],
                        "actual": response.status_code
                    })
                    
            except Exception as e:
                failed_endpoints.append({
                    "url": endpoint["url"],
                    "error": str(e)
                })
        
        if failed_endpoints:
            print("❌ 以下API端点测试失败:")
            for failed in failed_endpoints:
                print(f"  - {failed}")
            assert False, f"有 {len(failed_endpoints)} 个API端点测试失败"
        else:
            print("✅ 所有API端点测试通过")
    
    def test_data_consistency(self):
        """测试数据一致性"""
        """
        测试各模块间数据的一致性和完整性
        """
        
        # 测试项目数据一致性
        self._test_project_data_consistency()
        
        # 测试地形数据一致性
        self._test_terrain_data_consistency()
        
        # 测试道路数据一致性
        self._test_road_data_consistency()
        
        print("✅ 数据一致性测试通过")
    
    def _test_project_data_consistency(self):
        """测试项目数据一致性"""
        # 创建项目
        project_data = {"name": "一致性测试项目", "description": "测试", "location": "测试"}
        response = client.post("/api/v1/projects/", json=project_data)
        assert response.status_code == 200
        
        project = response.json()
        project_id = project["id"]
        
        # 验证项目在各模块中的一致性
        modules = [
            f"/api/v1/terrain/{project_id}/files",
            f"/api/v1/roads/{project_id}/list",
            f"/api/v1/conflicts/{project_id}/list",
            f"/api/v1/visualization/{project_id}/statistics"
        ]
        
        for module_url in modules:
            response = client.get(module_url)
            # 应该返回200或404（如果没有数据），但不应该返回500
            assert response.status_code in [200, 404]
    
    def _test_terrain_data_consistency(self):
        """测试地形数据一致性"""
        # 测试地形数据在不同模块中的使用一致性
        pass
    
    def _test_road_data_consistency(self):
        """测试道路数据一致性"""
        # 测试道路数据在不同模块中的使用一致性
        pass
    
    def test_performance(self):
        """测试系统性能"""
        """
        测试系统在不同负载下的性能表现
        """
        
        # 测试API响应时间
        self._test_api_response_time()
        
        # 测试并发处理能力
        self._test_concurrent_requests()
        
        # 测试大数据处理能力
        self._test_large_data_processing()
        
        print("✅ 性能测试通过")
    
    def _test_api_response_time(self):
        """测试API响应时间"""
        import time
        
        start_time = time.time()
        response = client.get("/api/v1/projects/")
        end_time = time.time()
        
        response_time = end_time - start_time
        assert response_time < 2.0, f"API响应时间过长: {response_time}秒"
    
    def _test_concurrent_requests(self):
        """测试并发请求"""
        import threading
        import time
        
        def make_request():
            response = client.get("/api/v1/projects/")
            return response.status_code == 200
        
        # 创建10个并发请求
        threads = []
        results = []
        
        for i in range(10):
            thread = threading.Thread(target=lambda: results.append(make_request()))
            threads.append(thread)
            thread.start()
        
        for thread in threads:
            thread.join()
        
        # 所有请求都应该成功
        assert all(results), "并发请求测试失败"
    
    def _test_large_data_processing(self):
        """测试大数据处理"""
        # 测试处理大量数据时的性能
        # 这里可以创建大量测试数据进行测试
        pass
    
    def test_error_handling(self):
        """测试错误处理"""
        """
        测试系统的错误处理和恢复能力
        """
        
        # 测试无效输入
        self._test_invalid_input_handling()
        
        # 测试资源不存在
        self._test_resource_not_found()
        
        # 测试权限错误
        self._test_permission_errors()
        
        print("✅ 错误处理测试通过")
    
    def _test_invalid_input_handling(self):
        """测试无效输入处理"""
        # 测试无效的项目数据
        invalid_project = {"name": "", "description": None}
        response = client.post("/api/v1/projects/", json=invalid_project)
        assert response.status_code == 422  # 验证错误
    
    def _test_resource_not_found(self):
        """测试资源不存在的处理"""
        # 测试不存在的项目
        response = client.get("/api/v1/projects/99999")
        assert response.status_code == 404
    
    def _test_permission_errors(self):
        """测试权限错误处理"""
        # 这里可以测试权限相关的错误处理
        pass


if __name__ == "__main__":
    # 运行集成测试
    test_integration = TestSystemIntegration()
    
    print("🚀 开始系统集成测试...")
    
    try:
        # 运行各项测试
        test_integration.test_api_endpoints()
        test_integration.test_data_consistency()
        test_integration.test_performance()
        test_integration.test_error_handling()
        
        print("\n🎉 所有集成测试通过！")
        
    except Exception as e:
        print(f"\n❌ 集成测试失败: {str(e)}")
        raise
