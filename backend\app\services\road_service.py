"""
道路设计服务
"""
import os
import json
from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from datetime import datetime
import asyncio

from app.models.road import Road, RoadSegment, RoadPoint
from app.models.project import Project
from app.models.design_standard import DesignStandard
from app.models.terrain import TerrainData
from app.core.config import settings
from algorithms.road_design.road_designer import RoadDesigner, Point3D, DesignStandards
from algorithms.road_design.geometric_calculator import GeometricCalculator
from algorithms.road_design.earthwork_calculator import EarthworkCalculator, EarthworkSection
import logging

logger = logging.getLogger(__name__)


class RoadService:
    """道路设计服务类"""
    
    def __init__(self):
        self.designer = RoadDesigner()
        self.geometric_calc = GeometricCalculator()
        self.earthwork_calc = EarthworkCalculator()
    
    async def design_road(self, 
                         db: Session,
                         project_id: int,
                         design_params: Dict) -> Road:
        """设计道路"""
        try:
            # 验证项目存在
            project = db.query(Project).filter(Project.id == project_id).first()
            if not project:
                raise ValueError(f"项目不存在: {project_id}")
            
            # 获取设计标准
            design_standard = None
            if design_params.get('design_standard_id'):
                design_standard = db.query(DesignStandard).filter(
                    DesignStandard.id == design_params['design_standard_id']
                ).first()
            
            # 设置设计标准
            if design_standard:
                standards = DesignStandards(
                    min_road_width=design_standard.min_road_width,
                    max_gradient=design_standard.max_gradient,
                    min_turning_radius=design_standard.min_turning_radius,
                    min_sight_distance=design_standard.min_sight_distance,
                    design_speed=design_params.get('design_speed', 30.0)
                )
            else:
                standards = DesignStandards()
            
            self.designer.standards = standards
            
            # 解析起终点
            start_point = Point3D(
                x=design_params['start_point']['x'],
                y=design_params['start_point']['y'],
                z=design_params['start_point'].get('z', 0)
            )
            
            end_point = Point3D(
                x=design_params['end_point']['x'],
                y=design_params['end_point']['y'],
                z=design_params['end_point'].get('z', 0)
            )
            
            # 解析控制点
            control_points = []
            if design_params.get('control_points'):
                for cp in design_params['control_points']:
                    control_points.append(Point3D(
                        x=cp['x'],
                        y=cp['y'],
                        z=cp.get('z', 0)
                    ))
            
            # 获取地形数据
            terrain_data = await self._get_terrain_data(db, project_id)
            
            # 执行道路设计
            design_result = await asyncio.get_event_loop().run_in_executor(
                None,
                self.designer.design_road_alignment,
                start_point,
                end_point,
                control_points,
                terrain_data
            )
            
            if design_result['status'] != 'success':
                raise ValueError(f"道路设计失败: {design_result.get('error_message')}")
            
            # 创建道路记录
            road = Road(
                project_id=project_id,
                name=design_params.get('name', f"道路_{datetime.now().strftime('%Y%m%d_%H%M%S')}"),
                description=design_params.get('description'),
                road_type=design_params.get('road_type', 'haul_road'),
                design_speed=design_params.get('design_speed', 30.0),
                design_load=design_params.get('design_load', 100.0),
                road_width=design_params.get('road_width', standards.min_road_width),
                total_length=design_result['alignment']['total_length'],
                max_gradient=design_result['alignment']['max_gradient'],
                min_turning_radius=design_result['alignment']['min_radius'],
                start_elevation=start_point.z,
                end_elevation=end_point.z,
                design_status='designed',
                design_standard_id=design_params.get('design_standard_id'),
                geometry=design_result['geometry'],
                alignment_data=design_result['alignment'],
                designed_at=datetime.utcnow()
            )
            
            db.add(road)
            db.commit()
            db.refresh(road)
            
            # 创建道路段
            await self._create_road_segments(db, road, design_result['alignment']['segments'])
            
            # 创建详细点位
            await self._create_road_points(db, road, design_result['detailed_points'])
            
            # 计算土方量
            await self._calculate_earthwork(db, road)
            
            logger.info(f"道路设计完成: {road.id}")
            return road
            
        except Exception as e:
            logger.error(f"道路设计失败: {str(e)}")
            raise
    
    async def _get_terrain_data(self, db: Session, project_id: int) -> Optional[any]:
        """获取地形数据"""
        try:
            terrain_list = db.query(TerrainData).filter(
                TerrainData.project_id == project_id,
                TerrainData.processing_status == 'processed'
            ).all()
            
            if not terrain_list:
                return None
            
            # 返回第一个处理完成的地形数据
            # TODO: 实际应该加载DEM数据
            return None
            
        except Exception as e:
            logger.error(f"获取地形数据失败: {str(e)}")
            return None
    
    async def _create_road_segments(self, 
                                  db: Session, 
                                  road: Road, 
                                  segments_data: List[Dict]):
        """创建道路段"""
        try:
            chainage = 0.0
            
            for i, seg_data in enumerate(segments_data):
                segment = RoadSegment(
                    road_id=road.id,
                    segment_index=i,
                    segment_type=seg_data['segment_type'],
                    start_chainage=chainage,
                    end_chainage=chainage + seg_data['length'],
                    length=seg_data['length'],
                    start_point=[
                        seg_data['start_point']['x'],
                        seg_data['start_point']['y'],
                        seg_data['start_point']['z']
                    ],
                    end_point=[
                        seg_data['end_point']['x'],
                        seg_data['end_point']['y'],
                        seg_data['end_point']['z']
                    ],
                    radius=seg_data.get('radius'),
                    gradient=seg_data.get('gradient')
                )
                
                db.add(segment)
                chainage += seg_data['length']
            
            db.commit()
            
        except Exception as e:
            logger.error(f"创建道路段失败: {str(e)}")
            raise
    
    async def _create_road_points(self, 
                                db: Session, 
                                road: Road, 
                                points_data: List[Dict]):
        """创建道路点位"""
        try:
            # 获取道路段
            segments = db.query(RoadSegment).filter(RoadSegment.road_id == road.id).all()
            segment_dict = {seg.segment_index: seg for seg in segments}
            
            for i, point_data in enumerate(points_data):
                # 确定点属于哪个段
                segment_id = None
                for seg in segments:
                    if seg.start_chainage <= point_data['chainage'] <= seg.end_chainage:
                        segment_id = seg.id
                        break
                
                if segment_id:
                    point = RoadPoint(
                        segment_id=segment_id,
                        point_index=i,
                        chainage=point_data['chainage'],
                        x=point_data['x'],
                        y=point_data['y'],
                        z=point_data['z'],
                        left_width=road.road_width / 2,
                        right_width=road.road_width / 2,
                        cross_slope=2.0  # 默认2%横坡
                    )
                    
                    db.add(point)
            
            db.commit()
            
        except Exception as e:
            logger.error(f"创建道路点位失败: {str(e)}")
            raise
    
    async def _calculate_earthwork(self, db: Session, road: Road):
        """计算土方量"""
        try:
            # 获取道路点位
            segments = db.query(RoadSegment).filter(RoadSegment.road_id == road.id).all()
            
            # 模拟横断面数据
            sections = []
            for segment in segments:
                # 简化计算，实际应该基于地形数据
                section = EarthworkSection(
                    chainage=segment.start_chainage,
                    cut_area=10.0,  # 模拟数据
                    fill_area=5.0,   # 模拟数据
                    center_height=segment.start_point[2] if segment.start_point else 0,
                    road_width=road.road_width
                )
                sections.append(section)
            
            if len(sections) >= 2:
                # 计算土方量
                earthwork_summary = self.earthwork_calc.calculate_earthwork_volume(sections)
                
                # 更新道路记录
                road.earthwork_cut = earthwork_summary.total_cut_volume
                road.earthwork_fill = earthwork_summary.total_fill_volume
                
                db.commit()
            
        except Exception as e:
            logger.error(f"计算土方量失败: {str(e)}")
    
    def get_road_list(self, 
                     db: Session,
                     project_id: int,
                     skip: int = 0,
                     limit: int = 100) -> List[Road]:
        """获取道路列表"""
        return db.query(Road)\
                 .filter(Road.project_id == project_id)\
                 .offset(skip)\
                 .limit(limit)\
                 .all()
    
    def get_road_detail(self, db: Session, road_id: int) -> Optional[Road]:
        """获取道路详情"""
        return db.query(Road).filter(Road.id == road_id).first()
    
    def get_road_profile(self, 
                        db: Session,
                        road_id: int,
                        start_chainage: float = 0,
                        end_chainage: float = None) -> Dict:
        """获取道路纵断面"""
        try:
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            # 获取道路点位
            query = db.query(RoadPoint).join(RoadSegment).filter(
                RoadSegment.road_id == road_id,
                RoadPoint.chainage >= start_chainage
            )
            
            if end_chainage:
                query = query.filter(RoadPoint.chainage <= end_chainage)
            
            points = query.order_by(RoadPoint.chainage).all()
            
            profile_data = []
            for point in points:
                profile_data.append({
                    'chainage': point.chainage,
                    'elevation': point.z,
                    'gradient': 0.0,  # TODO: 计算坡度
                    'curvature': 0.0  # TODO: 计算曲率
                })
            
            return {
                'road_id': road_id,
                'start_chainage': start_chainage,
                'end_chainage': end_chainage or road.total_length,
                'profile_data': profile_data
            }
            
        except Exception as e:
            logger.error(f"获取道路纵断面失败: {str(e)}")
            return {}
    
    def get_road_cross_section(self, 
                              db: Session,
                              road_id: int,
                              chainage: float) -> Dict:
        """获取道路横断面"""
        try:
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            # 查找最近的道路点
            point = db.query(RoadPoint).join(RoadSegment).filter(
                RoadSegment.road_id == road_id
            ).order_by(
                db.func.abs(RoadPoint.chainage - chainage)
            ).first()
            
            if not point:
                raise ValueError(f"未找到里程{chainage}处的道路点")
            
            # 生成横断面数据
            cross_section_data = {
                'chainage': chainage,
                'center_point': {
                    'x': point.x,
                    'y': point.y,
                    'elevation': point.z
                },
                'road_width': road.road_width,
                'left_width': point.left_width,
                'right_width': point.right_width,
                'cross_slope': point.cross_slope,
                'cut_area': 0.0,  # TODO: 从地形数据计算
                'fill_area': 0.0  # TODO: 从地形数据计算
            }
            
            return cross_section_data
            
        except Exception as e:
            logger.error(f"获取道路横断面失败: {str(e)}")
            return {}
    
    def optimize_road_alignment(self, 
                               db: Session,
                               road_id: int,
                               optimization_params: Dict) -> Dict:
        """优化道路线形"""
        try:
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                raise ValueError(f"道路不存在: {road_id}")
            
            # TODO: 实现线形优化算法
            # 这里返回模拟结果
            
            optimization_result = {
                'status': 'success',
                'original_length': road.total_length,
                'optimized_length': road.total_length * 0.95,  # 模拟优化5%
                'cost_reduction': 0.1,  # 模拟成本降低10%
                'improvements': [
                    '减少曲线段数量',
                    '优化坡度分布',
                    '改善土方平衡'
                ]
            }
            
            return optimization_result
            
        except Exception as e:
            logger.error(f"优化道路线形失败: {str(e)}")
            return {'status': 'error', 'error_message': str(e)}
    
    def delete_road(self, db: Session, road_id: int) -> bool:
        """删除道路"""
        try:
            road = db.query(Road).filter(Road.id == road_id).first()
            if not road:
                return False
            
            # 删除关联的段和点位(通过级联删除)
            db.delete(road)
            db.commit()
            
            logger.info(f"道路删除成功: {road_id}")
            return True
            
        except Exception as e:
            logger.error(f"道路删除失败: {str(e)}")
            return False
