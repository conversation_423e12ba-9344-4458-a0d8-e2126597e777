# 露天矿山道路设计软件 - 部署指南

## 概述

本文档提供了露天矿山道路设计软件的完整部署指南，包括开发环境、测试环境和生产环境的部署方法。

## 系统要求

### 最低配置
- **CPU**: 4核心
- **内存**: 8GB RAM
- **存储**: 100GB 可用空间
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+
- **Docker**: 20.10+
- **Docker Compose**: 2.0+

### 推荐配置
- **CPU**: 8核心
- **内存**: 16GB RAM
- **存储**: 500GB SSD
- **网络**: 1Gbps
- **备份存储**: 额外500GB

## 快速开始

### 1. 克隆项目

```bash
git clone https://github.com/your-org/mining-road-design.git
cd mining-road-design
```

### 2. 配置环境变量

```bash
# 复制环境变量模板
cp .env.production .env

# 编辑环境变量
nano .env
```

### 3. 运行部署脚本

```bash
# 给脚本执行权限
chmod +x scripts/deploy.sh

# 运行部署
./scripts/deploy.sh
```

## 详细部署步骤

### 1. 环境准备

#### 安装Docker和Docker Compose

```bash
# Ubuntu/Debian
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh
sudo usermod -aG docker $USER

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.12.2/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### 配置防火墙

```bash
# Ubuntu UFW
sudo ufw enable
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# CentOS/RHEL Firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. 配置环境变量

编辑 `.env` 文件，设置以下关键配置：

```bash
# 域名配置
DOMAIN=your-domain.com
SSL_EMAIL=<EMAIL>

# 数据库密码
POSTGRES_PASSWORD=your-strong-password

# 应用密钥
SECRET_KEY=your-very-long-secret-key

# 监控配置
GRAFANA_USER=admin
GRAFANA_PASSWORD=your-grafana-password
```

### 3. SSL证书配置

#### 使用Let's Encrypt (推荐)

```bash
# 自动配置SSL证书
docker-compose -f docker-compose.prod.yml run --rm certbot
```

#### 使用自定义证书

```bash
# 将证书文件放置到ssl目录
mkdir -p ssl
cp your-cert.pem ssl/fullchain.pem
cp your-key.pem ssl/privkey.pem
```

### 4. 启动服务

```bash
# 构建并启动所有服务
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

### 5. 初始化数据库

```bash
# 运行数据库迁移
docker-compose -f docker-compose.prod.yml exec app python -m alembic upgrade head

# 创建管理员用户
docker-compose -f docker-compose.prod.yml exec app python scripts/create_admin.py
```

## 服务配置

### Nginx配置

主要配置文件：`nginx/nginx.prod.conf`

- 反向代理和负载均衡
- SSL终止
- 静态文件服务
- 安全头设置
- 限流配置

### 数据库配置

- PostgreSQL 15 with PostGIS
- 自动备份
- 连接池优化
- 性能调优

### 监控配置

#### Prometheus指标收集

- 系统指标
- 应用指标
- 数据库指标
- 自定义业务指标

#### Grafana仪表板

- 系统监控面板
- 应用性能面板
- 业务指标面板
- 告警配置

## 备份和恢复

### 自动备份

系统每天自动备份数据库和重要文件：

```bash
# 查看备份状态
docker-compose -f docker-compose.prod.yml logs backup

# 手动触发备份
docker-compose -f docker-compose.prod.yml exec backup /backup.sh
```

### 数据恢复

```bash
# 从备份恢复数据库
docker-compose -f docker-compose.prod.yml exec postgres psql -U postgres -d mining_road_design < /backups/backup_file.sql
```

## 维护操作

### 更新应用

```bash
# 拉取最新代码
git pull origin main

# 重新构建并部署
docker-compose -f docker-compose.prod.yml build --no-cache
docker-compose -f docker-compose.prod.yml up -d
```

### 扩容服务

```bash
# 扩展应用实例
docker-compose -f docker-compose.prod.yml up -d --scale app=3
```

### 日志管理

```bash
# 查看应用日志
docker-compose -f docker-compose.prod.yml logs app

# 查看Nginx日志
docker-compose -f docker-compose.prod.yml logs nginx

# 清理日志
docker system prune -f
```

## 性能优化

### 系统级优化

```bash
# 调整内核参数
echo 'vm.swappiness=10' >> /etc/sysctl.conf
echo 'net.core.somaxconn=65535' >> /etc/sysctl.conf
sysctl -p
```

### 应用级优化

- 启用Redis缓存
- 数据库连接池优化
- 静态文件CDN
- 图片压缩和优化

### 数据库优化

```sql
-- 创建索引
CREATE INDEX CONCURRENTLY idx_projects_created_at ON projects(created_at);
CREATE INDEX CONCURRENTLY idx_roads_project_id ON roads(project_id);

-- 分析表统计信息
ANALYZE;
```

## 安全配置

### 网络安全

- 防火墙配置
- SSL/TLS加密
- 安全头设置
- 访问控制

### 应用安全

- 强密码策略
- JWT令牌管理
- 输入验证
- SQL注入防护

### 数据安全

- 数据库加密
- 备份加密
- 敏感数据脱敏
- 访问日志审计

## 故障排除

### 常见问题

#### 服务启动失败

```bash
# 检查服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看错误日志
docker-compose -f docker-compose.prod.yml logs [service_name]

# 重启服务
docker-compose -f docker-compose.prod.yml restart [service_name]
```

#### 数据库连接问题

```bash
# 检查数据库状态
docker-compose -f docker-compose.prod.yml exec postgres pg_isready

# 查看数据库日志
docker-compose -f docker-compose.prod.yml logs postgres
```

#### SSL证书问题

```bash
# 检查证书有效期
openssl x509 -in ssl/fullchain.pem -text -noout

# 更新证书
docker-compose -f docker-compose.prod.yml run --rm certbot renew
```

### 性能问题诊断

```bash
# 查看系统资源使用
docker stats

# 查看应用性能指标
curl http://localhost:9090/metrics

# 数据库性能分析
docker-compose -f docker-compose.prod.yml exec postgres psql -U postgres -c "SELECT * FROM pg_stat_activity;"
```

## 监控和告警

### 系统监控

- CPU、内存、磁盘使用率
- 网络流量
- 服务可用性
- 响应时间

### 应用监控

- API请求量和错误率
- 数据库查询性能
- 文件上传统计
- 用户活跃度

### 告警配置

- 系统资源告警
- 服务异常告警
- 性能阈值告警
- 安全事件告警

## 联系支持

如果在部署过程中遇到问题，请联系技术支持：

- 邮箱：<EMAIL>
- 电话：400-xxx-xxxx
- 文档：https://docs.your-domain.com

## 版本历史

- v1.0.0 - 初始版本
- v1.1.0 - 添加三维可视化
- v1.2.0 - 添加AutoCAD集成
- v1.3.0 - 添加路线优化
